#!/bin/bash

# new-track - Create a new Git worktree for parallel development
# Usage: ./bin/new-track <track-name> [base-branch]

set -euo pipefail

# Configuration - paths relative to current repo
WORKTREE_DIR="../wt"
CURRENT_DIR="$(pwd)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Usage: $0 <track-name> [base-branch]"
    echo ""
    echo "Creates a new Git worktree for parallel development."
    echo ""
    echo "Arguments:"
    echo "  track-name    Name for the track (e.g., 'auth', 'inbox-e2e')"
    echo "  base-branch   Base branch to branch from (default: 'origin/main')"
    echo ""
    echo "Examples:"
    echo "  $0 auth"
    echo "  $0 inbox-e2e"
    echo "  $0 bank-integration main"
    echo ""
    echo "The script will:"
    echo "  1. Create a new worktree in ../wt/track-<name>"
    echo "  2. Create and switch to branch feat/track-<name>"
    echo "  3. Push the branch and set upstream"
    echo "  4. Install dependencies (pnpm, uv if applicable)"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [[ $# -lt 1 ]] || [[ $# -gt 2 ]]; then
    log_error "Invalid number of arguments"
    print_usage
    exit 1
fi

TRACK_NAME="$1"
BASE_BRANCH="${2:-origin/main}"
BRANCH_NAME="feat/track-${TRACK_NAME}"
WORKTREE_PATH="${WORKTREE_DIR}/track-${TRACK_NAME}"

# Validate track name
if [[ ! "$TRACK_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
    log_error "Track name can only contain letters, numbers, hyphens, and underscores"
    exit 1
fi

# Check if we're in a git repo
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_error "Not in a git repository"
    exit 1
fi

# Check if worktree already exists
if [[ -d "$WORKTREE_PATH" ]]; then
    log_error "Worktree already exists at $WORKTREE_PATH"
    exit 1
fi

log_info "Creating new worktree for track: $TRACK_NAME"
log_info "Base branch: $BASE_BRANCH"
log_info "Target branch: $BRANCH_NAME"
log_info "Worktree path: $WORKTREE_PATH"

# Create the worktree
log_info "Fetching latest changes..."
git fetch origin

log_info "Creating worktree..."
if ! git worktree add "$WORKTREE_PATH" -b "$BRANCH_NAME" "$BASE_BRANCH"; then
    log_error "Failed to create worktree"
    exit 1
fi

log_success "Worktree created at $WORKTREE_PATH"

# Set up the worktree
cd "$WORKTREE_PATH"

# Push branch and set upstream
log_info "Pushing branch and setting upstream..."
if git push -u origin "$BRANCH_NAME"; then
    log_success "Branch pushed and upstream set"
else
    log_warning "Failed to push branch (you may need to push manually later)"
fi

# Install dependencies if package files exist
log_info "Installing dependencies..."

if [[ -f "package.json" ]]; then
    log_info "Installing Node.js dependencies with pnpm..."
    if command -v pnpm &> /dev/null; then
        pnpm install
        log_success "pnpm dependencies installed"
    else
        log_warning "pnpm not found, skipping Node.js dependencies"
    fi
fi

if [[ -f "pyproject.toml" ]] && command -v uv &> /dev/null; then
    log_info "Setting up Python virtual environment..."
    uv venv
    uv sync
    log_success "Python environment set up"
fi

# Copy environment file if it exists
if [[ -f ".env.example" ]] && [[ ! -f ".env.local" ]]; then
    log_info "Creating .env.local from .env.example..."
    cp .env.example .env.local
    log_success ".env.local created"
    log_warning "Remember to adjust ports if running multiple tracks simultaneously"
fi

# Return to original directory
cd "$CURRENT_DIR"

echo ""
log_success "Track '$TRACK_NAME' is ready!"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "  1. cd $WORKTREE_PATH"
echo "  2. Edit .env.local if needed (adjust ports for parallel running)"
echo "  3. Start development:"
echo "     supabase start"
echo "     pnpm -w dev"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "  List all worktrees: git worktree list"
echo "  Remove this worktree: ./bin/cleanup-track $TRACK_NAME"
echo "  Sync all worktrees: ./bin/sync-all"