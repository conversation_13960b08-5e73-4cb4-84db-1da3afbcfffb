#!/bin/bash

# sync-all - Sync all worktrees with latest main
# Usage: ./bin/sync-all

set -euo pipefail

# Configuration
WORKTREE_DIR="../wt"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in a git repo
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_error "Not in a git repository"
    exit 1
fi

log_info "Syncing all worktrees with latest main..."

# Update current repo (main worktree) first
log_info "Updating main worktree..."
git fetch origin
current_branch=$(git rev-parse --abbrev-ref HEAD)

if [[ "$current_branch" == "main" ]]; then
    git pull origin main
    log_success "Main worktree updated"
else
    log_info "Currently on branch '$current_branch' - not pulling main"
fi

# Find and update all worktrees
if [[ -d "$WORKTREE_DIR" ]]; then
    for worktree in "$WORKTREE_DIR"/*; do
        if [[ -d "$worktree" ]]; then
            worktree_name=$(basename "$worktree")
            log_info "Syncing $worktree_name..."
            
            cd "$worktree"
            git fetch origin
            
            # Get current branch
            current_branch=$(git rev-parse --abbrev-ref HEAD)
            
            # Try to rebase on main
            if git rebase origin/main; then
                log_success "$worktree_name synced successfully"
            else
                log_warning "$worktree_name has conflicts - please resolve manually"
                log_info "To resolve conflicts:"
                log_info "  cd $worktree"
                log_info "  # Fix conflicts in files"
                log_info "  git add ."
                log_info "  git rebase --continue"
            fi
            
            # Return to original directory
            cd - > /dev/null
        fi
    done
else
    log_info "No worktrees found in $WORKTREE_DIR"
fi

log_success "Sync complete!"
echo ""
log_info "Run 'git worktree list' to see all worktrees"