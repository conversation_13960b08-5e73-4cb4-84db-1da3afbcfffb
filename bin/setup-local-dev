#!/bin/bash

# setup-local-dev - One-command setup for new contributors
# Usage: ./bin/setup-local-dev

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    BelBooks Local Dev Setup                 ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  This script sets up everything you need for local          ║${NC}"
    echo -e "${BLUE}║  development with Supabase, database migrations, and types. ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check for required tools
    local missing_tools=()
    
    if ! command -v pnpm &> /dev/null; then
        missing_tools+=("pnpm")
    fi
    
    if ! command -v supabase &> /dev/null; then
        missing_tools+=("supabase")
    fi
    
    if ! command -v uv &> /dev/null; then
        log_warning "uv not found - Python workers may not work"
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        echo ""
        echo "Please install missing tools:"
        for tool in "${missing_tools[@]}"; do
            case $tool in
                "pnpm")
                    echo "  pnpm: npm install -g pnpm"
                    ;;
                "supabase")
                    echo "  supabase: brew install supabase/tap/supabase"
                    ;;
                "docker")
                    echo "  docker: https://docs.docker.com/desktop/"
                    ;;
            esac
        done
        exit 1
    fi
    
    log_success "All prerequisites found"
}

setup_environment() {
    log_info "Setting up environment variables..."
    
    if [[ ! -f ".env.local" ]]; then
        cp .env.example .env.local
        log_success ".env.local created from .env.example"
    else
        log_info ".env.local already exists - not overwriting"
    fi
}

install_dependencies() {
    log_info "Installing dependencies..."
    
    # Install Node.js dependencies
    log_info "Installing Node.js dependencies with pnpm..."
    if pnpm install; then
        log_success "Node.js dependencies installed"
    else
        log_error "Failed to install Node.js dependencies"
        exit 1
    fi
    
    # Set up Python environment if uv is available
    if command -v uv &> /dev/null; then
        log_info "Setting up Python virtual environment..."
        if uv venv && uv sync; then
            log_success "Python environment set up"
        else
            log_warning "Python environment setup failed - continuing anyway"
        fi
    fi
}

start_supabase() {
    log_info "Starting Supabase..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    
    # Start Supabase
    if supabase start; then
        log_success "Supabase started successfully"
        
        # Show connection details
        echo ""
        log_info "Supabase services are running:"
        echo "  • API: http://127.0.0.1:54321"
        echo "  • Studio: http://127.0.0.1:54323"
        echo "  • Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
        echo "  • Email (Inbucket): http://127.0.0.1:54324"
        
    else
        log_error "Failed to start Supabase"
        exit 1
    fi
}

generate_types() {
    log_info "Generating database types..."
    
    if pnpm db:types; then
        log_success "Database types generated"
    else
        log_warning "Failed to generate types - you may need to run migrations first"
    fi
}

apply_migrations() {
    log_info "Checking for database migrations..."
    
    if [[ -d "packages/db/migrations" ]] && [[ -n "$(ls -A packages/db/migrations 2>/dev/null)" ]]; then
        log_info "Applying database migrations..."
        
        if [[ -f "packages/db/migrations/all.sql" ]]; then
            if psql "$DATABASE_URL" -f packages/db/migrations/all.sql; then
                log_success "Migrations applied successfully"
            else
                log_warning "Some migrations may have failed - check output above"
            fi
        else
            log_info "No migrations/all.sql found - skipping migration step"
        fi
    else
        log_info "No migrations found - this is normal for new projects"
    fi
}

run_tests() {
    log_info "Running basic health checks..."
    
    # Test database connection
    if psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "Database connection works"
    else
        log_warning "Database connection failed - check Supabase status"
    fi
    
    # Test API endpoint
    if curl -s http://127.0.0.1:54321/rest/v1/ >/dev/null 2>&1; then
        log_success "Supabase API is responding"
    else
        log_warning "Supabase API not responding"
    fi
}

print_next_steps() {
    echo ""
    echo -e "${GREEN}🎉 Local development environment is ready!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "  1. Start the development servers:"
    echo "     pnpm dev"
    echo ""
    echo "  2. Visit your local application:"
    echo "     • Web: http://localhost:3000"
    echo "     • BFF API: http://localhost:4000" 
    echo "     • Supabase Studio: http://127.0.0.1:54323"
    echo ""
    echo -e "${BLUE}Useful commands:${NC}"
    echo "  pnpm dev              # Start all development servers"
    echo "  pnpm db:types         # Regenerate database types"
    echo "  supabase status       # Check Supabase services"
    echo "  supabase stop         # Stop Supabase services"
    echo ""
    echo -e "${BLUE}Documentation:${NC}"
    echo "  docs/env-profiles.md  # Environment setup guide"
    echo "  docs/run-locally.md   # Detailed local development guide"
    echo ""
    echo -e "${YELLOW}⚠️  Important:${NC}"
    echo "  • Only run one Supabase instance at a time across all worktrees"
    echo "  • If you see port conflicts, edit .env.local to use different ports"
    echo "  • Never commit real keys to Git - they're in .env.local (ignored)"
    echo ""
}

# Main execution
main() {
    print_header
    check_prerequisites
    setup_environment
    install_dependencies
    start_supabase
    apply_migrations
    generate_types
    run_tests
    print_next_steps
}

# Run main function
main "$@"