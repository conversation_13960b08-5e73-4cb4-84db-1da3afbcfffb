#!/bin/bash

# cleanup-track - Remove a completed worktree and its branch
# Usage: ./bin/cleanup-track <track-name>

set -euo pipefail

# Configuration
WORKTREE_DIR="../wt"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Usage: $0 <track-name>"
    echo ""
    echo "Removes a completed worktree and its branch."
    echo ""
    echo "Arguments:"
    echo "  track-name    Name of the track to clean up (e.g., 'auth', 'inbox-e2e')"
    echo ""
    echo "Examples:"
    echo "  $0 auth"
    echo "  $0 inbox-e2e"
    echo ""
    echo "The script will:"
    echo "  1. Remove the worktree directory"
    echo "  2. Delete the local branch"
    echo "  3. Delete the remote branch"
    echo "  4. Prune stale worktree references"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [[ $# -ne 1 ]]; then
    log_error "Invalid number of arguments"
    print_usage
    exit 1
fi

TRACK_NAME="$1"
BRANCH_NAME="feat/track-${TRACK_NAME}"
WORKTREE_PATH="${WORKTREE_DIR}/track-${TRACK_NAME}"

# Check if we're in a git repo
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_error "Not in a git repository"
    exit 1
fi

# Check if worktree exists
if [[ ! -d "$WORKTREE_PATH" ]]; then
    log_error "Worktree not found at $WORKTREE_PATH"
    exit 1
fi

log_info "Cleaning up track: $TRACK_NAME"
log_info "Branch: $BRANCH_NAME"
log_info "Worktree path: $WORKTREE_PATH"

# Confirm deletion
echo ""
log_warning "This will permanently delete the worktree and branch."
read -p "Are you sure? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Cleanup cancelled"
    exit 0
fi

# Remove the worktree
log_info "Removing worktree..."
if git worktree remove "$WORKTREE_PATH"; then
    log_success "Worktree removed"
else
    log_error "Failed to remove worktree"
    exit 1
fi

# Delete local branch
log_info "Deleting local branch..."
if git branch -D "$BRANCH_NAME"; then
    log_success "Local branch deleted"
else
    log_warning "Local branch may not exist or failed to delete"
fi

# Delete remote branch
log_info "Deleting remote branch..."
if git push origin ":$BRANCH_NAME"; then
    log_success "Remote branch deleted"
else
    log_warning "Remote branch may not exist or failed to delete"
fi

# Prune stale references
log_info "Pruning stale references..."
git worktree prune
git fetch --prune

log_success "Track '$TRACK_NAME' cleaned up successfully!"