#!/bin/bash

# Apply Database Migrations to Production
# This script applies all new migrations to the production environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️ Applying Database Migrations to Production${NC}"
echo "=================================================="

# Check if PROD_DATABASE_URL is set
if [ -z "${PROD_DATABASE_URL:-}" ]; then
    echo -e "${RED}❌ PROD_DATABASE_URL environment variable not set${NC}"
    echo ""
    echo "Please set the production database URL:"
    echo "export PROD_DATABASE_URL=\"postgresql://postgres:[PASSWORD]@db.ulhlnhwwxiolyskxulmm.supabase.co:5432/postgres\""
    echo ""
    echo "Get the password from Supabase Dashboard → Settings → Database"
    exit 1
fi

echo -e "${GREEN}✅ Database URL configured${NC}"

# Test database connection
echo -e "${YELLOW}🔌 Testing database connection...${NC}"
if ! psql "$PROD_DATABASE_URL" -c "SELECT version();" > /dev/null 2>&1; then
    echo -e "${RED}❌ Cannot connect to production database${NC}"
    echo "Please check your database URL and credentials"
    exit 1
fi

echo -e "${GREEN}✅ Database connection successful${NC}"

# Confirmation prompt for production
echo -e "${YELLOW}⚠️  WARNING: You are about to apply migrations to PRODUCTION${NC}"
echo -e "${YELLOW}This will modify the production database schema.${NC}"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo -e "${YELLOW}❌ Migration cancelled by user${NC}"
    exit 0
fi

# Apply migrations
echo -e "${YELLOW}📊 Applying migrations to production...${NC}"
psql "$PROD_DATABASE_URL" -f packages/db/migrations/all.sql

echo -e "${GREEN}✅ All migrations applied successfully${NC}"

# Verify migrations
echo -e "${YELLOW}🔍 Verifying migrations...${NC}"

# Check for authentication tables
AUTH_TABLES=$(psql "$PROD_DATABASE_URL" -t -c "
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('pending_invites', 'security_events');
" | wc -l)

if [ "$AUTH_TABLES" -eq 2 ]; then
    echo -e "${GREEN}✅ Authentication tables created successfully${NC}"
else
    echo -e "${RED}❌ Expected 2 authentication tables, found $AUTH_TABLES${NC}"
fi

# Check for RPC functions
RPC_FUNCTIONS=$(psql "$PROD_DATABASE_URL" -t -c "
SELECT count(*) FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE 'rpc_%';
")

echo -e "${GREEN}✅ Found $RPC_FUNCTIONS RPC functions${NC}"

echo -e "${BLUE}🎉 Production migration verification complete!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Generate types: pnpm db:types:prod"
echo "2. Run production smoke tests"
echo "3. Monitor security events"
echo "4. Verify authentication flows"
