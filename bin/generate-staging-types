#!/bin/bash

# Generate TypeScript types from staging database
set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Generating Types from Staging Database${NC}"

# Check if supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first."
    exit 1
fi

echo -e "${YELLOW}📝 Generating types from staging project...${NC}"

# Generate types from staging project
supabase gen types typescript --db-url "postgresql://postgres:<EMAIL>:5432/postgres" > packages/types/src/supabase.ts

echo -e "${GREEN}✅ Types generated successfully${NC}"
echo "Updated: packages/types/src/supabase.ts"

# Rebuild DAL package with new types
echo -e "${YELLOW}🔨 Rebuilding DAL package...${NC}"
cd packages/dal && pnpm build && cd ../..

echo -e "${GREEN}✅ DAL package rebuilt with new types${NC}"
echo -e "${YELLOW}Next: Run integration tests${NC}"
