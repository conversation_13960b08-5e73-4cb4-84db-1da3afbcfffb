#!/bin/bash

# BelBooks Staging Deployment Script
# Deploys the authentication branch to staging environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STAGING_PROJECT_ID="kqkeqzpccirdcosiqusl"
STAGING_DB_URL="postgresql://postgres:[password]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres"

echo -e "${BLUE}🚀 Starting BelBooks Staging Deployment${NC}"
echo "=================================================="

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI not found. Please install it first.${NC}"
    exit 1
fi

if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ PostgreSQL client (psql) not found. Please install it first.${NC}"
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}❌ pnpm not found. Please install it first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Verify we're on the correct branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "feat/track-auth" ]; then
    echo -e "${RED}❌ Not on feat/track-auth branch. Current: $CURRENT_BRANCH${NC}"
    echo "Please switch to feat/track-auth branch first:"
    echo "git checkout feat/track-auth"
    exit 1
fi

echo -e "${GREEN}✅ On correct branch: $CURRENT_BRANCH${NC}"

# Check for uncommitted changes
if ! git diff-index --quiet HEAD --; then
    echo -e "${RED}❌ Uncommitted changes detected. Please commit or stash changes first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Working directory clean${NC}"

# Step 1: Apply database migrations to staging
echo -e "${YELLOW}📊 Step 1: Applying database migrations to staging...${NC}"

# Note: In a real deployment, you would need the actual database password
echo "⚠️  Database password required for staging deployment"
echo "Please ensure STAGING_DATABASE_URL is properly configured with credentials"

# Uncomment when ready to deploy:
# export STAGING_DATABASE_URL="$STAGING_DB_URL"
# pnpm db:migrate:staging

echo -e "${GREEN}✅ Database migrations ready (manual step required)${NC}"

# Step 2: Generate types from staging database
echo -e "${YELLOW}🔧 Step 2: Generating types from staging database...${NC}"

# This requires Supabase CLI to be logged in and have access to the project
echo "Generating types from staging database..."
# pnpm db:types:staging

echo -e "${GREEN}✅ Types generation ready (manual step required)${NC}"

# Step 3: Run tests against staging (if applicable)
echo -e "${YELLOW}🧪 Step 3: Running integration tests...${NC}"

# Load staging environment for tests
if [ -f ".env.staging" ]; then
    export $(cat .env.staging | grep -v '^#' | xargs)
fi

# Run tests (commented out for now due to build issues)
# pnpm test

echo -e "${GREEN}✅ Tests ready (manual verification required)${NC}"

# Step 4: Deployment summary
echo -e "${BLUE}📋 Deployment Summary${NC}"
echo "=================================================="
echo "Branch: $CURRENT_BRANCH"
echo "Target: Staging (Project ID: $STAGING_PROJECT_ID)"
echo "Database: kqkeqzpccirdcosiqusl.supabase.co"
echo ""
echo -e "${YELLOW}Manual Steps Required:${NC}"
echo "1. Apply migrations: pnpm db:migrate:staging"
echo "2. Generate types: pnpm db:types:staging"
echo "3. Run integration tests against staging"
echo "4. Verify all functionality works correctly"
echo ""
echo -e "${GREEN}🎉 Staging deployment preparation complete!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Execute the manual steps above"
echo "2. Create PR for feat/track-auth -> main"
echo "3. After PR approval, deploy to production"
