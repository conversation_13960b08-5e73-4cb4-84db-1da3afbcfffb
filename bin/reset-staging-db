#!/bin/bash

# Reset Staging Database - Clean slate for migration
set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🗄️ Resetting Staging Database${NC}"

# Check if STAGING_DATABASE_URL is set
if [ -z "${STAGING_DATABASE_URL:-}" ]; then
    echo -e "${RED}❌ STAGING_DATABASE_URL environment variable not set${NC}"
    echo "Please set: export STAGING_DATABASE_URL=\"postgresql://postgres:[PASSWORD]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres\""
    exit 1
fi

echo -e "${GREEN}✅ Database URL configured${NC}"

# Test connection
echo -e "${YELLOW}🔌 Testing database connection...${NC}"
if ! psql "$STAGING_DATABASE_URL" -c "SELECT version();" > /dev/null 2>&1; then
    echo -e "${RED}❌ Cannot connect to staging database${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Database connection successful${NC}"

# Drop existing tables in correct order (reverse dependency order)
echo -e "${YELLOW}🧹 Dropping existing tables...${NC}"

psql "$STAGING_DATABASE_URL" << 'EOF'
-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS audit_events CASCADE;
DROP TABLE IF EXISTS domain_events CASCADE;
DROP TABLE IF EXISTS reconciliations CASCADE;
DROP TABLE IF EXISTS bank_transactions CASCADE;
DROP TABLE IF EXISTS bank_accounts CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS invoices CASCADE;
DROP TABLE IF EXISTS journal_lines CASCADE;
DROP TABLE IF EXISTS journals CASCADE;
DROP TABLE IF EXISTS vat_periods CASCADE;
DROP TABLE IF EXISTS vat_codes CASCADE;
DROP TABLE IF EXISTS accounts CASCADE;
DROP TABLE IF EXISTS operating_modes CASCADE;
DROP TABLE IF EXISTS entity_memberships CASCADE;
DROP TABLE IF EXISTS entities CASCADE;
DROP TABLE IF EXISTS tenant_memberships CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Drop views
DROP VIEW IF EXISTS v_user_entities CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS get_entity_balance(bigint, date) CASCADE;
DROP FUNCTION IF EXISTS get_account_balance(bigint, date) CASCADE;

-- Drop any remaining RPC functions
DROP FUNCTION IF EXISTS rpc_post_journal CASCADE;
DROP FUNCTION IF EXISTS rpc_switch_mode CASCADE;
DROP FUNCTION IF EXISTS rpc_create_tenant CASCADE;
DROP FUNCTION IF EXISTS rpc_grant_tenant_role CASCADE;
DROP FUNCTION IF EXISTS rpc_create_entity CASCADE;
DROP FUNCTION IF EXISTS rpc_invite CASCADE;
DROP FUNCTION IF EXISTS rpc_accept_invite CASCADE;

-- Drop new tables from auth branch
DROP TABLE IF EXISTS security_events CASCADE;
DROP TABLE IF EXISTS pending_invites CASCADE;

-- Drop extensions (will be recreated)
-- Note: Don't drop citext as it might be used by Supabase auth
-- DROP EXTENSION IF EXISTS citext CASCADE;

SELECT 'Database reset complete' as status;
EOF

echo -e "${GREEN}✅ Database reset complete${NC}"
echo -e "${YELLOW}Next: Run ./bin/apply-staging-migrations${NC}"
