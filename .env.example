# =============================================================================
# LOCAL DEVELOPMENT ENVIRONMENT VARIABLES
# =============================================================================
# This file shows the required environment variables for local development.
# Copy this to .env.local and fill in the values after running 'supabase start'
#
# ⚠️  NEVER commit real keys to Git
# ⚠️  LLM Coders: Use LOCAL values only, never staging/prod
# =============================================================================

# Environment Profile
APP_ENV=local

# Supabase Local Development (from 'supabase start' output)
# These values are generated when you run 'supabase start'
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Server-only keys (BFF, Workers, Migrations) - NEVER expose to browser
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Direct Database Connection (for migrations, pgTAP tests, jobs)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Application Services
BFF_PORT=4000
REDIS_URL=redis://localhost:6379

# Development Tools
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long

# =============================================================================
# TRACK G - AI SUGGESTIONS CONFIGURATION
# =============================================================================

# AI Suggestions Feature Toggle
AISUGGEST_ENABLED=true

# Embedding Provider Configuration
# Options: openai, stub
# - Use 'stub' for testing (deterministic embeddings)
# - Use 'openai' for production (requires API key)
EMBED_PROVIDER=stub

# OpenAI Configuration (when EMBED_PROVIDER=openai)
OPENAI_API_KEY=sk-your-openai-api-key-here
EMBED_MODEL=text-embedding-3-small

# Workers URL for AI processing
WORKERS_URL=http://localhost:8000

# =============================================================================
# STAGING / PRODUCTION (Maintainers Only)
# =============================================================================
# These are managed via CI/CD secrets and platform environment variables.
# See docs/env-profiles.md for details.
#
# Projects:
# - ledgerly-be-staging (EU region)
# - ledgerly-be-prod (EU region)
# =============================================================================