# Staging Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the `feat/track-auth` branch to the staging environment.

## Prerequisites

- Supabase CLI installed and authenticated
- PostgreSQL client (psql) installed
- Access to staging project: `ledgerly-be-staging` (ID: kqkeqzpccirdcosiqusl)
- Database password for staging environment

## Staging Environment Details

- **Project ID**: kqkeqzpccirdcosiqusl
- **Project Name**: ledgerly-be-staging
- **Region**: eu-west-3
- **Database Host**: db.kqkeqzpccirdcosiqusl.supabase.co
- **Database Port**: 5432

## Step 1: Apply Database Migrations

### 1.1 Get Database Password

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Navigate to the `ledgerly-be-staging` project
3. Go to Settings → Database
4. Copy the database password

### 1.2 Set Environment Variables

```bash
export STAGING_DATABASE_URL="postgresql://postgres:[PASSWORD]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres"
```

Replace `[PASSWORD]` with the actual database password from step 1.1.

### 1.3 Apply Migrations

```bash
# Apply all migrations to staging
pnpm db:migrate:staging
```

This will execute the following migration files in order:
- `0001_baseline.sql` (if not already applied)
- `0002_rpcs.sql` - RPC functions for atomic operations
- `0003_tenant_fixes.sql` - Tenant/entity two-layer model fixes
- `0004_tenant_creation_rpc.sql` - Tenant creation for onboarding
- `0005_security_events.sql` - Security events table and monitoring

### 1.4 Verify Migrations

Connect to the staging database and verify the new tables and functions exist:

```sql
-- Check new tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('pending_invites', 'security_events');

-- Check new RPC functions
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE 'rpc_%';
```

## Step 2: Generate Type Definitions

```bash
# Generate types from staging database
pnpm db:types:staging
```

This will update `packages/types/src/supabase.ts` with the latest database schema types.

## Step 3: Run Integration Tests

### 3.1 Set Environment Variables

```bash
# Load staging environment
export $(cat .env.staging | grep -v '^#' | xargs)
```

### 3.2 Execute Tests

```bash
# Run all tests
pnpm test

# Run specific security tests
cd apps/web
pnpm test __tests__/security/
```

## Step 4: Verify Functionality

### 4.1 Key Features to Test

1. **Authentication Flow**
   - User registration
   - Email verification
   - Login/logout
   - Password reset

2. **Authorization & RLS**
   - Tenant isolation
   - Entity access control
   - Role-based permissions

3. **Security Features**
   - CSRF protection
   - Rate limiting
   - Session management
   - Security event logging

4. **Invitation System**
   - Create invitations
   - Accept invitations
   - Role assignment

### 4.2 Database Verification

```sql
-- Verify RLS policies are active
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Check security events table
SELECT event_type, count(*) 
FROM security_events 
GROUP BY event_type;

-- Verify tenant/entity structure
SELECT t.name as tenant_name, e.name as entity_name, tm.role as tenant_role, em.role as entity_role
FROM tenants t
LEFT JOIN entities e ON e.tenant_id = t.id
LEFT JOIN tenant_memberships tm ON tm.tenant_id = t.id
LEFT JOIN entity_memberships em ON em.entity_id = e.id
LIMIT 10;
```

## Step 5: Create Pull Request

Once staging verification is complete:

1. Create PR: `feat/track-auth` → `main`
2. Use squash & merge strategy
3. Include comprehensive description of changes
4. Reference security implementation details

## Rollback Plan

If issues are discovered:

1. **Database Rollback**: Restore from backup (contact Supabase support)
2. **Code Rollback**: Revert to previous stable commit
3. **Type Rollback**: Regenerate types from previous schema

## Security Considerations

- All migrations are additive (no breaking changes)
- RLS policies maintain data isolation
- Security events provide audit trail
- Session management prevents unauthorized access

## Monitoring

After deployment, monitor:
- Security events table for anomalies
- Application logs for errors
- Database performance metrics
- User authentication success rates
