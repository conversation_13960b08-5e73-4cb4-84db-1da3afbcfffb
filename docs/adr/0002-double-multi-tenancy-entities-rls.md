# ADR-0002: Double Multi-Tenancy with Entities and RLS

## Status

Accepted

## Context

BelBooks serves accounting firms that manage multiple clients, each with potentially multiple legal entities (companies). We need a multi-tenancy architecture that provides:

- **Tenant Isolation**: Complete data separation between different accounting firms
- **Entity Isolation**: Separate books for each legal entity within a firm
- **Flexible Access Control**: Different roles and permissions across tenants and entities
- **Performance**: Efficient queries without cross-tenant data leakage
- **Compliance**: Audit trails showing who accessed what entity
- **Scalability**: Support for large firms with hundreds of entities

## Decision

We will implement a **double multi-tenancy** architecture using:

1. **Tenant Level**: Top-level organizations (accounting firms)
2. **Entity Level**: Sub-tenant legal entities (client companies)
3. **Row-Level Security (RLS)**: Database-enforced access control
4. **Role-Based Permissions**: Granular access control at both levels

## Architecture Design

### Data Model Hierarchy

```
Tenant (Accounting Firm)
└── Entity (Client Company)
    ├── Accounts
    ├── Journals
    ├── Invoices
    ├── Bank Transactions
    └── VAT Reports
```

### Access Control Matrix

| Role | Tenant Access | Entity Access | Can Create Entities |
|------|---------------|---------------|-------------------|
| Tenant Owner | All entities | Full access | Yes |
| Tenant Admin | All entities | Full access | Yes |
| Tenant Member | Assigned entities only | Based on entity role | No |
| Entity Owner | Single entity | Full access | No |
| Entity Admin | Single entity | Full access | No |
| Entity Accountant | Single entity | Read/Write accounting | No |
| Entity Bookkeeper | Single entity | Basic entry/edit | No |
| Entity Viewer | Single entity | Read-only | No |

## Implementation Details

### Database Schema

```sql
-- Tenant level
CREATE TABLE tenants (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE tenant_memberships (
  tenant_id BIGINT REFERENCES tenants(id),
  user_id UUID REFERENCES auth.users(id),
  role TEXT CHECK (role IN ('owner', 'admin', 'member')),
  UNIQUE(tenant_id, user_id)
);

-- Entity level
CREATE TABLE entities (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT REFERENCES tenants(id),
  name TEXT NOT NULL,
  UNIQUE(tenant_id, name)
);

CREATE TABLE entity_memberships (
  entity_id BIGINT REFERENCES entities(id),
  user_id UUID REFERENCES auth.users(id),
  role TEXT CHECK (role IN ('owner', 'admin', 'accountant', 'bookkeeper', 'viewer')),
  UNIQUE(entity_id, user_id)
);
```

### RLS Implementation

```sql
-- Helper view for user access
CREATE VIEW v_user_entities AS
SELECT DISTINCT
  em.user_id,
  em.entity_id,
  e.tenant_id,
  em.role,
  e.name as entity_name,
  t.name as tenant_name
FROM entity_memberships em
JOIN entities e ON em.entity_id = e.id
JOIN tenants t ON e.tenant_id = t.id
WHERE em.user_id = auth.uid();

-- Example RLS policy
CREATE POLICY journal_access ON journals 
FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));
```

## Rationale

### Why Double Multi-Tenancy?

1. **Business Model Alignment**: Matches how accounting firms operate
2. **Data Isolation**: Complete separation prevents cross-contamination
3. **Flexible Permissions**: Different access levels for different relationships
4. **Audit Compliance**: Clear tracking of who accessed which entity's data
5. **Performance**: Efficient queries with proper indexing on tenant_id/entity_id

### Why RLS Over Application-Level Filtering?

1. **Security in Depth**: Database enforces isolation even if application has bugs
2. **Performance**: Database can optimize queries with RLS predicates
3. **Simplicity**: No need to add WHERE clauses to every query
4. **Audit**: Database logs show actual data access patterns
5. **Third-party Tools**: Any tool connecting to DB automatically respects isolation

### Alternative Approaches Considered

**Separate Databases per Tenant**: 
- **Pros**: Complete isolation, simpler queries
- **Cons**: Management overhead, no cross-tenant analytics, expensive

**Schema-per-Tenant**:
- **Pros**: Good isolation, shared infrastructure
- **Cons**: Schema management complexity, migration challenges

**Application-Level Filtering**:
- **Pros**: Simple to understand, flexible
- **Cons**: Security risk, performance overhead, error-prone

## Consequences

### Positive

- **Security**: Database-level enforcement prevents data leaks
- **Performance**: Efficient queries with proper indexing strategy
- **Flexibility**: Supports complex permission scenarios
- **Audit**: Built-in access logging and compliance
- **Scalability**: Proven pattern that scales to thousands of entities

### Negative

- **Complexity**: RLS policies are complex to design and test
- **Debugging**: Harder to troubleshoot when policies hide data unexpectedly
- **Performance Risk**: Poorly designed policies can slow queries
- **Migration Complexity**: Schema changes must consider RLS implications

### Risk Mitigation

1. **Comprehensive Testing**: Automated tests for all RLS policies
2. **Policy Documentation**: Clear documentation of access rules
3. **Performance Monitoring**: Track query performance with RLS enabled
4. **Staged Rollout**: Deploy RLS changes gradually with rollback plans

## Implementation Guidelines

### RLS Policy Patterns

```sql
-- Basic entity access
USING (entity_id IN (SELECT entity_id FROM v_user_entities))

-- Role-based access
USING (entity_id IN (
  SELECT entity_id FROM v_user_entities 
  WHERE role IN ('owner', 'admin', 'accountant')
))

-- Time-based access (for archived data)
USING (
  entity_id IN (SELECT entity_id FROM v_user_entities) 
  AND created_at > (CURRENT_DATE - INTERVAL '7 years')
)
```

### Index Strategy

```sql
-- Every transactional table needs these indexes
CREATE INDEX idx_journals_entity_id ON journals(entity_id);
CREATE INDEX idx_accounts_entity_id ON accounts(entity_id);
CREATE INDEX idx_invoices_entity_id ON invoices(entity_id);

-- Composite indexes for common queries
CREATE INDEX idx_journals_entity_date ON journals(entity_id, transaction_date);
```

### Development Guidelines

1. **Always Test RLS**: Every policy must have positive and negative test cases
2. **Use Service Role Sparingly**: Only for system operations, not user data access
3. **Monitor Performance**: Track execution plans for RLS-enabled queries
4. **Document Policies**: Each policy needs clear business justification

## Migration Strategy

1. **Phase 1**: Implement schema without RLS (current)
2. **Phase 2**: Add RLS policies in "permissive" mode (log only)
3. **Phase 3**: Enable RLS in "warn" mode (log violations)
4. **Phase 4**: Enable full RLS enforcement
5. **Phase 5**: Remove temporary bypass mechanisms

## Review Criteria

This architecture should be reviewed when:
- Performance issues arise from RLS complexity
- Business model changes (e.g., direct client access)
- Scale exceeds 10,000 entities per tenant
- Regulatory requirements change significantly
- Alternative multi-tenancy patterns prove superior