# ADR-0001: System of Record - PostgreSQL

## Status

Accepted

## Context

For Ledgerly, we need to choose a database system that can serve as the single source of truth for all accounting data. The system must provide:

- **ACID compliance** for financial data integrity
- **Strong consistency** for accounting operations
- **Advanced constraints** for double-entry bookkeeping rules
- **Row-level security (RLS)** for multi-tenant isolation
- **Audit capabilities** for compliance requirements
- **Performance** for real-time financial reporting
- **Vector search** capabilities for AI-powered document processing

## Decision

We will use **PostgreSQL** (via Supabase) as our system of record for all persistent data.

## Rationale

### PostgreSQL Advantages

1. **Financial Integrity**
   - ACID compliance ensures data consistency
   - Constraint triggers for complex business rules (e.g., balanced journals)
   - Deferrable constraints for transaction-level validation
   - Numeric types with exact decimal precision

2. **Multi-tenancy & Security**
   - Row-Level Security (RLS) for tenant isolation
   - Policy-based access control at the database level
   - Service role segregation (anon vs service key usage)
   - Built-in user authentication integration

3. **Advanced Features**
   - JSON/JSONB for flexible metadata storage
   - Full-text search for document content
   - Vector extensions (pgvector) for AI similarity search
   - Stored procedures for atomic operations
   - Materialized views for reporting performance

4. **Operational Benefits**
   - Mature ecosystem with extensive tooling
   - Strong backup and replication capabilities
   - Performance monitoring and optimization tools
   - Schema migration and versioning support

### Supabase Layer

Using Supabase provides additional benefits:
- **Managed infrastructure** with EU region support for GDPR compliance
- **Real-time subscriptions** for live financial data updates
- **Automatic API generation** with OpenAPI specs
- **Built-in authentication** with JWT integration
- **Dashboard and admin tools** for development

### Alternative Considerations

**MongoDB/Document Stores**: Rejected due to eventual consistency and weaker constraint enforcement for financial data.

**MySQL**: Considered but PostgreSQL's advanced features (RLS, JSON, constraints) are better suited for accounting.

**SQLite**: Not suitable for multi-tenant production workloads.

**Event Store Systems**: Considered for audit trails but decided to use traditional tables with event outbox pattern for simpler operations.

## Consequences

### Positive

- **Data Integrity**: Strong guarantees for financial data consistency
- **Security**: Built-in multi-tenant isolation via RLS
- **Performance**: Optimized queries with proper indexing
- **Compliance**: Audit trails and immutable records
- **Development Speed**: Rich SQL feature set and Supabase tooling
- **Scalability**: Proven at scale with read replicas and connection pooling

### Negative

- **Complexity**: RLS policies require careful design and testing
- **Lock-in**: Some Supabase-specific features (real-time, auth) create vendor dependency
- **Cost**: Managed PostgreSQL is more expensive than self-hosted
- **Learning Curve**: Team needs strong SQL and PostgreSQL knowledge

### Migration Risks

- **Schema Changes**: Must use forward-only migrations to avoid data loss
- **RLS Testing**: Complex policies require comprehensive test coverage
- **Performance**: Query performance must be monitored as data grows
- **Backup Strategy**: Critical for financial data - automated daily backups to multiple regions

## Implementation Notes

### Database Architecture

```sql
-- Example of constraint implementation
ALTER TABLE journal_lines 
ADD CONSTRAINT check_journal_balance 
DEFERRABLE INITIALLY DEFERRED;

-- Example of RLS policy
CREATE POLICY entity_access ON journals 
FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));
```

### Connection Strategy

- **Web App**: Uses anon key with user JWT for RLS
- **BFF API**: Uses service role key for trusted operations
- **Workers**: Uses service role with connection pooling

### Monitoring

- Query performance monitoring via pg_stat_statements
- Connection pool metrics via PgBouncer
- RLS policy execution tracking
- Database size and growth monitoring

## Review

This decision should be reviewed annually or when:
- Scale requirements exceed PostgreSQL capabilities (>10TB, >100k concurrent users)
- Compliance requirements change significantly
- Supabase pricing or feature changes affect viability
- Alternative databases gain significantly better accounting-specific features