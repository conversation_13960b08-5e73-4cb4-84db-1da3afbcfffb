# Session Security System Documentation

## Overview

The Session Security System provides comprehensive session management with advanced security features including device fingerprinting, concurrent session limits, activity tracking, and security event logging. This system is designed to protect against session hijacking, unauthorized access, and other session-based security threats.

## Table of Contents

1. [API Documentation](#api-documentation)
2. [Integration Guide](#integration-guide)
3. [Configuration Reference](#configuration-reference)
4. [Security Features](#security-features)
5. [Usage Examples](#usage-examples)
6. [Security Best Practices](#security-best-practices)

## API Documentation

### SessionSecurityManager Class

The `SessionSecurityManager` class is the core component that handles all session security operations.

#### Constructor

```typescript
constructor(supabase: SupabaseClient, config?: Partial<SessionSecurityConfig>)
```

**Parameters:**
- `supabase`: SupabaseClient instance for database operations
- `config`: Optional configuration object to override default settings

#### Methods

##### `initializeSession(userId: string, metadata: SessionMetadata): Promise<SessionSecurityResult>`

Initializes a new secure session for a user.

**Parameters:**
- `userId`: Unique identifier for the user
- `metadata`: Session metadata including device fingerprint, IP address, and user agent

**Returns:** Promise resolving to SessionSecurityResult with session details

**Example:**
```typescript
const result = await sessionManager.initializeSession('user-123', {
  deviceFingerprint: 'fp_abc123',
  ipAddress: '***********',
  userAgent: 'Mozilla/5.0...'
});
```

##### `updateActivity(sessionId: string): Promise<SessionSecurityResult>`

Updates the last activity timestamp for a session.

**Parameters:**
- `sessionId`: Unique session identifier

**Returns:** Promise resolving to SessionSecurityResult

##### `validateSession(sessionId: string, metadata: SessionMetadata): Promise<SessionSecurityResult>`

Validates a session and checks for security violations.

**Parameters:**
- `sessionId`: Session identifier to validate
- `metadata`: Current session metadata for comparison

**Returns:** Promise resolving to SessionSecurityResult with validation status

##### `terminateSession(sessionId: string, reason?: string): Promise<void>`

Terminates a specific session.

**Parameters:**
- `sessionId`: Session identifier to terminate
- `reason`: Optional reason for termination (for logging)

##### `terminateAllUserSessions(userId: string, excludeSessionId?: string): Promise<void>`

Terminates all sessions for a user.

**Parameters:**
- `userId`: User identifier
- `excludeSessionId`: Optional session ID to exclude from termination

##### `getUserSessions(userId: string): Promise<SessionData[]>`

Retrieves all active sessions for a user.

**Parameters:**
- `userId`: User identifier

**Returns:** Promise resolving to array of SessionData objects

##### `cleanupExpiredSessions(): Promise<void>`

Removes expired sessions from the database.

### Type Definitions

#### SessionSecurityConfig

```typescript
interface SessionSecurityConfig {
  maxConcurrentSessions: number;
  idleTimeoutMinutes: number;
  absoluteTimeoutHours: number;
  securityEventLogging: boolean;
  environment: 'development' | 'production';
}
```

#### SessionMetadata

```typescript
interface SessionMetadata {
  deviceFingerprint: string;
  ipAddress: string;
  userAgent: string;
}
```

#### SessionSecurityResult

```typescript
interface SessionSecurityResult {
  success: boolean;
  sessionId?: string;
  message?: string;
  requiresReauth?: boolean;
  timeoutWarning?: boolean;
  minutesUntilTimeout?: number;
}
```

#### SessionData

```typescript
interface SessionData {
  id: string;
  userId: string;
  deviceFingerprint: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActivity: Date;
  isActive: boolean;
}
```

## Integration Guide

### Basic Integration

1. **Initialize the Session Security Manager**

```typescript
import { createClient } from '@supabase/supabase-js';
import { SessionSecurityManager } from '@/lib/security/session-security';

const supabase = createClient(url, key);
const sessionManager = new SessionSecurityManager(supabase, {
  maxConcurrentSessions: 3,
  idleTimeoutMinutes: 30,
  absoluteTimeoutHours: 8
});
```

2. **Integrate with Authentication Flow**

```typescript
// During login
const loginResult = await signIn(email, password);
if (loginResult.success) {
  const sessionResult = await sessionManager.initializeSession(
    loginResult.user.id,
    {
      deviceFingerprint: generateDeviceFingerprint(),
      ipAddress: getClientIP(),
      userAgent: navigator.userAgent
    }
  );
  
  if (!sessionResult.success) {
    // Handle session initialization failure
    await signOut();
    throw new Error(sessionResult.message);
  }
}
```

3. **Middleware Integration**

```typescript
// In your authentication middleware
export async function authMiddleware(request: NextRequest) {
  const sessionId = request.cookies.get('session-id')?.value;
  
  if (sessionId) {
    const validationResult = await sessionManager.validateSession(sessionId, {
      deviceFingerprint: request.headers.get('x-device-fingerprint'),
      ipAddress: getClientIP(request),
      userAgent: request.headers.get('user-agent')
    });
    
    if (!validationResult.success) {
      return redirectToLogin();
    }
    
    // Update activity
    await sessionManager.updateActivity(sessionId);
  }
}
```

### Advanced Integration

#### Activity Tracking

```typescript
// Set up periodic activity updates
setInterval(async () => {
  const sessionId = getCurrentSessionId();
  if (sessionId) {
    const result = await sessionManager.updateActivity(sessionId);
    
    if (result.timeoutWarning) {
      showTimeoutWarning(result.minutesUntilTimeout);
    }
  }
}, 60000); // Check every minute
```

#### Session Management UI

```typescript
// Get user's active sessions for management interface
const sessions = await sessionManager.getUserSessions(userId);

// Terminate specific session
await sessionManager.terminateSession(sessionId, 'User requested termination');

// Terminate all other sessions
await sessionManager.terminateAllUserSessions(userId, currentSessionId);
```

## Configuration Reference

### Default Configuration

```typescript
const defaultConfig: SessionSecurityConfig = {
  maxConcurrentSessions: 5,
  idleTimeoutMinutes: 30,
  absoluteTimeoutHours: 24,
  securityEventLogging: true,
  environment: 'production'
};
```

### Environment-Specific Configurations

#### Development Environment

```typescript
const devConfig: Partial<SessionSecurityConfig> = {
  maxConcurrentSessions: 10,
  idleTimeoutMinutes: 120, // 2 hours
  absoluteTimeoutHours: 48, // 2 days
  environment: 'development'
};
```

#### Production Environment

```typescript
const prodConfig: Partial<SessionSecurityConfig> = {
  maxConcurrentSessions: 3,
  idleTimeoutMinutes: 15,
  absoluteTimeoutHours: 8,
  securityEventLogging: true,
  environment: 'production'
};
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxConcurrentSessions` | number | 5 | Maximum number of concurrent sessions per user |
| `idleTimeoutMinutes` | number | 30 | Minutes of inactivity before session expires |
| `absoluteTimeoutHours` | number | 24 | Maximum session duration regardless of activity |
| `securityEventLogging` | boolean | true | Enable logging of security events |
| `environment` | string | 'production' | Environment setting affecting timeout behavior |

## Security Features

### Device Fingerprinting

Device fingerprinting creates a unique identifier for each device/browser combination to detect session hijacking attempts.

**How it works:**
- Combines browser characteristics, screen resolution, timezone, and other factors
- Stored with each session for validation
- Alerts triggered when fingerprint changes unexpectedly

**Implementation:**
```typescript
function generateDeviceFingerprint(): string {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('Device fingerprint', 2, 2);
  
  return btoa(JSON.stringify({
    canvas: canvas.toDataURL(),
    screen: `${screen.width}x${screen.height}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: navigator.language,
    platform: navigator.platform
  }));
}
```

### Concurrent Session Management

Limits the number of active sessions per user to prevent account sharing and reduce attack surface.

**Features:**
- Configurable session limits
- Automatic cleanup of oldest sessions when limit exceeded
- Session termination notifications

### Timeout Management

Dual timeout system for comprehensive session security:

1. **Idle Timeout**: Sessions expire after period of inactivity
2. **Absolute Timeout**: Sessions expire after maximum duration regardless of activity

**Timeout Warnings:**
- Users receive warnings before session expiration
- Configurable warning thresholds
- Automatic session extension on user activity

### Security Event Logging

Comprehensive logging of security-related events for monitoring and forensics.

**Logged Events:**
- Session creation and termination
- Failed validation attempts
- Concurrent session limit violations
- Suspicious activity detection
- Device fingerprint mismatches

**Log Format:**
```typescript
{
  event_type: 'session_security_violation',
  user_id: 'user-123',
  session_id: 'session-456',
  details: {
    violation_type: 'device_fingerprint_mismatch',
    expected_fingerprint: 'fp_abc123',
    actual_fingerprint: 'fp_xyz789'
  },
  timestamp: '2024-01-15T10:30:00Z',
  ip_address: '***********'
}
```

## Usage Examples

### Complete Authentication Flow

```typescript
import { SessionSecurityManager } from '@/lib/security/session-security';
import { createClient } from '@supabase/supabase-js';

class AuthService {
  private sessionManager: SessionSecurityManager;

  constructor() {
    const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_ANON_KEY!);
    this.sessionManager = new SessionSecurityManager(supabase, {
      maxConcurrentSessions: 3,
      idleTimeoutMinutes: 30,
      absoluteTimeoutHours: 8,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'development'
    });
  }

  async login(email: string, password: string, metadata: SessionMetadata) {
    try {
      // Authenticate user
      const { data: authData, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      // Initialize secure session
      const sessionResult = await this.sessionManager.initializeSession(
        authData.user.id,
        metadata
      );

      if (!sessionResult.success) {
        await this.supabase.auth.signOut();
        throw new Error(`Session initialization failed: ${sessionResult.message}`);
      }

      return {
        user: authData.user,
        sessionId: sessionResult.sessionId,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async validateAndRefreshSession(sessionId: string, metadata: SessionMetadata) {
    const result = await this.sessionManager.validateSession(sessionId, metadata);

    if (result.success) {
      // Update activity
      await this.sessionManager.updateActivity(sessionId);

      // Check for timeout warnings
      if (result.timeoutWarning) {
        return {
          ...result,
          warning: `Session will expire in ${result.minutesUntilTimeout} minutes`
        };
      }
    }

    return result;
  }

  async logout(sessionId: string) {
    await this.sessionManager.terminateSession(sessionId, 'User logout');
    await this.supabase.auth.signOut();
  }

  async logoutAllDevices(userId: string, currentSessionId: string) {
    await this.sessionManager.terminateAllUserSessions(userId, currentSessionId);
  }
}
```

### React Hook for Session Management

```typescript
import { useEffect, useState, useCallback } from 'react';
import { SessionSecurityManager } from '@/lib/security/session-security';

interface UseSessionSecurityOptions {
  sessionId: string;
  userId: string;
  activityInterval?: number;
}

export function useSessionSecurity({
  sessionId,
  userId,
  activityInterval = 60000
}: UseSessionSecurityOptions) {
  const [timeoutWarning, setTimeoutWarning] = useState<{
    show: boolean;
    minutesLeft: number;
  }>({ show: false, minutesLeft: 0 });

  const [sessions, setSessions] = useState<SessionData[]>([]);

  const sessionManager = new SessionSecurityManager(supabase);

  // Update activity periodically
  useEffect(() => {
    const interval = setInterval(async () => {
      if (sessionId) {
        const result = await sessionManager.updateActivity(sessionId);

        if (result.timeoutWarning) {
          setTimeoutWarning({
            show: true,
            minutesLeft: result.minutesUntilTimeout || 0
          });
        } else {
          setTimeoutWarning({ show: false, minutesLeft: 0 });
        }
      }
    }, activityInterval);

    return () => clearInterval(interval);
  }, [sessionId, activityInterval]);

  // Load user sessions
  const loadSessions = useCallback(async () => {
    if (userId) {
      const userSessions = await sessionManager.getUserSessions(userId);
      setSessions(userSessions);
    }
  }, [userId]);

  // Terminate specific session
  const terminateSession = useCallback(async (targetSessionId: string) => {
    await sessionManager.terminateSession(targetSessionId, 'User terminated session');
    await loadSessions();
  }, [loadSessions]);

  // Terminate all other sessions
  const terminateOtherSessions = useCallback(async () => {
    await sessionManager.terminateAllUserSessions(userId, sessionId);
    await loadSessions();
  }, [userId, sessionId, loadSessions]);

  return {
    timeoutWarning,
    sessions,
    loadSessions,
    terminateSession,
    terminateOtherSessions
  };
}
```

### Next.js Middleware Integration

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { SessionSecurityManager } from '@/lib/security/session-security';
import { createClient } from '@supabase/supabase-js';

export async function middleware(request: NextRequest) {
  const sessionId = request.cookies.get('session-id')?.value;
  const protectedPaths = ['/dashboard', '/profile', '/settings'];

  const isProtectedPath = protectedPaths.some(path =>
    request.nextUrl.pathname.startsWith(path)
  );

  if (isProtectedPath && sessionId) {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const sessionManager = new SessionSecurityManager(supabase);

    const metadata = {
      deviceFingerprint: request.headers.get('x-device-fingerprint') || '',
      ipAddress: request.ip || request.headers.get('x-forwarded-for') || '',
      userAgent: request.headers.get('user-agent') || ''
    };

    const validationResult = await sessionManager.validateSession(sessionId, metadata);

    if (!validationResult.success) {
      // Clear invalid session cookie
      const response = NextResponse.redirect(new URL('/login', request.url));
      response.cookies.delete('session-id');
      return response;
    }

    // Update activity
    await sessionManager.updateActivity(sessionId);

    // Add timeout warning header if needed
    if (validationResult.timeoutWarning) {
      const response = NextResponse.next();
      response.headers.set('x-session-timeout-warning', 'true');
      response.headers.set('x-minutes-until-timeout',
        validationResult.minutesUntilTimeout?.toString() || '0');
      return response;
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/profile/:path*', '/settings/:path*']
};
```

### Session Management Dashboard Component

```typescript
import React, { useEffect, useState } from 'react';
import { useSessionSecurity } from '@/hooks/useSessionSecurity';

interface SessionManagerProps {
  userId: string;
  currentSessionId: string;
}

export function SessionManager({ userId, currentSessionId }: SessionManagerProps) {
  const {
    sessions,
    loadSessions,
    terminateSession,
    terminateOtherSessions,
    timeoutWarning
  } = useSessionSecurity({
    sessionId: currentSessionId,
    userId
  });

  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  const formatLastActivity = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minutes ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hours ago`;

    const days = Math.floor(hours / 24);
    return `${days} days ago`;
  };

  return (
    <div className="session-manager">
      {timeoutWarning.show && (
        <div className="timeout-warning">
          ⚠️ Your session will expire in {timeoutWarning.minutesLeft} minutes
        </div>
      )}

      <div className="session-list">
        <h3>Active Sessions</h3>

        <div className="session-actions">
          <button
            onClick={terminateOtherSessions}
            className="btn-danger"
          >
            Terminate All Other Sessions
          </button>
        </div>

        {sessions.map(session => (
          <div key={session.id} className="session-item">
            <div className="session-info">
              <div className="device-info">
                {session.id === currentSessionId ? (
                  <span className="current-session">Current Session</span>
                ) : (
                  <span className="other-session">Other Device</span>
                )}
              </div>

              <div className="session-details">
                <p>IP: {session.ipAddress}</p>
                <p>Last Activity: {formatLastActivity(session.lastActivity)}</p>
                <p>Created: {new Date(session.createdAt).toLocaleDateString()}</p>
              </div>
            </div>

            {session.id !== currentSessionId && (
              <button
                onClick={() => terminateSession(session.id)}
                className="btn-secondary"
              >
                Terminate
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Security Best Practices

### 1. Device Fingerprinting

- **Generate robust fingerprints**: Include multiple browser characteristics
- **Handle fingerprint changes**: Allow for minor variations due to browser updates
- **Privacy considerations**: Avoid collecting personally identifiable information

### 2. Session Validation

- **Validate on every request**: Check session validity for protected routes
- **Use secure cookies**: Set HttpOnly, Secure, and SameSite flags
- **Implement CSRF protection**: Use anti-CSRF tokens with session validation

### 3. Timeout Management

- **Set appropriate timeouts**: Balance security with user experience
- **Provide warnings**: Give users advance notice of session expiration
- **Allow session extension**: Let users extend sessions through activity

### 4. Concurrent Session Limits

- **Set reasonable limits**: Consider user workflows and device usage patterns
- **Provide session management**: Allow users to view and terminate sessions
- **Handle limit violations gracefully**: Clear messaging when limits are reached

### 5. Security Event Logging

- **Log security events**: Track all session-related security incidents
- **Monitor for patterns**: Set up alerts for suspicious activity
- **Retain logs appropriately**: Follow data retention policies

### 6. Error Handling

- **Fail securely**: Default to denying access when validation fails
- **Provide clear messages**: Help users understand security requirements
- **Log security failures**: Track failed validation attempts

### 7. Database Security

- **Use Row Level Security**: Ensure users can only access their own sessions
- **Encrypt sensitive data**: Protect session metadata in the database
- **Regular cleanup**: Remove expired sessions to minimize data exposure

## Troubleshooting

### Common Issues

1. **Session validation failures**
   - Check device fingerprint generation consistency
   - Verify IP address detection in different network configurations
   - Ensure user agent strings are being captured correctly

2. **Timeout warnings not appearing**
   - Verify activity update intervals are appropriate
   - Check timeout configuration values
   - Ensure client-side activity tracking is working

3. **Concurrent session limit issues**
   - Review session cleanup processes
   - Check for sessions not being properly terminated
   - Verify session limit configuration

### Debugging

Enable debug logging by setting the environment variable:
```bash
DEBUG_SESSION_SECURITY=true
```

This will provide detailed logs of session operations and security validations.

## Migration and Deployment

### Database Setup

Ensure the following tables exist in your Supabase database:

```sql
-- Sessions table (should already exist)
CREATE TABLE IF NOT EXISTS sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  device_fingerprint TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Security events table (should already exist)
CREATE TABLE IF NOT EXISTS security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Environment Variables

```bash
# Required
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional
DEBUG_SESSION_SECURITY=false
SESSION_SECURITY_MAX_CONCURRENT=5
SESSION_SECURITY_IDLE_TIMEOUT=30
SESSION_SECURITY_ABSOLUTE_TIMEOUT=24
```

This completes the comprehensive documentation for the Session Security System. The documentation covers all aspects of implementation, configuration, and usage, providing developers with everything they need to integrate and maintain the session security features.
