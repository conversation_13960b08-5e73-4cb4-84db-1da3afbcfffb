# Running Ledgerly Locally

This guide walks you through setting up and running the complete Ledgerly development environment on your local machine.

## Prerequisites

Before starting, ensure you have completed the setup in [docs/dev-setup-mac.md](./dev-setup-mac.md).

## Quick Start

1. **Clone and setup the repository:**
   ```bash
   git clone <repo-url>
   cd ledgerly
   cp .env.example .env
   pnpm install
   ```

2. **Start Supabase locally:**
   ```bash
   supabase start
   ```
   
   This will start PostgreSQL, PostgREST, and other services. Note the output which contains your local connection details.

3. **Update your .env file with the Supabase local URLs:**
   ```bash
   # Copy these values from supabase start output
   NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
   NEXT_PUBLIC_SUPABASE_ANON_KEY=<anon_key_from_output>
   SUPABASE_SERVICE_ROLE_KEY=<service_role_key_from_output>
   DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
   
   # BFF Configuration
   BFF_PORT=4000
   INTERNAL_KEY=your-secure-internal-key-here
   
   # Workers Configuration
   REDIS_URL=redis://localhost:6379
   ```

4. **Apply database migrations:**
   ```bash
   pnpm db:migrate:local
   pnpm db:seed:local
   ```

5. **Generate TypeScript types:**
   ```bash
   pnpm db:types
   ```

6. **Start all services:**
   ```bash
   # Terminal 1: Start Next.js web app
   cd apps/web
   pnpm dev
   
   # Terminal 2: Start BFF API  
   cd apps/bff
   pnpm dev
   
   # Terminal 3: Start Python workers
   cd apps/workers-py
   make run-dev
   
   # Terminal 4: Start Redis (if not running)
   redis-server
   ```

## Detailed Setup Steps

### 1. Database Setup

**Start Supabase:**
```bash
supabase start
```

**Expected output:**
```
Started supabase local development setup.

         API URL: http://localhost:54321
          DB URL: postgresql://postgres:postgres@localhost:54322/postgres
      Studio URL: http://localhost:54323
    Inbucket URL: http://localhost:54324
        JWT secret: <jwt-secret>
         anon key: <anon-key>
service_role key: <service-role-key>
```

**Apply migrations and seed data:**
```bash
# Set DATABASE_URL
export DATABASE_URL="postgresql://postgres:postgres@localhost:54322/postgres"

# Apply all migrations
pnpm db:migrate:local

# Expected output:
# CREATE EXTENSION
# CREATE TABLE
# CREATE POLICY
# ... (migration output)

# Seed development data
pnpm db:seed:local

# Expected output:
# INSERT 0 1
# INSERT 0 1
# INSERT 0 30 (accounts)
```

**Verify database setup:**
```bash
# Run invariant tests
psql $DATABASE_URL -f packages/db/tests/001_invariants.sql

# Expected output:
# NOTICE:  PASS: Unbalanced journal correctly rejected
# NOTICE:  PASS: Journal line modification correctly prevented  
# NOTICE:  PASS: RLS enabled on critical tables
# NOTICE:  PASS: Belgian VAT codes seeded correctly
# NOTICE:  All database invariant tests completed successfully
```

### 2. TypeScript Services

**Generate types and build packages:**
```bash
# Generate Supabase types
pnpm db:types

# Install dependencies and build all packages
pnpm install
pnpm build

# Expected output:
# ✓ packages/types built successfully
# ✓ packages/dal built successfully  
# ✓ packages/domain-* built successfully
# ✓ apps/web built successfully
# ✓ apps/bff built successfully
```

**Start web application:**
```bash
cd apps/web
pnpm dev

# Expected output:
# ▲ Next.js 14.0.0
# - Local:        http://localhost:3000
# - Network:      http://192.168.1.x:3000
# ✓ Ready in 2.1s
```

**Test web app:** Visit http://localhost:3000
- Dashboard should show "Environment OK" 
- Navigation should work between pages
- Database connectivity test should pass

**Start BFF API:**
```bash
cd apps/bff
pnpm dev

# Expected output:
# [INFO] Server starting on port 4000
# [INFO] Supabase client initialized  
# [INFO] Authentication plugin registered
# [INFO] Health check: http://localhost:4000/healthz
# [INFO] Server listening on http://localhost:4000
```

**Test BFF API:**
```bash
# Health check
curl http://localhost:4000/healthz
# Expected: {"ok": true}

# Test RPC endpoint (requires INTERNAL_KEY)
curl -X POST http://localhost:4000/rpc/post-journal \
  -H "Content-Type: application/json" \
  -H "X-Internal-Key: your-secure-internal-key-here" \
  -d '{
    "entity_id": 1,
    "description": "Test journal",
    "transaction_date": "2024-01-15",
    "lines": [
      {"account_id": 1, "debit_amount": 100.00},
      {"account_id": 2, "credit_amount": 100.00}
    ]
  }'
```

### 3. Python AI Workers

**Setup Python environment:**
```bash
cd apps/workers-py
uv sync

# Expected output:
# Creating virtualenv at: .venv
# Resolved X packages in Xms
# Installed X packages in Xms
```

**Start Redis (if not running):**
```bash
# macOS with Homebrew:
brew services start redis

# Or run directly:
redis-server

# Expected output:
# Ready to accept connections
```

**Start FastAPI server:**
```bash
cd apps/workers-py
make run-dev

# Expected output:
# INFO:     Will watch for changes in these directories: ['/path/to/apps/workers-py']
# INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
# INFO:     Started reloader process [12345] using StatReload
# INFO:     Started server process [12346]
# INFO:     Waiting for application startup.
# INFO:     Application startup complete.
```

**Start Celery worker (optional, in separate terminal):**
```bash
cd apps/workers-py
make run-worker

# Expected output:
# [INFO/MainProcess] Connected to redis://localhost:6379//
# [INFO/MainProcess] mingle: searching for neighbor nodes for 1.0 seconds
# [INFO/MainProcess] mingle: all alone
# [INFO/MainProcess] celery@hostname ready.
```

**Test Python workers:**
```bash
# Health check
curl http://127.0.0.1:8000/health

# Expected response:
# {
#   "status": "healthy",
#   "database": "connected", 
#   "redis": "connected",
#   "services": ["ocr", "pdf", "ai"]
# }

# Test document processing endpoint
curl -X POST http://127.0.0.1:8000/process-document \
  -H "Content-Type: application/json" \
  -d '{
    "entity_id": "test-entity",
    "file_url": "https://example.com/sample.pdf"
  }'
```

## Development Workflow

### Making Changes

1. **Database changes:**
   ```bash
   # Create new migration
   echo "-- New migration" > packages/db/migrations/0003_new_feature.sql
   
   # Add to all.sql
   echo '\i packages/db/migrations/0003_new_feature.sql' >> packages/db/migrations/all.sql
   
   # Apply migration
   pnpm db:migrate:local
   
   # Regenerate types
   pnpm db:types
   ```

2. **Code changes:**
   ```bash
   # Lint and type check
   pnpm lint
   pnpm typecheck
   
   # Build and test
   pnpm build
   pnpm test
   ```

3. **Python changes:**
   ```bash
   cd apps/workers-py
   
   # Lint and format
   make lint
   make format
   
   # Type check and test
   make type
   make test
   ```

### Testing

**Run all tests:**
```bash
# JavaScript/TypeScript tests
pnpm test

# Python tests  
cd apps/workers-py && make test

# Database tests
psql $DATABASE_URL -f packages/db/tests/001_invariants.sql

# Linting
pnpm lint
cd apps/workers-py && make lint
```

**Integration testing:**
```bash
# Test full workflow
curl -X POST http://localhost:4000/rpc/post-journal \
  -H "Content-Type: application/json" \
  -H "X-Internal-Key: your-secure-internal-key-here" \
  -d @test-data/sample-journal.json
```

### Debugging

**View logs:**
```bash
# Supabase logs
supabase logs

# Next.js logs (in terminal running web app)
# BFF logs (in terminal running BFF)
# Python logs (in terminal running workers)

# Database queries
tail -f ~/.supabase/logs/postgresql.log
```

**Database inspection:**
```bash
# Connect to database
psql $DATABASE_URL

# Useful queries:
SELECT * FROM entities;
SELECT * FROM accounts WHERE entity_id = 1;
SELECT * FROM journals ORDER BY created_at DESC LIMIT 5;
```

## Troubleshooting

### Common Issues

**1. Database connection errors:**
```bash
# Check Supabase status
supabase status

# Restart if needed
supabase stop
supabase start
```

**2. Port conflicts:**
```bash
# Check what's running on ports
lsof -i :3000  # Next.js
lsof -i :4000  # BFF
lsof -i :8000  # Python workers
lsof -i :6379  # Redis

# Kill processes if needed
kill -9 <PID>
```

**3. Python environment issues:**
```bash
cd apps/workers-py

# Recreate virtual environment
rm -rf .venv
uv venv
uv sync

# Install system dependencies
brew install tesseract poppler
```

**4. Type generation issues:**
```bash
# Ensure database is running
supabase status

# Regenerate types
pnpm db:types

# Check generated file
cat packages/types/src/supabase.ts
```

### Reset Everything

**Complete reset:**
```bash
# Stop all services
supabase stop

# Clean build artifacts
pnpm clean
rm -rf node_modules
rm -rf apps/*/node_modules
rm -rf packages/*/node_modules

# Reinstall
pnpm install

# Restart services
supabase start
pnpm db:migrate:local
pnpm db:seed:local
pnpm db:types
```

## Production Readiness Checklist

Before deploying to production:

- [ ] All tests pass (`pnpm test`)
- [ ] Database migrations tested
- [ ] RLS policies validated  
- [ ] Environment variables configured
- [ ] API security headers set
- [ ] Error monitoring configured
- [ ] Backup strategy implemented
- [ ] Performance benchmarks met
- [ ] Security audit completed
- [ ] Documentation updated

## Next Steps

Once your local environment is running:

1. **Review the codebase**: Start with `docs/adr/` for architectural decisions
2. **Run the test suite**: `pnpm test` to understand test patterns  
3. **Try the APIs**: Use the BFF endpoints to understand data flow
4. **Check the database**: Explore the schema and RLS policies
5. **Read the specs**: Review `docs/specs/` for business logic

## Getting Help

- **Database issues**: Check Supabase logs and documentation
- **Build issues**: Ensure Node.js 20+ and proper dependencies
- **Python issues**: Verify uv installation and system dependencies
- **General setup**: Review [dev-setup-mac.md](./dev-setup-mac.md)