# Developer Setup Guide - Mac (Apple Silicon)

## Prerequisites Installation

### Homebrew & Core Tools

```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Update and install core tools
brew update
brew install git gh jq yq coreutils fd ripgrep tree mkcert
brew install --cask docker
brew install supabase/tap/supabase
brew install libpq redis tesseract poppler

# Add libpq to PATH for Postgres client tools
echo 'export PATH="/opt/homebrew/opt/libpq/bin:$PATH"' >> ~/.zshrc
```

### Node.js & pnpm

```bash
# Install Node.js 20
brew install node@20

# Enable corepack and install pnpm
corepack enable
corepack prepare pnpm@9 --activate
```

### Python with uv

```bash
# Install uv (preferred method)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Add uv to PATH
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.zshrc

# Reload shell
exec $SHELL

# Verify installation
uv --version
```

### Supabase CLI

```bash
# Verify Supabase CLI installation
supabase --version
```

## Troubleshooting (Apple Silicon)

### Docker CPU Architecture Issues
If you encounter platform-specific Docker issues:
```bash
# Run containers with explicit platform
docker run --platform linux/amd64 <image>
```

### libpq PATH Issues
If PostgreSQL client commands aren't found:
```bash
# Verify PATH includes libpq
echo $PATH | grep libpq

# If missing, add to your shell profile
echo 'export PATH="/opt/homebrew/opt/libpq/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### Redis Connection Issues
```bash
# Start Redis service
brew services start redis

# Test connection
redis-cli ping
# Should return: PONG
```

## Verification

Run these commands to verify your setup:

```bash
# Core tools
git --version
node --version
pnpm --version
uv --version
supabase --version

# Database tools
psql --version
redis-cli --version

# Python tools
python3 --version

# Docker
docker --version
```

## Next Steps

1. Clone the repository
2. Follow the instructions in `docs/run-locally.md`
3. Set up your `.env` file from `.env.example`