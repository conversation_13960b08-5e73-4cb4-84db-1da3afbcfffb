# Track H: Email-In Inbox & Auto-Capture

## Overview

The Email-In feature allows entities to receive documents via email using unique, entity-specific addresses like `<EMAIL>`. Emails sent to these addresses automatically create inbox documents, store attachments in Supabase Storage, deduplicate by content hash, and enqueue for AI extraction.

## Architecture

### Components

1. **Database Layer** - New tables for aliases, message audit, and sender allowlists
2. **BFF Webhook Endpoint** - Processes inbound emails from email providers
3. **BFF Management APIs** - Entity alias and allowlist management
4. **Email Providers** - Postmark, Mailgun, SES, or test provider support

### Security Model

- **RLS Enforcement** - All entity data access controlled by entity membership
- **Webhook Signatures** - Provider-specific signature verification for security
- **Feature Flags** - `EmailInEnabled` per entity for controlled rollout
- **Sender Allowlists** - Optional email pattern filtering per entity

## Email Providers

### Supported Providers

#### Postmark
- Webhook signature: `X-Postmark-Signature` (HMAC SHA256)
- Payload format: Postmark Inbound Webhook JSON
- Attachments: Base64 inline content

#### Mailgun  
- Webhook signature: `X-Mailgun-Timestamp`, `X-Mailgun-Token`, `X-Mailgun-Signature`
- Payload format: Mailgun Inbound Webhook JSON
- Attachments: URLs to fetch content

#### Test Provider (Development)
- No signature verification required
- Custom JSON format for testing
- Base64 inline attachments

### Configuration

Environment variables in `apps/bff/.env`:

```env
EMAIL_IN_PROVIDER=postmark|mailgun|ses|test
EMAIL_IN_SIGNING_SECRET=webhook-verification-secret
EMAIL_IN_DOMAIN=in.ledgerly.app
MAX_ATTACHMENTS=10
MAX_ATTACHMENT_MB=25
```

## Database Schema

### inbound_aliases
Maps email localparts to entities:
- `localpart` - Email local part (e.g., `inbox_acme_3f9a`)
- `entity_id` - References `entities(id)`
- `enabled` - Whether alias accepts emails

### inbound_messages  
Audit trail and idempotency:
- `provider` - Email provider (`postmark`, `mailgun`, `ses`, `test`)
- `message_id` - Provider message ID for deduplication
- `status` - `accepted`, `rejected`, `processed`, `failed`
- `attachments` - JSON metadata of processed attachments

### inbound_sender_allowlist
Per-entity sender filtering:
- `entity_id` - References `entities(id)`
- `email_like` - SQL LIKE pattern (e.g., `%@trusted-vendor.com`)

## API Endpoints

### Webhook Endpoint (Public)
```
POST /webhooks/email-in
```
- Verifies provider webhook signature
- Resolves entity via email alias
- Processes attachments and creates documents
- Enqueues AI extraction for supported file types

### Alias Management (Authenticated)
```
GET /entities/:entityId/email/aliases
POST /entities/:entityId/email/aliases
PATCH /entities/:entityId/email/aliases/:id
```

### Allowlist Management (Authenticated)
```
GET /entities/:entityId/email/allowlist
POST /entities/:entityId/email/allowlist  
DELETE /entities/:entityId/email/allowlist
```

## Email Processing Workflow

1. **Email Reception** - Provider forwards email to webhook endpoint
2. **Signature Verification** - Verify webhook authenticity 
3. **Alias Resolution** - Parse recipient to find target entity
4. **Feature Flag Check** - Ensure `EmailInEnabled` is true
5. **Sender Verification** - Check against allowlist if configured
6. **Attachment Processing**:
   - Validate file types and sizes
   - Compute SHA256 hash for deduplication  
   - Upload to Supabase Storage
   - Create `inbox_documents` record
   - Special handling for UBL XML (immediate extraction)
   - Enqueue non-UBL files for AI processing
7. **Audit Logging** - Record message processing status

## File Processing

### Supported File Types
- `application/pdf` - PDF documents
- `image/jpeg`, `image/png`, `image/tiff` - Image receipts/invoices
- `application/xml`, `text/xml` - UBL electronic invoices

### Storage Strategy
Files stored in Supabase Storage at:
```
inbox/{entityId}/{YYYY}/{MM}/{hash}.{ext}
```

### Deduplication
Files deduplicated by SHA256 hash using existing `documents` table constraint `uq_documents_entity_filehash`.

### UBL Handling
XML files containing UBL namespace are:
- Immediately marked as `status='extracted'`
- Parsed for structured data (future enhancement)
- Skip AI extraction queue

## Local Development

### Test Provider
Use `EMAIL_IN_PROVIDER=test` for local development:
- No signature verification required
- Custom JSON payload format
- Test fixtures provided in `apps/bff/test/fixtures/`

### MailHog Integration (Optional)
Add MailHog SMTP server to docker-compose for full email testing:
```yaml
services:
  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
```

### Testing Script
```bash
# Send test webhook payload
curl -X POST http://localhost:3001/webhooks/email-in \
  -H "Content-Type: application/json" \
  -d @apps/bff/test/fixtures/test-inbound.json
```

## Production Deployment

### DNS Configuration
Set up MX record for `in.ledgerly.app`:
```
in.ledgerly.app. MX 10 inbound.postmarkapp.com.
```

### Provider Setup

#### Postmark
1. Add `in.ledgerly.app` domain to Postmark
2. Configure inbound webhook URL: `https://api.ledgerly.app/webhooks/email-in`
3. Set webhook secret in `EMAIL_IN_SIGNING_SECRET`

#### Mailgun
1. Add `in.ledgerly.app` domain to Mailgun
2. Configure route: `match_recipient(".*@in.ledgerly.app") forward("https://api.ledgerly.app/webhooks/email-in")`
3. Set webhook secret for signature verification

### Rate Limiting
Consider implementing rate limiting per:
- IP address (webhook source)
- Entity (prevent abuse)
- Email alias (prevent spam)

### Monitoring
Monitor key metrics:
- Webhook signature failures (security)
- Processing errors by entity
- Storage usage growth
- Email volume per entity

## Security Considerations

### Webhook Security
- All providers require signature verification (except test mode)
- Failed signature attempts should be logged and monitored
- Consider IP allowlisting for additional security

### File Upload Security  
- File type restrictions enforced
- File size limits per attachment and total
- Virus scanning integration (future enhancement)

### Access Control
- All entity data protected by RLS policies
- Webhook processing uses service role for writes only
- User management endpoints enforce membership roles

## Feature Flag Control

The `EmailInEnabled` feature flag provides:
- **Dark Launch** - Deploy without enabling
- **Gradual Rollout** - Enable per entity basis  
- **Emergency Disable** - Quick disable if issues arise
- **A/B Testing** - Compare adoption rates

## Future Enhancements

### Virus Scanning
Integrate ClamAV or similar for attachment scanning:
```typescript
// Pseudo-code for virus scanning
const scanResult = await virusScanner.scan(fileBuffer)
if (scanResult.infected) {
  await rejectMessage('Virus detected')
}
```

### Peppol Integration
Add Peppol Access Point for B2B electronic invoicing:
- Same document processing pipeline
- Handle Peppol UBL format
- AS4 protocol support

### Advanced Parsing
Enhanced UBL parsing for immediate data extraction:
- Full UBL 2.1 support
- Extract line items, VAT details
- Auto-suggest account mappings

### Billing Integration
Tie email-in usage to subscription tiers:
- Limit number of aliases per plan
- Monthly inbound document quotas
- Usage-based billing metrics

## Testing

### Unit Tests
- Webhook signature verification
- Attachment processing logic
- RLS policy enforcement
- Error handling scenarios

### Integration Tests  
- End-to-end email processing
- Provider webhook simulation
- Database constraint validation
- Storage upload verification

### Load Testing
- High-volume email processing
- Concurrent webhook handling
- Storage performance under load
- Database performance with large attachment volumes

## Migration Strategy

### Phase 1: Infrastructure
1. Deploy database migration
2. Deploy BFF webhook endpoint (disabled)
3. Run pgTAP tests to verify RLS

### Phase 2: Internal Testing
1. Enable `EmailInEnabled` for internal entity
2. Use test provider for email simulation
3. Verify end-to-end processing

### Phase 3: Production Pilot
1. Configure production email provider
2. Set up DNS and webhook endpoints
3. Enable for select pilot customers
4. Monitor processing metrics

### Phase 4: General Availability
1. Enable feature flag for all entities
2. Update documentation and UI
3. Monitor adoption and performance
4. Plan future enhancements

## Support Scenarios

### Common Issues
1. **Email Not Processing** - Check alias exists and enabled
2. **Attachments Missing** - Verify file type and size limits
3. **Duplicate Documents** - Hash-based deduplication working correctly
4. **Webhook Failures** - Signature verification or provider config issues

### Troubleshooting
- Check `inbound_messages` table for processing status
- Review BFF logs for detailed error messages
- Verify feature flag and allowlist configuration
- Test webhook endpoint directly with fixtures