# Enhanced Post-Authentication User Flow

## Overview

The BelBooks application now features a sophisticated post-authentication routing system that automatically directs users to the appropriate onboarding experience based on their account creation method and invitation context.

## Flow Types

### 1. Primary Flow - New Company Creation
**Trigger**: User creates a new account without invitation context
**Path**: Sign up → Authentication → Company creation onboarding
**User Role**: Owner/Admin of new company

### 2. Secondary Flow - Existing Company Invitation
**Trigger**: User follows an invitation link
**Path**: Invitation link → Authentication → Automatic company assignment
**User Role**: Assigned role from invitation (non-admin)

## Technical Implementation

### Authentication Callback Logic (`/auth/callback/page.tsx`)

The enhanced callback logic now:
1. **Detects invitation context** from URL parameters (`invite_token`)
2. **Checks user's tenant status** (existing vs. new user)
3. **Routes appropriately** based on context:
   - New user + invitation → `/invites/{token}`
   - New user + no invitation → `/onboarding`
   - Existing user + invitation → `/invites/{token}`
   - Existing user + no invitation → dashboard

### Google OAuth Integration

Google OAuth now preserves invitation context by:
- Accepting `inviteToken` prop in `GoogleSignInButton`
- Building redirect URLs with invitation parameters
- Maintaining context through OAuth flow

### Invitation Landing Page (`/invites/[token]/page.tsx`)

Enhanced to handle both authenticated and unauthenticated users:
- **Unauthenticated users**: Shows sign-in options with invitation context
- **Authenticated users**: Shows direct invitation acceptance
- **Expired invitations**: Clear error messaging

## User Experience Flows

### New User Without Invitation
1. Visit `/signup` or `/login`
2. Choose Google OAuth or email/password
3. Complete authentication
4. Redirect to `/onboarding` (company creation)
5. Set up new company as owner

### New User With Invitation
1. Click invitation link `/invites/{token}`
2. See invitation details and sign-in options
3. Choose authentication method (preserves invitation context)
4. Complete authentication
5. Automatically accept invitation and join company

### Existing User With Invitation
1. Click invitation link `/invites/{token}`
2. If authenticated: Direct invitation acceptance
3. If not authenticated: Sign in with context preserved
4. Accept invitation and gain access to new company

## URL Parameter Handling

### Invitation Context Preservation
- `invite_token` parameter preserved through authentication flow
- Login/signup pages detect invitation context from `redirectTo` parameter
- Google OAuth maintains context through redirect URLs

### Example URLs
```
# Direct invitation link
/invites/abc123-def456-ghi789

# Login with invitation context
/login?redirectTo=/auth/callback?invite_token=abc123-def456-ghi789

# Signup with invitation context  
/signup?redirectTo=/auth/callback?invite_token=abc123-def456-ghi789
```

## Security Considerations

- **CSRF Protection**: All invitation acceptance requires CSRF tokens
- **Token Validation**: Invitation tokens are validated server-side
- **Expiration Handling**: Expired invitations show appropriate messaging
- **Authentication Required**: All invitation acceptance requires authentication

## Error Handling

### Edge Cases Covered
- Expired invitations
- Invalid invitation tokens
- Authentication failures during invitation flow
- Network errors during invitation acceptance
- Missing tenant data

### User-Friendly Messaging
- Clear distinction between "create company" vs "join company"
- Helpful error messages for expired/invalid invitations
- Guidance for users who can't find their invitation

## Testing Scenarios

### Happy Paths
1. ✅ New user creates account → company creation
2. ✅ New user follows invitation → joins existing company
3. ✅ Existing user follows invitation → joins additional company
4. ✅ Google OAuth preserves invitation context

### Error Paths
1. ✅ Expired invitation handling
2. ✅ Invalid invitation token handling
3. ✅ Authentication failure recovery
4. ✅ Network error handling

## Benefits

### For Users
- **Seamless experience**: No manual selection between flows
- **Clear context**: Always know if creating or joining
- **Flexible authentication**: Google OAuth or email/password
- **Error recovery**: Clear guidance when things go wrong

### For Administrators
- **Reliable invitations**: Context preserved through authentication
- **Security**: Proper validation and CSRF protection
- **Monitoring**: Security events logged for invitation flows
- **Flexibility**: Works with all authentication methods

## Future Enhancements

### Potential Improvements
- **Email notifications**: Confirm invitation acceptance
- **Bulk invitations**: Invite multiple users at once
- **Role templates**: Predefined role sets for common scenarios
- **Invitation analytics**: Track invitation success rates
