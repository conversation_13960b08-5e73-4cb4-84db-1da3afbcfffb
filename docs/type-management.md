# Type Management Strategy

This document defines how TypeScript types are generated, managed, and kept consistent across environments to minimize TS/ESLint errors and rework.

## 🎯 Core Principle

**CI is the single source of truth for types.** Local development generates types for speed, but only CI commits the canonical `supabase.ts` to the repository.

## 📋 Type Generation Workflows

### Local Development (LLM Coders & Contributors)
```bash
pnpm db:types
# Uses: supabase gen types typescript --local
# Generates from: Local Supabase (localhost:54322)  
# Output: packages/types/src/supabase.ts (local only)
```

**Purpose**: Fast iteration and parallel development without blocking others.

### Staging/CI (Maintainers & Automated)
```bash
pnpm db:types:staging  
# Uses: supabase gen types typescript --linked
# Generates from: Remote staging (ledgerly-be-staging)
# Output: packages/types/src/supabase.ts (canonical)
```

**Purpose**: Single source of truth that matches deployed staging environment.

### Production
Uses the same types as staging (schemas should be identical).

## 🛡️ Guardrails to Minimize Errors

### 1. Lock Down the Generated File

**Problem**: Accidentally committing local types that diverge from staging.

**Solutions**:
- CODEOWNERS protection requiring maintainer approval
- Pre-commit hook blocking local-generated files
- CI diff-gate ensuring deterministic generation
- Merge strategy to reduce conflicts

### 2. Keep Lint Noise to Zero

**Problem**: ESLint/Prettier complaining about generated code.

**Solution**: Ignore generated files in linting:
```bash
# .eslintignore and .prettierignore
packages/types/src/supabase.ts
```

### 3. Provide Typed Client Wrappers

**Problem**: LLMs making query mistakes with raw Supabase client.

**Solution**: Centralized client with domain-specific helpers:
```typescript
// ✅ Use helpers
import { listUserEntities } from '@belbooks/dal'

// ❌ Don't use raw client
import { supabase } from '@supabase/supabase-js'
```

### 4. Schema Evolution Discipline (Expand-Contract Pattern)

**Problem**: Breaking changes causing type errors across branches.

**Process**:
1. **Expand**: Add new columns/tables (keep old ones working)
2. **Switch**: Migrate code to use new schema  
3. **Contract**: Remove old columns after all code switched

### 5. Self-Consistent PRs  

**Problem**: Code using new fields before migration exists.

**Rule**: Each PR using new schema must include the SQL migration.

## 📁 File Protection Setup

### .gitattributes
```
# Reduce merge conflicts on generated file
packages/types/src/supabase.ts merge=ours
```

### CODEOWNERS
```
# Only maintainers can approve changes to generated types
/packages/types/src/supabase.ts @maintainers
```

### .eslintignore
```
# Don't lint generated code
packages/types/src/supabase.ts
node_modules/
```

### .prettierignore  
```
# Don't format generated code
packages/types/src/supabase.ts
node_modules/
```

## 🏗️ Client Architecture

### Centralized Database Client
```typescript
// packages/dal/src/client.ts
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types/src/supabase'

export const createSupabaseClient = (
  url: string,
  key: string
) => createClient<Database>(url, key)
```

### Domain-Specific Helpers
```typescript
// packages/dal/src/tenants.ts  
export async function listUserTenants(userId: string) {
  return db.from('v_user_tenants').select('*').eq('user_id', userId)
}

// packages/dal/src/entities.ts
export async function listUserEntities(userId: string) {
  return db.from('v_user_entities').select('*').eq('user_id', userId)
}
```

## 🚀 CI/CD Integration

### Type Generation Pipeline
1. Apply migrations to staging
2. Run `pnpm db:types:staging` 
3. Git diff on `supabase.ts`
4. Fail if uncommitted changes detected
5. Commit updated types if changed
6. Run TypeScript compilation
7. Run ESLint with type-aware rules

### Pre-commit Hooks
```bash
#!/bin/bash
# Block local-generated types from being committed
if grep -q "Generated from.*local" packages/types/src/supabase.ts 2>/dev/null; then
  echo "❌ Blocking commit: supabase.ts was generated locally"
  echo "   Run 'git checkout packages/types/src/supabase.ts' to restore"
  exit 1
fi
```

## 🎯 Best Practices for LLM Coders

### ✅ Do This
- Use `pnpm db:types` for local development
- Import from `@ledgerly/dal` helpers, not raw Supabase
- Include SQL migration in PR when using new schema
- Test that code compiles against current main branch types
- Use expand-and-contract for schema changes

### ❌ Don't Do This  
- Commit locally-generated `supabase.ts`
- Use raw `supabase.from()` calls in application code
- Add new fields without migration in same PR
- Edit the generated `supabase.ts` manually
- Break backwards compatibility in schema changes

## 🔍 Troubleshooting

### "Type errors after schema changes"
```bash
# Regenerate from local DB
pnpm db:types

# If still failing, check migration was applied
psql $DATABASE_URL -f packages/db/migrations/all.sql
```

### "CI failing on type mismatch" 
- Your PR needs to include the SQL migration
- Or your local schema diverged from staging
- Rebase on latest main and reapply your migration

### "Merge conflicts in supabase.ts"
- Use the merge strategy: `git checkout --ours packages/types/src/supabase.ts`
- Let CI regenerate the canonical version

### "ESLint complaining about generated file"
- Check `.eslintignore` includes `packages/types/src/supabase.ts`
- Use `--ignore-path .eslintignore` if running ESLint manually

## 📊 Success Metrics

This type management strategy succeeds when:
- ✅ Zero manual type conflicts in PRs
- ✅ LLM-generated code compiles on first try  
- ✅ No "works on my machine" type issues
- ✅ Fast local development iteration
- ✅ Deterministic CI builds
- ✅ Clean git history for generated files

## 🔧 Implementation Checklist

- [ ] Add file protection (CODEOWNERS, .gitattributes)
- [ ] Configure linting ignores
- [ ] Create typed client wrappers
- [ ] Set up pre-commit hooks
- [ ] Configure CI type generation pipeline
- [ ] Document expand-and-contract workflow
- [ ] Train team on new practices

---

**Remember**: Local types for speed, CI types for truth.