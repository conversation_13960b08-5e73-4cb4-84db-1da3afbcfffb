# Supabase Local Development Configuration
# This config is for local development only using Supabase CLI
# Remote projects (staging/prod) are managed separately via CI/CD

[project]
# Local project name - not linked to remote Supabase projects
id = "ledgerly-local-dev"
name = "ledgerly-local-dev"

[db]
# Local Postgres port (avoid conflicts with system Postgres)
port = 54322
# Enable extensions for local development
major_version = 17

[studio]
# Supabase Studio local port
port = 54323

[auth]
# Local auth settings
enabled = true
# Email confirmation disabled for local dev
enable_confirmations = false
# Magic links enabled for passwordless auth
enable_email_signup = true

[local.api]
# Local API port
port = 54321

[local.db]
# Additional local database settings
port = 54322

[local.storage]
# Local storage settings
enabled = true

[local.realtime]
# Local realtime settings  
enabled = true

[local.analytics]
# Disable analytics for local dev
enabled = false