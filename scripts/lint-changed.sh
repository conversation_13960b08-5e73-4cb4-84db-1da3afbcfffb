#!/bin/bash
# <PERSON><PERSON> only changed files in PRs to prevent new warnings without failing on old ones

set -e

echo "🔍 <PERSON><PERSON> changed files..."

# Get changed files compared to main branch
CHANGED=$(git diff --name-only origin/main...HEAD | grep -E '\.(ts|tsx|js|jsx)$' || true)

if [ -z "$CHANGED" ]; then
  echo "✅ No JavaScript/TypeScript files changed"
  exit 0
fi

echo "📝 Changed files:"
echo "$CHANGED" | sed 's/^/  - /'

# Run eslint on changed files with zero tolerance for new warnings
echo "🧹 Running ESLint on changed files..."
pnpm eslint --max-warnings=0 --cache $CHANGED

echo "✅ All changed files pass lint checks!"