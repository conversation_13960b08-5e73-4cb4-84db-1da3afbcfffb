# Git Worktrees Setup for BelBooks

This repository is configured for parallel development using Git worktrees, allowing multiple features to be developed simultaneously without conflicts.

## 🏗️ Structure

```
belbooks/                    # Main worktree (this directory)
├── bin/                    # Worktree management scripts
│   ├── new-track          # Create new feature worktree
│   ├── sync-all          # Sync all worktrees with main
│   └── cleanup-track     # Remove completed worktree
├── ...                    # Your main codebase
../wt/                     # Feature worktrees (sibling directory)
├── track-auth/           # Auth feature development
├── track-inbox/          # Inbox AI feature development
└── track-bank/           # Bank integration development
```

## 🚀 Quick Start

### 1. Create a New Feature Track

```bash
# From the main belbooks directory
./bin/new-track auth
./bin/new-track inbox-e2e
./bin/new-track bank-integration
```

This creates a new worktree at `../wt/track-<name>/` with:
- New branch `feat/track-<name>`
- Dependencies installed (pnpm, uv)
- Environment file `.env.local` copied from `.env.example`
- Remote branch created and tracked

### 2. Work on Your Track

```bash
# Switch to your track
cd ../wt/track-auth

# Start development
supabase start
pnpm -w dev

# Make changes and commit
git add -A
git commit -m "feat(auth): implement login flow"
git push
```

### 3. Keep Everything Synced

```bash
# From main ledgerly directory
./bin/sync-all
```

This updates main and rebases all feature tracks on the latest changes.

### 4. Clean Up Completed Work

```bash
# From main ledgerly directory
./bin/cleanup-track auth
```

This removes the worktree, local branch, and remote branch.

## 🔧 Development Workflow

### Starting Work

1. **You start here**: Current `ledgerly/` directory (main worktree)
2. **Create tracks**: Use `./bin/new-track <name>` to create feature worktrees
3. **Switch to track**: `cd ../wt/track-<name>/` to work on features
4. **Return to main**: `cd -` or `cd ~/Documents/Coding/ledgerly/`

### Running Services

⚠️ **Important**: Only run one Supabase instance at a time (fixed local ports)

```bash
# In your active track (e.g., ../wt/track-auth/)
supabase start
pnpm -w dev
```

For parallel development, edit `.env.local` in one track:
```env
PORT=3001
BFF_PORT=4001  
WORKER_PORT=8001
```

### Directory Ownership Strategy

To minimize conflicts, assign clear ownership:

- **Auth Track**: `apps/web/(auth|layout)`, auth routes in `apps/bff`
- **Inbox Track**: `apps/web/inbox`, `apps/bff/inbox`, `apps/workers-py/*`
- **Bank Track**: `apps/web/bank`, `packages/domain-bank`, bank DB tables
- **VAT Track**: UI components and calculation logic only

**⚠️ Hotspot Files** (coordinate before touching):
- `packages/types/src/contracts.ts`
- `packages/db/migrations/**`
- Root configuration files

### Database Migrations

- Only one track should add migrations at a time
- Use timestamped, forward-only files
- If conflicts occur during rebase, create a new migration file
- Keep `/packages/db` under CODEOWNERS for review

## 📋 Command Reference

### Management Scripts

```bash
# Create new track worktree
./bin/new-track <track-name> [base-branch]

# Sync all tracks with main
./bin/sync-all

# Remove completed track
./bin/cleanup-track <track-name>
```

### Git Worktree Commands

```bash
# List all worktrees
git worktree list

# Create manually (if needed)
git worktree add ../wt/track-name -b feat/track-name origin/main

# Remove manually
git worktree remove ../wt/track-name
```

### Daily Workflow

```bash
# Check status across all tracks
for d in ../wt/*; do echo "=== $d ===" && git -C "$d" status -s; done

# Update everything
./bin/sync-all

# Push all uncommitted work
for d in ../wt/*; do git -C "$d" push; done
```

## 🎯 Best Practices

### 1. Small, Frequent Commits
```bash
git add -A
git commit -m "feat(auth): add form validation"
git push
```

### 2. Stay Fresh
Run `./bin/sync-all` daily to avoid large conflicts.

### 3. Early PRs
Open draft PRs early for CI feedback and collaboration.

### 4. Isolated Testing
Each worktree runs independently - perfect for thorough testing.

### 5. Clean Regularly
Remove merged tracks with `./bin/cleanup-track <name>`.

## 🐛 Troubleshooting

### Merge Conflicts During Sync
```bash
cd ../wt/track-problematic
# Fix conflicts in files
git add .
git rebase --continue
```

### Port Conflicts
Edit `.env.local` in conflicting worktree:
```env
PORT=3001
BFF_PORT=4001
WORKER_PORT=8001
```

### Worktree Won't Remove
```bash
git worktree remove --force ../wt/track-name
```

### Reset Corrupted Worktree
```bash
./bin/cleanup-track track-name
./bin/new-track track-name
```

## 📍 Navigation

- **Main development**: Stay in `ledgerly/` directory
- **Feature work**: Switch to `../wt/track-<name>/`
- **Scripts**: Always run from `ledgerly/` directory
- **Return home**: `cd ~/Documents/Coding/ledgerly/`

---

**Ready for parallel development! 🚀**

Provide your issues and I'll help create the appropriate tracks for each one.