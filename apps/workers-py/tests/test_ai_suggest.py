"""Tests for AI suggestion service."""

import hashlib
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import numpy as np
import pytest

from workers_py.models import (
    ExtractionResult,
    InvoiceInfo,
    LineItem,
    SupplierInfo,
    VatRate,
)
from workers_py.services.ai_suggest import (
    AISuggestService,
    OpenAIEmbeddingProvider,
    StubEmbeddingProvider,
    create_ai_suggest_service,
)


class TestStubEmbeddingProvider:
    """Test the deterministic stub embedding provider."""

    @pytest.mark.asyncio
    async def test_deterministic_embeddings(self):
        provider = StubEmbeddingProvider()

        # Same text should produce same embedding
        text = "test invoice signature"
        embedding1 = await provider.embed_text(text)
        embedding2 = await provider.embed_text(text)

        assert embedding1 == embedding2
        assert len(embedding1) == 1536
        assert all(-1 <= x <= 1 for x in embedding1)

    @pytest.mark.asyncio
    async def test_different_text_different_embeddings(self):
        provider = StubEmbeddingProvider()

        embedding1 = await provider.embed_text("text one")
        embedding2 = await provider.embed_text("text two")

        assert embedding1 != embedding2


class TestOpenAIEmbeddingProvider:
    """Test OpenAI embedding provider with mocking."""

    @pytest.mark.asyncio
    async def test_successful_embedding(self):
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1] * 1536)]

        with patch("workers_py.services.ai_suggest.AsyncOpenAI") as mock_openai:
            mock_client = Mock()
            mock_client.embeddings.create = AsyncMock(return_value=mock_response)
            mock_openai.return_value = mock_client

            provider = OpenAIEmbeddingProvider(api_key="test-key")
            embedding = await provider.embed_text("test text")

            assert len(embedding) == 1536
            assert all(x == 0.1 for x in embedding)

    @pytest.mark.asyncio
    async def test_api_error_handling(self):
        with patch("workers_py.services.ai_suggest.AsyncOpenAI") as mock_openai:
            mock_client = Mock()
            mock_client.embeddings.create = AsyncMock(
                side_effect=Exception("API Error")
            )
            mock_openai.return_value = mock_client

            provider = OpenAIEmbeddingProvider(api_key="test-key")

            with pytest.raises(Exception, match="API Error"):
                await provider.embed_text("test text")


class TestAISuggestService:
    """Test the main AI suggestion service."""

    def setup_method(self):
        self.mock_db_pool = Mock()
        self.stub_provider = StubEmbeddingProvider()
        self.service = AISuggestService(
            embedding_provider=self.stub_provider, db_pool=self.mock_db_pool
        )

    def create_mock_extraction(self) -> ExtractionResult:
        """Create a mock extraction result for testing."""
        return ExtractionResult(
            supplier=SupplierInfo(name="ACME Corp", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="INV-001",
                issue_date="2024-01-15",
                net="1000.00",
                vat="210.00",
                gross="1210.00",
            ),
            lines=[
                LineItem(
                    description="Office supplies",
                    quantity="2",
                    unit_price="500.00",
                    vat_rate=VatRate.STANDARD,
                )
            ],
            confidence=0.95,
        )

    def test_build_signature(self):
        """Test signature building from extraction."""
        extraction = self.create_mock_extraction()

        signature = self.service.build_signature(extraction)

        assert "supplier: acme corp" in signature
        assert "vat: be0123456789" in signature
        assert "lines: office supplies x2 @500.00 21%" in signature
        assert "total: 1210.00" in signature
        assert len(signature) <= 2000  # Should respect length limit

    def test_build_signature_with_overrides(self):
        """Test signature building with supplier overrides."""
        extraction = self.create_mock_extraction()

        signature = self.service.build_signature(
            extraction, supplier_name="Custom Corp", supplier_vat="BE9999999999"
        )

        assert "supplier: custom corp" in signature
        assert "vat: be9999999999" in signature

    def test_build_signature_normalization(self):
        """Test signature normalization (lowercase, whitespace collapse)."""
        extraction = ExtractionResult(
            supplier=SupplierInfo(name="  ACME   Corp  ", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="INV-001",
                issue_date="2024-01-15",
                net="826.45",
                vat="173.55",
                gross="1000.00",
            ),
            lines=[
                LineItem(
                    description="  Office    Supplies  ",
                    quantity="1",
                    unit_price="1000.00",
                    vat_rate=VatRate.STANDARD,
                )
            ],
            confidence=0.9,
        )

        signature = self.service.build_signature(extraction)

        assert "acme corp" in signature  # Normalized
        assert "office supplies" in signature  # Normalized
        assert "  " not in signature  # No double spaces

    def test_build_signature_limits_lines(self):
        """Test that signature building limits to 5 lines."""
        many_lines = [
            LineItem(
                description=f"Item {i}",
                quantity="1",
                unit_price="100.00",
                vat_rate=VatRate.STANDARD,
            )
            for i in range(10)  # 10 lines
        ]

        extraction = ExtractionResult(
            supplier=SupplierInfo(name="Test Corp"),
            invoice=InvoiceInfo(
                number="INV-001",
                issue_date="2024-01-15",
                net="826.45",
                vat="173.55",
                gross="1000.00",
            ),
            lines=many_lines,
            confidence=0.9,
        )

        signature = self.service.build_signature(extraction)

        # Should only contain first 5 items
        assert "item 0" in signature
        assert "item 4" in signature
        assert "item 9" not in signature

    def test_compute_text_hash(self):
        """Test text hash computation."""
        text = "test signature text"
        expected_hash = hashlib.sha256(text.encode("utf-8")).hexdigest()

        computed_hash = self.service.compute_text_hash(text)

        assert computed_hash == expected_hash

    @pytest.mark.asyncio
    async def test_create_coding_event(self):
        """Test coding event creation."""
        mock_conn = Mock()
        mock_conn.fetchval = AsyncMock(return_value=12345)
        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            extraction = self.create_mock_extraction()

            coding_event_id = await self.service.create_coding_event(
                entity_id=1001,
                document_id=2002,
                extraction=extraction,
                account_id=6000,
                vat_code_id=21,
            )

            assert coding_event_id == 12345
            mock_conn.fetchval.assert_called_once()

            # Verify SQL parameters (0=SQL, 1=entity_id, 2=document_id, 3=supplier_name, 4=supplier_vat, 5=signature_text, 6=account_id, 7=vat_code_id, 8=journal_id)
            call_args = mock_conn.fetchval.call_args
            assert call_args[0][1] == 1001  # entity_id
            assert call_args[0][2] == 2002  # document_id
            assert call_args[0][6] == 6000  # account_id (6th parameter)
            assert call_args[0][7] == 21  # vat_code_id (7th parameter)

    @pytest.mark.asyncio
    async def test_index_coding_event_success(self):
        """Test successful embedding generation and storage."""
        # Mock database responses
        mock_conn = Mock()
        mock_conn.fetchrow = AsyncMock(
            return_value={"entity_id": 1001, "signature_text": "test signature"}
        )
        mock_conn.fetchval = AsyncMock(return_value=None)  # No existing embedding
        mock_conn.execute = AsyncMock()

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            result = await self.service.index_coding_event(12345)

            assert result is True
            mock_conn.execute.assert_called_once()  # Should insert embedding

    @pytest.mark.asyncio
    async def test_index_coding_event_already_exists(self):
        """Test handling of already-existing embeddings."""
        mock_conn = Mock()
        mock_conn.fetchrow = AsyncMock(
            return_value={"entity_id": 1001, "signature_text": "test signature"}
        )
        mock_conn.fetchval = AsyncMock(return_value=99999)  # Existing embedding

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            result = await self.service.index_coding_event(12345)

            assert result is True  # Should succeed but not create new embedding
            mock_conn.execute.assert_not_called()  # Should not insert

    @pytest.mark.asyncio
    async def test_index_coding_event_not_found(self):
        """Test handling of non-existent coding events."""
        mock_conn = Mock()
        mock_conn.fetchrow = AsyncMock(return_value=None)  # Event not found

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            result = await self.service.index_coding_event(99999)

            assert result is False

    @pytest.mark.asyncio
    async def test_reindex_entity(self):
        """Test entity reindexing."""
        # Mock events without embeddings
        mock_conn = Mock()
        mock_conn.fetch = AsyncMock(
            return_value=[{"id": 1001}, {"id": 1002}, {"id": 1003}]
        )

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            # Mock successful indexing
            with patch.object(
                self.service, "index_coding_event", return_value=True
            ) as mock_index:
                processed = await self.service.reindex_entity(1001, limit=50)

                assert processed == 3
                assert mock_index.call_count == 3

    @pytest.mark.asyncio
    async def test_get_suggestions(self):
        """Test getting AI suggestions."""
        mock_conn = Mock()
        mock_suggestions = [
            {
                "account_id": 6000,
                "vat_code_id": 21,
                "score": 0.85,
                "avg_sim": 0.9,
                "votes": 5,
                "top_examples": [101, 102, 103],
            },
            {
                "account_id": 6132,
                "vat_code_id": 21,
                "score": 0.65,
                "avg_sim": 0.7,
                "votes": 2,
                "top_examples": [201, 202],
            },
        ]
        from datetime import date

        mock_examples = [
            {"supplier_name": "ACME Corp", "confirmed_date": date(2024, 1, 1)},
            {"supplier_name": "ACME Corp", "confirmed_date": date(2024, 1, 15)},
        ]

        mock_conn.fetch = AsyncMock(
            side_effect=[mock_suggestions, mock_examples, mock_examples]
        )

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        extraction = self.create_mock_extraction()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            suggestions = await self.service.get_suggestions(
                entity_id=1001,
                extraction=extraction,
                supplier_name="ACME Corp",
                supplier_vat="BE0123456789",  # Add VAT to trigger supplier name in explanation
            )

            assert len(suggestions) == 2
            assert suggestions[0]["account_id"] == 6000
            assert suggestions[0]["confidence"] == "high"  # 0.85 score -> high
            assert "ACME Corp" in suggestions[0]["explanation"]
            assert suggestions[1]["confidence"] == "high"  # 0.65 score -> high (> 0.6)

    @pytest.mark.asyncio
    async def test_get_suggestions_no_neighbors(self):
        """Test handling when no similar documents found."""
        mock_conn = Mock()
        mock_conn.fetch = AsyncMock(return_value=[])  # No suggestions

        self.mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(
            return_value=mock_conn
        )
        self.mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        extraction = self.create_mock_extraction()

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            suggestions = await self.service.get_suggestions(1001, extraction)

            assert suggestions == []

    @patch.dict("os.environ", {"EMBED_PROVIDER": "stub"}, clear=False)
    def test_service_configuration_defaults(self):
        """Test service configuration with defaults."""
        service = AISuggestService()

        assert service.embedding_provider is not None
        assert service.db_pool is None

    @patch.dict("os.environ", {"EMBED_PROVIDER": "stub"})
    def test_service_configuration_stub_provider(self):
        """Test service configuration with stub provider."""
        service = AISuggestService()

        assert isinstance(service.embedding_provider, StubEmbeddingProvider)

    @patch.dict(
        "os.environ", {"EMBED_PROVIDER": "openai", "OPENAI_API_KEY": "test-key"}
    )
    def test_service_configuration_openai_provider(self):
        """Test service configuration with OpenAI provider."""
        service = AISuggestService()

        assert isinstance(service.embedding_provider, OpenAIEmbeddingProvider)


class TestServiceFactory:
    """Test the service factory function."""

    def test_create_ai_suggest_service(self):
        """Test factory function creates service correctly."""
        mock_db_pool = Mock()
        mock_provider = Mock()

        service = create_ai_suggest_service(
            db_pool=mock_db_pool, embedding_provider=mock_provider
        )

        assert service.db_pool is mock_db_pool
        assert service.embedding_provider is mock_provider


class TestIntegration:
    """Integration tests for the complete workflow."""

    @pytest.mark.asyncio
    async def test_full_workflow_simulation(self):
        """Test the complete workflow from extraction to suggestions."""
        # Create service with stub provider
        mock_db_pool = Mock()
        service = AISuggestService(
            embedding_provider=StubEmbeddingProvider(), db_pool=mock_db_pool
        )

        # Mock database for coding event creation
        mock_conn = Mock()
        mock_conn.fetchval = AsyncMock(
            side_effect=[
                12345,  # First call: coding event creation returns ID
                None,  # Second call: no existing embedding found
            ]
        )
        mock_conn.fetchrow = AsyncMock(
            return_value={
                "entity_id": 1001,
                "signature_text": "supplier: acme corp; vat: BE0123456789; lines: office supplies x2 @500.0 21%; total: 1210.0",
            }
        )
        mock_conn.fetch = AsyncMock(return_value=[])  # No existing embeddings
        mock_conn.execute = AsyncMock()

        mock_db_pool.acquire.return_value.__aenter__ = AsyncMock(return_value=mock_conn)
        mock_db_pool.acquire.return_value.__aexit__ = AsyncMock()

        extraction = ExtractionResult(
            supplier=SupplierInfo(name="ACME Corp", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="INV-001",
                issue_date="2024-01-15",
                net="1000.00",
                vat="210.00",
                gross="1210.00",
            ),
            lines=[
                LineItem(
                    description="Office supplies",
                    quantity="2",
                    unit_price="500.00",
                    vat_rate=VatRate.STANDARD,
                )
            ],
            confidence=0.95,
        )

        # Mock the register_vector function
        with patch(
            "workers_py.services.ai_suggest.register_vector", new_callable=AsyncMock
        ):
            # Step 1: Create coding event
            coding_event_id = await service.create_coding_event(
                entity_id=1001,
                document_id=2002,
                extraction=extraction,
                account_id=6000,
                vat_code_id=21,
            )

            assert coding_event_id == 12345

            # Step 2: Index the coding event
            indexed = await service.index_coding_event(coding_event_id)

            assert indexed is True

        # Verify database calls (entity_id, coding_event_id, text_hash, embedding)
        insert_call = mock_conn.execute.call_args[0]
        assert insert_call[1] == 1001  # entity_id
        assert insert_call[2] == coding_event_id  # coding_event_id
        # Skip text_hash verification since it's computed internally
        assert len(insert_call) >= 4  # Ensure all parameters are present

        # Step 3: Verify embedding array shape
        embedding_array = insert_call[4]
        assert isinstance(embedding_array, np.ndarray)
        assert embedding_array.shape == (1536,)
        assert embedding_array.dtype == np.float32
