"""Tests for Pydantic models."""

from datetime import datetime
from typing import Dict, Any

import pytest
from pydantic import ValidationError

from workers_py.models import (
    DocumentProcessRequest,
    DocumentProcessResponse,
    ProcessingOptions,
    TaskStatusResponse,
    HealthResponse,
    AIAnalysisResult,
    ProcessingMetadata,
    TaskResult,
    ErrorResponse,
    TaskStatus,
    HealthStatus,
)


class TestProcessingOptions:
    """Test ProcessingOptions model."""

    def test_processing_options_defaults(self):
        """Test ProcessingOptions with default values."""
        options = ProcessingOptions()

        assert options.ocr_language == "eng"
        assert options.extract_tables is False
        assert options.extract_images is False
        assert options.ai_categorize is True
        assert options.ai_extract_entities is True
        assert options.confidence_threshold == 0.7
        assert options.max_pages is None

    def test_processing_options_custom_values(self):
        """Test ProcessingOptions with custom values."""
        options = ProcessingOptions(
            ocr_language="spa",
            extract_tables=True,
            extract_images=True,
            ai_categorize=False,
            confidence_threshold=0.9,
            max_pages=10,
        )

        assert options.ocr_language == "spa"
        assert options.extract_tables is True
        assert options.extract_images is True
        assert options.ai_categorize is False
        assert options.confidence_threshold == 0.9
        assert options.max_pages == 10

    def test_processing_options_confidence_validation(self):
        """Test confidence threshold validation."""
        # Valid confidence values
        ProcessingOptions(confidence_threshold=0.0)
        ProcessingOptions(confidence_threshold=1.0)
        ProcessingOptions(confidence_threshold=0.5)

        # Invalid confidence values
        with pytest.raises(ValidationError):
            ProcessingOptions(confidence_threshold=-0.1)

        with pytest.raises(ValidationError):
            ProcessingOptions(confidence_threshold=1.1)

    def test_processing_options_max_pages_validation(self):
        """Test max_pages validation."""
        # Valid max_pages values
        ProcessingOptions(max_pages=1)
        ProcessingOptions(max_pages=100)
        ProcessingOptions(max_pages=None)

        # Invalid max_pages values
        with pytest.raises(ValidationError):
            ProcessingOptions(max_pages=0)

        with pytest.raises(ValidationError):
            ProcessingOptions(max_pages=-1)


class TestDocumentProcessRequest:
    """Test DocumentProcessRequest model."""

    def test_document_process_request_valid(self):
        """Test valid DocumentProcessRequest."""
        request = DocumentProcessRequest(
            entity_id="test-entity-123", file_url="https://example.com/document.pdf"
        )

        assert request.entity_id == "test-entity-123"
        assert str(request.file_url) == "https://example.com/document.pdf"
        assert request.options is None

    def test_document_process_request_with_options(self):
        """Test DocumentProcessRequest with options."""
        options = ProcessingOptions(
            ocr_language="eng", extract_tables=True, confidence_threshold=0.8
        )

        request = DocumentProcessRequest(
            entity_id="test-entity-123",
            file_url="https://example.com/document.pdf",
            options=options,
        )

        assert request.options is not None
        assert request.options.extract_tables is True

    def test_document_process_request_entity_id_validation(self):
        """Test entity_id validation."""
        # Valid entity IDs
        DocumentProcessRequest(
            entity_id="test-entity-123", file_url="https://example.com/document.pdf"
        )

        DocumentProcessRequest(
            entity_id="   test-entity   ",  # Should be stripped
            file_url="https://example.com/document.pdf",
        )

        # Invalid entity IDs
        with pytest.raises(ValidationError):
            DocumentProcessRequest(
                entity_id="", file_url="https://example.com/document.pdf"
            )

        with pytest.raises(ValidationError):
            DocumentProcessRequest(
                entity_id="  ",  # Only whitespace
                file_url="https://example.com/document.pdf",
            )

        with pytest.raises(ValidationError):
            DocumentProcessRequest(
                entity_id="ab", file_url="https://example.com/document.pdf"  # Too short
            )

    def test_document_process_request_url_validation(self):
        """Test file_url validation."""
        # Valid URLs
        DocumentProcessRequest(
            entity_id="test-entity", file_url="https://example.com/document.pdf"
        )

        DocumentProcessRequest(
            entity_id="test-entity", file_url="http://example.com/document.pdf"
        )

        # Invalid URLs
        with pytest.raises(ValidationError):
            DocumentProcessRequest(entity_id="test-entity", file_url="not-a-valid-url")

        with pytest.raises(ValidationError):
            DocumentProcessRequest(
                entity_id="test-entity",
                file_url="ftp://example.com/document.pdf",  # Not HTTP/HTTPS
            )


class TestDocumentProcessResponse:
    """Test DocumentProcessResponse model."""

    def test_document_process_response_valid(self):
        """Test valid DocumentProcessResponse."""
        response = DocumentProcessResponse(
            task_id="task-123",
            status=TaskStatus.PROCESSING,
            message="Document processing started",
        )

        assert response.task_id == "task-123"
        assert response.status == TaskStatus.PROCESSING
        assert response.message == "Document processing started"
        assert response.estimated_completion_time is None

    def test_document_process_response_with_estimate(self):
        """Test DocumentProcessResponse with completion time estimate."""
        response = DocumentProcessResponse(
            task_id="task-123",
            status=TaskStatus.PROCESSING,
            message="Processing started",
            estimated_completion_time=60,
        )

        assert response.estimated_completion_time == 60


class TestTaskStatusResponse:
    """Test TaskStatusResponse model."""

    def test_task_status_response_pending(self):
        """Test TaskStatusResponse for pending task."""
        response = TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.PENDING,
            message="Task is waiting to be processed",
        )

        assert response.task_id == "task-123"
        assert response.status == TaskStatus.PENDING
        assert response.progress is None
        assert response.result is None
        assert response.error is None

    def test_task_status_response_processing(self):
        """Test TaskStatusResponse for processing task."""
        response = TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.PROCESSING,
            message="Task is being processed",
            progress=50,
        )

        assert response.progress == 50

    def test_task_status_response_completed(self):
        """Test TaskStatusResponse for completed task."""
        # Create a minimal TaskResult
        result = TaskResult(
            entity_id="test-entity",
            file_url="https://example.com/test.pdf",
            extracted_text="Sample text",
            metadata=ProcessingMetadata(),
            ai_analysis=AIAnalysisResult(),
            processing_time=1.5,
        )

        response = TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.COMPLETED,
            message="Task completed successfully",
            progress=100,
            result=result,
        )

        assert response.status == TaskStatus.COMPLETED
        assert response.progress == 100
        assert response.result is not None
        assert response.error is None

    def test_task_status_response_failed(self):
        """Test TaskStatusResponse for failed task."""
        response = TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.FAILED,
            message="Task failed",
            error="Processing error occurred",
        )

        assert response.status == TaskStatus.FAILED
        assert response.error == "Processing error occurred"

    def test_task_status_response_progress_validation(self):
        """Test progress validation."""
        # Valid progress values
        TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.PROCESSING,
            message="Processing",
            progress=0,
        )

        TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.PROCESSING,
            message="Processing",
            progress=100,
        )

        # Invalid progress values
        with pytest.raises(ValidationError):
            TaskStatusResponse(
                task_id="task-123",
                status=TaskStatus.PROCESSING,
                message="Processing",
                progress=-1,
            )

        with pytest.raises(ValidationError):
            TaskStatusResponse(
                task_id="task-123",
                status=TaskStatus.PROCESSING,
                message="Processing",
                progress=101,
            )


class TestHealthResponse:
    """Test HealthResponse model."""

    def test_health_response_healthy(self):
        """Test healthy HealthResponse."""
        services = {"database": "connected", "redis": "connected"}

        response = HealthResponse(
            status=HealthStatus.HEALTHY, version="0.1.0", services=services
        )

        assert response.status == HealthStatus.HEALTHY
        assert response.version == "0.1.0"
        assert response.services == services
        assert isinstance(response.timestamp, datetime)
        assert response.uptime is None

    def test_health_response_with_uptime(self):
        """Test HealthResponse with uptime."""
        services = {"database": "connected"}

        response = HealthResponse(
            status=HealthStatus.HEALTHY,
            version="0.1.0",
            services=services,
            uptime=86400.0,
        )

        assert response.uptime == 86400.0

    def test_health_response_unhealthy(self):
        """Test unhealthy HealthResponse."""
        services = {"database": "disconnected", "redis": "error"}

        response = HealthResponse(
            status=HealthStatus.UNHEALTHY, version="0.1.0", services=services
        )

        assert response.status == HealthStatus.UNHEALTHY


class TestAIAnalysisResult:
    """Test AIAnalysisResult model."""

    def test_ai_analysis_result_defaults(self):
        """Test AIAnalysisResult with default values."""
        result = AIAnalysisResult()

        assert result.document_type is None
        assert result.confidence is None
        assert result.categories == []
        assert result.entities == {}
        assert result.summary is None
        assert result.key_information == {}
        assert result.language is None

    def test_ai_analysis_result_full(self):
        """Test AIAnalysisResult with all fields."""
        entities = {
            "person": ["John Doe"],
            "company": ["Acme Corp"],
            "amount": ["$1,000.00"],
        }

        key_info = {
            "invoice_number": "12345",
            "total_amount": 1000.00,
            "due_date": "2023-12-31",
        }

        result = AIAnalysisResult(
            document_type="invoice",
            confidence=0.95,
            categories=["financial", "business"],
            entities=entities,
            summary="Invoice from Acme Corp for $1,000",
            key_information=key_info,
            language="en",
        )

        assert result.document_type == "invoice"
        assert result.confidence == 0.95
        assert len(result.categories) == 2
        assert result.entities["person"] == ["John Doe"]
        assert result.key_information["invoice_number"] == "12345"

    def test_ai_analysis_result_confidence_validation(self):
        """Test confidence validation in AIAnalysisResult."""
        # Valid confidence values
        AIAnalysisResult(confidence=0.0)
        AIAnalysisResult(confidence=1.0)
        AIAnalysisResult(confidence=0.5)

        # Invalid confidence values
        with pytest.raises(ValidationError):
            AIAnalysisResult(confidence=-0.1)

        with pytest.raises(ValidationError):
            AIAnalysisResult(confidence=1.1)


class TestProcessingMetadata:
    """Test ProcessingMetadata model."""

    def test_processing_metadata_defaults(self):
        """Test ProcessingMetadata with default values."""
        metadata = ProcessingMetadata()

        assert metadata.file_size is None
        assert metadata.file_type is None
        assert metadata.page_count is None
        assert metadata.processing_time is None
        assert metadata.ocr_confidence is None
        assert metadata.extracted_images is None
        assert metadata.extracted_tables is None
        assert metadata.character_count is None
        assert metadata.word_count is None

    def test_processing_metadata_full(self):
        """Test ProcessingMetadata with all fields."""
        metadata = ProcessingMetadata(
            file_size=1024000,
            file_type="application/pdf",
            page_count=5,
            processing_time=2.5,
            ocr_confidence=95.5,
            extracted_images=3,
            extracted_tables=2,
            character_count=5000,
            word_count=800,
        )

        assert metadata.file_size == 1024000
        assert metadata.file_type == "application/pdf"
        assert metadata.page_count == 5
        assert metadata.processing_time == 2.5
        assert metadata.ocr_confidence == 95.5
        assert metadata.extracted_images == 3
        assert metadata.extracted_tables == 2
        assert metadata.character_count == 5000
        assert metadata.word_count == 800


class TestTaskResult:
    """Test TaskResult model."""

    def test_task_result_valid(self):
        """Test valid TaskResult."""
        metadata = ProcessingMetadata(file_size=1024, page_count=1)
        ai_analysis = AIAnalysisResult(document_type="invoice", confidence=0.9)

        result = TaskResult(
            entity_id="test-entity",
            file_url="https://example.com/test.pdf",
            extracted_text="Sample extracted text",
            metadata=metadata,
            ai_analysis=ai_analysis,
            processing_time=1.5,
        )

        assert result.entity_id == "test-entity"
        assert result.file_url == "https://example.com/test.pdf"
        assert result.extracted_text == "Sample extracted text"
        assert result.metadata.file_size == 1024
        assert result.ai_analysis.document_type == "invoice"
        assert result.processing_time == 1.5
        assert isinstance(result.timestamp, datetime)


class TestErrorResponse:
    """Test ErrorResponse model."""

    def test_error_response_basic(self):
        """Test basic ErrorResponse."""
        response = ErrorResponse(error="ValidationError", message="Invalid input data")

        assert response.error == "ValidationError"
        assert response.message == "Invalid input data"
        assert response.details is None
        assert isinstance(response.timestamp, datetime)

    def test_error_response_with_details(self):
        """Test ErrorResponse with details."""
        details = {"field": "entity_id", "issue": "too short"}

        response = ErrorResponse(
            error="ValidationError", message="Invalid input data", details=details
        )

        assert response.details == details


class TestModelSerialization:
    """Test model JSON serialization/deserialization."""

    def test_document_process_request_json(self):
        """Test DocumentProcessRequest JSON serialization."""
        request_data = {
            "entity_id": "test-entity-123",
            "file_url": "https://example.com/document.pdf",
            "options": {
                "ocr_language": "eng",
                "extract_tables": True,
                "confidence_threshold": 0.8,
            },
        }

        request = DocumentProcessRequest(**request_data)
        json_data = request.model_dump()

        assert json_data["entity_id"] == "test-entity-123"
        assert json_data["options"]["extract_tables"] is True

        # Test round-trip
        request2 = DocumentProcessRequest(**json_data)
        assert request2.entity_id == request.entity_id
        assert request2.options.extract_tables == request.options.extract_tables

    def test_task_status_response_json(self):
        """Test TaskStatusResponse JSON serialization."""
        response = TaskStatusResponse(
            task_id="task-123",
            status=TaskStatus.COMPLETED,
            message="Completed successfully",
            progress=100,
            created_at=datetime(2023, 12, 1, 10, 0, 0),
            updated_at=datetime(2023, 12, 1, 10, 5, 0),
        )

        json_data = response.model_dump()

        assert json_data["task_id"] == "task-123"
        assert json_data["status"] == "completed"
        assert json_data["progress"] == 100
        # Datetime should be serialized as ISO string
        assert json_data["created_at"] == "2023-12-01T10:00:00"
        assert json_data["updated_at"] == "2023-12-01T10:05:00"
