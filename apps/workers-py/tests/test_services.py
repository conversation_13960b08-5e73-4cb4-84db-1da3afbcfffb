"""Tests for service modules."""

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from PIL import Image

from workers_py.services.ocr import OCRService
from workers_py.services.pdf import PDFService
from workers_py.services.ai import AIService


class TestOCRService:
    """Test OCR service functionality."""

    def test_init(self):
        """Test OCR service initialization."""
        with patch(
            "workers_py.services.ocr.pytesseract.get_tesseract_version"
        ) as mock_version:
            mock_version.return_value = "4.1.1"

            service = OCRService()
            assert service.language == "eng"  # Default language
            assert service.confidence_threshold == 60

    def test_init_with_custom_language(self):
        """Test OCR service initialization with custom language."""
        with patch(
            "workers_py.services.ocr.pytesseract.get_tesseract_version"
        ) as mock_version:
            mock_version.return_value = "4.1.1"

            service = OCRService(language="spa")
            assert service.language == "spa"

    def test_preprocess_image(self, temp_image_file):
        """Test image preprocessing."""
        with patch("workers_py.services.ocr.pytesseract.get_tesseract_version"):
            service = OCRService()

            # Load test image
            with Image.open(temp_image_file) as image:
                processed = service.preprocess_image(image)

                assert processed is not None
                assert processed.mode == "L"  # Should be grayscale

    def test_extract_text_from_image(self, temp_image_file):
        """Test text extraction from image."""
        with patch("workers_py.services.ocr.pytesseract") as mock_tesseract:
            # Mock tesseract responses
            mock_tesseract.get_tesseract_version.return_value = "4.1.1"
            mock_tesseract.image_to_string.return_value = "Sample OCR text"
            mock_tesseract.image_to_data.return_value = {
                "conf": ["95", "90", "85"],
                "text": ["Sample", "OCR", "text"],
                "left": [10, 50, 90],
                "top": [10, 10, 10],
                "width": [30, 25, 25],
                "height": [15, 15, 15],
            }

            service = OCRService()
            result = service.extract_text_from_image(temp_image_file)

            assert result["text"] == "Sample OCR text"
            assert "metadata" in result
            assert result["metadata"]["ocr_engine"] == "tesseract"
            assert result["metadata"]["average_confidence"] > 0
            assert "words" in result

    def test_extract_text_from_pdf(self, temp_pdf_file):
        """Test text extraction from PDF using OCR."""
        with (
            patch("workers_py.services.ocr.pytesseract") as mock_tesseract,
            patch("workers_py.services.ocr.pdf2image") as mock_pdf2image,
        ):

            # Mock tesseract and pdf2image
            mock_tesseract.get_tesseract_version.return_value = "4.1.1"
            mock_tesseract.image_to_string.return_value = "PDF OCR text"
            mock_tesseract.image_to_data.return_value = {
                "conf": ["95"],
                "text": ["PDF"],
                "left": [10],
                "top": [10],
                "width": [30],
                "height": [15],
            }

            # Mock PDF to image conversion
            mock_image = MagicMock()
            mock_image.width = 100
            mock_image.height = 100
            mock_image.mode = "RGB"
            mock_image.format = "PNG"
            mock_pdf2image.convert_from_path.return_value = [mock_image]

            service = OCRService()
            result = service.extract_text_from_pdf(temp_pdf_file)

            assert "text" in result
            assert "metadata" in result
            assert result["metadata"]["ocr_engine"] == "tesseract"
            assert "pages" in result

    def test_detect_language(self, temp_image_file):
        """Test language detection."""
        with patch("workers_py.services.ocr.pytesseract") as mock_tesseract:
            mock_tesseract.get_tesseract_version.return_value = "4.1.1"
            mock_tesseract.image_to_osd.return_value = "Script: Latin\nOrientation: 0"

            service = OCRService()
            language = service.detect_language(temp_image_file)

            assert language == "eng"  # Latin script maps to English


class TestPDFService:
    """Test PDF service functionality."""

    def test_init(self):
        """Test PDF service initialization."""
        service = PDFService()
        assert ".pdf" in service.supported_formats

    def test_extract_text_and_metadata(self, temp_pdf_file):
        """Test text and metadata extraction from PDF."""
        with patch("workers_py.services.pdf.pdfplumber") as mock_pdfplumber:
            # Mock pdfplumber
            mock_page = MagicMock()
            mock_page.extract_text.return_value = "Sample PDF text"
            mock_page.width = 612
            mock_page.height = 792
            mock_page.rotation = 0
            mock_page.images = []
            mock_page.find_tables.return_value = []

            mock_pdf = MagicMock()
            mock_pdf.metadata = {"Title": "Test PDF", "Author": "Test Author"}
            mock_pdf.pages = [mock_page]

            mock_pdfplumber.open.return_value.__enter__.return_value = mock_pdf

            service = PDFService()
            result = service.extract_text_and_metadata(temp_pdf_file)

            assert "text" in result
            assert "metadata" in result
            assert result["metadata"]["extractor"] == "pdfplumber"
            assert result["metadata"]["page_count"] == 1
            assert "pages" in result
            assert "tables" in result
            assert "images" in result

    def test_is_text_based_pdf(self, temp_pdf_file):
        """Test checking if PDF has extractable text."""
        with patch("workers_py.services.pdf.pdfplumber") as mock_pdfplumber:
            # Mock PDF with text
            mock_page = MagicMock()
            mock_page.extract_text.return_value = "This PDF has text"

            mock_pdf = MagicMock()
            mock_pdf.pages = [mock_page]

            mock_pdfplumber.open.return_value.__enter__.return_value = mock_pdf

            service = PDFService()
            has_text = service.is_text_based_pdf(temp_pdf_file)

            assert has_text is True

    def test_get_pdf_info(self, temp_pdf_file):
        """Test getting basic PDF information."""
        with patch("workers_py.services.pdf.pdfplumber") as mock_pdfplumber:
            mock_page = MagicMock()
            mock_page.width = 612
            mock_page.height = 792
            mock_page.rotation = 0

            mock_pdf = MagicMock()
            mock_pdf.metadata = {
                "Title": "Test PDF",
                "CreationDate": "D:20231201120000",
            }
            mock_pdf.pages = [mock_page]

            mock_pdfplumber.open.return_value.__enter__.return_value = mock_pdf

            service = PDFService()
            info = service.get_pdf_info(temp_pdf_file)

            assert "file_path" in info
            assert "page_count" in info
            assert "metadata" in info
            assert info["title"] == "Test PDF"


class TestAIService:
    """Test AI service functionality."""

    def test_init(self):
        """Test AI service initialization."""
        with patch("workers_py.services.ai.SentenceTransformer") as mock_transformer:
            mock_transformer.return_value = MagicMock()

            service = AIService()
            assert service.model_name == "all-MiniLM-L6-v2"  # Default from settings
            assert service.confidence_threshold == 0.7

    def test_analyze_document(self, sample_document_text):
        """Test comprehensive document analysis."""
        with patch("workers_py.services.ai.SentenceTransformer") as mock_transformer:
            mock_transformer.return_value = MagicMock()

            service = AIService()
            result = service.analyze_document(sample_document_text)

            assert "document_type" in result
            assert "confidence" in result
            assert "categories" in result
            assert "entities" in result
            assert "summary" in result
            assert "key_information" in result
            assert "language" in result
            assert "sentiment" in result

    def test_classify_document_type_invoice(self, sample_document_text):
        """Test document type classification for invoice."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()
            doc_type, confidence = service._classify_document_type(sample_document_text)

            assert doc_type == "invoice"
            assert confidence > 0

    def test_extract_entities(self, sample_document_text):
        """Test entity extraction."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()
            entities = service._extract_entities(sample_document_text)

            assert isinstance(entities, dict)
            # Should extract invoice number, amounts, dates, etc.
            if "invoice_number" in entities:
                assert "12345" in entities["invoice_number"]

    def test_detect_language_english(self):
        """Test language detection for English text."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()

            english_text = "This is an English document with common English words like the and and."
            language = service._detect_language(english_text)

            assert language == "en"

    def test_generate_summary(self, sample_document_text):
        """Test summary generation."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()
            summary = service._generate_summary(sample_document_text)

            assert isinstance(summary, str)
            assert len(summary) > 0
            assert len(summary) < len(
                sample_document_text
            )  # Should be shorter than original

    def test_analyze_sentiment(self):
        """Test sentiment analysis."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()

            positive_text = "This is an excellent document with good results and successful outcomes."
            sentiment = service._analyze_sentiment(positive_text)

            assert sentiment["sentiment"] == "positive"
            assert sentiment["confidence"] > 0

    def test_analyze_document_empty_text(self):
        """Test document analysis with empty text."""
        with patch("workers_py.services.ai.SentenceTransformer"):
            service = AIService()
            result = service.analyze_document("")

            assert result["document_type"] is None
            assert result["confidence"] == 0.0
            assert "error" in result

    def test_compute_embeddings(self):
        """Test computing embeddings."""
        with patch("workers_py.services.ai.SentenceTransformer") as mock_transformer:
            mock_model = MagicMock()
            mock_model.encode.return_value = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
            mock_transformer.return_value = mock_model

            service = AIService()
            texts = ["First text", "Second text"]
            embeddings = service.compute_embeddings(texts)

            assert embeddings is not None
            mock_model.encode.assert_called_once_with(texts)

    def test_compute_embeddings_no_model(self):
        """Test computing embeddings when model is not available."""
        with patch("workers_py.services.ai.SentenceTransformer") as mock_transformer:
            mock_transformer.side_effect = Exception("Model loading failed")

            service = AIService()
            service.model = None  # Simulate failed model loading

            texts = ["First text", "Second text"]
            embeddings = service.compute_embeddings(texts)

            assert embeddings is None


class TestServiceIntegration:
    """Test service integration scenarios."""

    def test_pdf_to_ocr_fallback(self, temp_pdf_file):
        """Test fallback from PDF extraction to OCR."""
        with (
            patch("workers_py.services.pdf.pdfplumber") as mock_pdfplumber,
            patch("workers_py.services.ocr.pytesseract") as mock_tesseract,
            patch("workers_py.services.ocr.pdf2image") as mock_pdf2image,
        ):

            # Mock PDF service returning no text
            mock_page = MagicMock()
            mock_page.extract_text.return_value = ""  # No extractable text

            mock_pdf = MagicMock()
            mock_pdf.pages = [mock_page]
            mock_pdf.metadata = {}

            mock_pdfplumber.open.return_value.__enter__.return_value = mock_pdf

            # Mock OCR service
            mock_tesseract.get_tesseract_version.return_value = "4.1.1"
            mock_tesseract.image_to_string.return_value = "OCR extracted text"
            mock_tesseract.image_to_data.return_value = {
                "conf": ["95"],
                "text": ["OCR"],
                "left": [10],
                "top": [10],
                "width": [30],
                "height": [15],
            }

            mock_image = MagicMock()
            mock_pdf2image.convert_from_path.return_value = [mock_image]

            pdf_service = PDFService()
            ocr_service = OCRService()

            # First try PDF extraction
            pdf_result = pdf_service.extract_text_and_metadata(temp_pdf_file)
            assert pdf_result["text"] == ""  # No text from PDF

            # Then try OCR
            ocr_result = ocr_service.extract_text_from_pdf(temp_pdf_file)
            assert "OCR extracted text" in ocr_result["text"]
