"""Tests for the main FastAPI application."""

from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from workers_py.main import app


class TestHealthEndpoint:
    """Test health check endpoint."""

    def test_health_check_success(self, client: TestClient):
        """Test successful health check."""
        with patch("workers_py.main.get_database") as mock_get_db:
            # Mock database connection
            mock_conn = MagicMock()
            mock_conn.execute.return_value = None
            mock_get_db.return_value.__aenter__.return_value = mock_conn

            response = client.get("/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["version"] == "0.1.0"
            assert "services" in data

    def test_health_check_database_failure(self, client: TestClient):
        """Test health check with database failure."""
        with patch("workers_py.main.get_database") as mock_get_db:
            # Mock database connection failure
            mock_get_db.side_effect = Exception("Database connection failed")

            response = client.get("/health")

            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "unhealthy"


class TestProcessDocumentEndpoint:
    """Test document processing endpoint."""

    def test_process_document_success(self, client: TestClient, sample_request_data):
        """Test successful document processing initiation."""
        with patch("workers_py.main.process_document_task") as mock_task:
            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "test-task-123"
            mock_task.delay.return_value = mock_result

            response = client.post("/process-document", json=sample_request_data)

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-123"
            assert data["status"] == "processing"
            assert "message" in data

            # Verify task was called with correct parameters
            mock_task.delay.assert_called_once_with(
                entity_id=sample_request_data["entity_id"],
                file_url=sample_request_data["file_url"],
                options=sample_request_data["options"],
            )

    def test_process_document_invalid_data(self, client: TestClient):
        """Test document processing with invalid data."""
        invalid_data = {
            "entity_id": "",  # Invalid empty ID
            "file_url": "not-a-valid-url",  # Invalid URL
        }

        response = client.post("/process-document", json=invalid_data)

        assert response.status_code == 422  # Validation error

    def test_process_document_missing_fields(self, client: TestClient):
        """Test document processing with missing required fields."""
        incomplete_data = {
            "entity_id": "test-entity"
            # Missing file_url
        }

        response = client.post("/process-document", json=incomplete_data)

        assert response.status_code == 422

    def test_process_document_task_failure(
        self, client: TestClient, sample_request_data
    ):
        """Test document processing when task creation fails."""
        with patch("workers_py.main.process_document_task") as mock_task:
            # Mock task creation failure
            mock_task.delay.side_effect = Exception("Task creation failed")

            response = client.post("/process-document", json=sample_request_data)

            assert response.status_code == 500


class TestTaskStatusEndpoint:
    """Test task status endpoint."""

    def test_get_task_status_success(self, client: TestClient):
        """Test getting task status successfully."""
        task_id = "test-task-123"

        with patch("workers_py.main.celery_app") as mock_celery:
            # Mock successful task result
            mock_result = MagicMock()
            mock_result.state = "SUCCESS"
            mock_result.result = {"status": "completed", "text": "Sample text"}
            mock_celery.AsyncResult.return_value = mock_result

            response = client.get(f"/tasks/{task_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "completed"
            assert data["progress"] == 100
            assert data["result"] is not None

    def test_get_task_status_pending(self, client: TestClient):
        """Test getting status of pending task."""
        task_id = "test-task-pending"

        with patch("workers_py.main.celery_app") as mock_celery:
            # Mock pending task
            mock_result = MagicMock()
            mock_result.state = "PENDING"
            mock_celery.AsyncResult.return_value = mock_result

            response = client.get(f"/tasks/{task_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "pending"

    def test_get_task_status_progress(self, client: TestClient):
        """Test getting status of task in progress."""
        task_id = "test-task-progress"

        with patch("workers_py.main.celery_app") as mock_celery:
            # Mock task in progress
            mock_result = MagicMock()
            mock_result.state = "PROGRESS"
            mock_result.info = {"progress": 50}
            mock_celery.AsyncResult.return_value = mock_result

            response = client.get(f"/tasks/{task_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "processing"
            assert data["progress"] == 50

    def test_get_task_status_failed(self, client: TestClient):
        """Test getting status of failed task."""
        task_id = "test-task-failed"

        with patch("workers_py.main.celery_app") as mock_celery:
            # Mock failed task
            mock_result = MagicMock()
            mock_result.state = "FAILURE"
            mock_result.info = "Task failed due to error"
            mock_celery.AsyncResult.return_value = mock_result

            response = client.get(f"/tasks/{task_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "failed"
            assert data["error"] is not None

    def test_get_task_status_not_found(self, client: TestClient):
        """Test getting status of non-existent task."""
        task_id = "non-existent-task"

        with patch("workers_py.main.celery_app") as mock_celery:
            # Mock Celery returning unknown state
            mock_result = MagicMock()
            mock_result.state = "UNKNOWN"
            mock_celery.AsyncResult.return_value = mock_result

            response = client.get(f"/tasks/{task_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "unknown"


@pytest.mark.asyncio
class TestAsyncEndpoints:
    """Test endpoints with async client."""

    async def test_health_check_async(self, async_client: AsyncClient):
        """Test health check with async client."""
        with patch("workers_py.main.get_database") as mock_get_db:
            # Mock database connection
            mock_conn = MagicMock()
            mock_conn.execute.return_value = None
            mock_get_db.return_value.__aenter__.return_value = mock_conn

            response = await async_client.get("/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"

    async def test_process_document_async(
        self, async_client: AsyncClient, sample_request_data
    ):
        """Test document processing with async client."""
        with patch("workers_py.main.process_document_task") as mock_task:
            # Mock Celery task
            mock_result = MagicMock()
            mock_result.id = "test-task-async-123"
            mock_task.delay.return_value = mock_result

            response = await async_client.post(
                "/process-document", json=sample_request_data
            )

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-async-123"


class TestCORSAndMiddleware:
    """Test CORS and middleware functionality."""

    def test_cors_headers(self, client: TestClient):
        """Test CORS headers are present."""
        response = client.options(
            "/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
            },
        )

        # Note: This test might need adjustment based on actual CORS config
        assert response.status_code in [200, 204]

    def test_global_exception_handler(self, client: TestClient):
        """Test global exception handler."""
        # This would test the global exception handler
        # We'd need to trigger an unhandled exception somehow
        pass


class TestApplicationLifespan:
    """Test application lifespan events."""

    def test_app_startup(self):
        """Test application startup."""
        # This would test the lifespan startup events
        # In a real scenario, we'd mock the database initialization
        with patch("workers_py.main.init_database") as mock_init:
            mock_init.return_value = None

            # Test that we can create the app without errors
            assert app is not None

    def test_app_shutdown(self):
        """Test application shutdown."""
        # This would test the lifespan shutdown events
        # In practice, this is harder to test directly
        pass
