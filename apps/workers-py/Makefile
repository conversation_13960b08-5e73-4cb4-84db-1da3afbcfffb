# Makefile for workers-py service
.PHONY: help install dev-install run run-worker run-dev lint type test clean docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install       - Install production dependencies"
	@echo "  dev-install   - Install development dependencies"
	@echo "  run          - Run the FastAPI server"
	@echo "  run-worker   - Run Celery worker"
	@echo "  run-dev      - Run development server with reload"
	@echo "  lint         - Run code linting (ruff + black)"
	@echo "  type         - Run type checking (mypy)"
	@echo "  test         - Run tests"
	@echo "  clean        - Clean up temporary files"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"

# Installation
install:
	uv sync

dev-install:
	uv sync --dev

# Development
run:
	uv run python -m uvicorn workers_py.main:app --host 0.0.0.0 --port 8000

run-worker:
	uv run python -m celery -A workers_py.tasks worker --loglevel=info

run-dev:
	uv run python -m uvicorn workers_py.main:app --host 0.0.0.0 --port 8000 --reload

run-beat:
	uv run python -m celery -A workers_py.tasks beat --loglevel=info

# Code quality
lint:
	@echo "Running ruff..."
	uv run ruff check src/
	@echo "Running black..."
	uv run black --check src/

lint-fix:
	@echo "Fixing with ruff..."
	uv run ruff check --fix src/
	@echo "Fixing with black..."
	uv run black src/

type:
	@echo "Running mypy..."
	uv run mypy src/

# Testing
test:
	@echo "Running pytest..."
	uv run pytest tests/ -v --cov=src/workers_py --cov-report=term-missing

test-fast:
	@echo "Running pytest (fast)..."
	uv run pytest tests/ -x --ff

# Database
db-upgrade:
	@echo "Database migrations would go here..."
	@echo "Creating database tables..."
	uv run python -c "import asyncio; from src.workers_py.database import init_database; asyncio.run(init_database())"

# Cleanup
clean:
	@echo "Cleaning up..."
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type d -name ".ruff_cache" -exec rm -rf {} +

# Docker
docker-build:
	docker build -t workers-py .

docker-run:
	docker run -p 8000:8000 --env-file .env workers-py

docker-compose-up:
	docker-compose up -d

docker-compose-down:
	docker-compose down

# Environment
env-setup:
	@if [ ! -f .env ]; then \
		echo "Creating .env file from .env.example..."; \
		cp .env.example .env; \
		echo "Please edit .env file with your configuration."; \
	else \
		echo ".env file already exists."; \
	fi

# Health checks
health:
	@echo "Checking service health..."
	curl -s http://localhost:8000/health | python -m json.tool

# Development utilities
shell:
	uv run python

format: lint-fix

check: lint type test

# CI/CD helpers
ci-install:
	uv sync --dev

ci-test: ci-install lint type test

# Monitoring
logs:
	@echo "Application logs would be here..."
	@echo "Check your log directory or use docker logs <container_name>"

# Dependencies
update-deps:
	uv lock --upgrade

list-deps:
	uv tree

# Production
prod-run:
	uv run gunicorn workers_py.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Monitoring and debugging
debug:
	uv run python -m pdb -m workers_py.main

profile:
	uv run python -m cProfile -o profile.stats -m workers_py.main

# Documentation
docs-serve:
	@echo "Starting FastAPI docs server..."
	@echo "Docs will be available at http://localhost:8000/docs"
	$(MAKE) run-dev

# Quick development setup
quick-start: env-setup dev-install db-upgrade
	@echo "Development environment setup complete!"
	@echo "Run 'make run-dev' to start the development server"
	@echo "Run 'make run-worker' to start the Celery worker"