# Multi-stage Dockerfile for workers-py service
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Essential build tools
    gcc \
    g++ \
    make \
    # For image processing
    libpoppler-cpp-dev \
    poppler-utils \
    # For Tesseract OCR
    tesseract-ocr \
    tesseract-ocr-eng \
    libtesseract-dev \
    # For image handling
    libimage-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    # For PostgreSQL
    libpq-dev \
    # For cleaning up
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install uv
RUN pip install uv

# Create non-root user
RUN useradd -m -u 1000 worker && \
    mkdir -p /app && \
    chown -R worker:worker /app

WORKDIR /app

# Copy dependency files
COPY --chown=worker:worker pyproject.toml uv.lock ./

# Development stage
FROM base as development

# Install development dependencies
RUN uv sync --dev

# Copy source code
COPY --chown=worker:worker . .

# Switch to non-root user
USER worker

# Expose port
EXPOSE 8000

# Default command for development
CMD ["uv", "run", "uvicorn", "workers_py.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM base as production

# Install only production dependencies
RUN uv sync --no-dev

# Copy source code
COPY --chown=worker:worker src/ ./src/

# Switch to non-root user
USER worker

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command
CMD ["uv", "run", "gunicorn", "workers_py.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]

# Worker stage for Celery workers
FROM production as worker

# Override command for worker
CMD ["uv", "run", "celery", "-A", "workers_py.tasks", "worker", "--loglevel=info", "--concurrency=4"]

# Beat scheduler stage
FROM production as beat

# Override command for beat scheduler
CMD ["uv", "run", "celery", "-A", "workers_py.tasks", "beat", "--loglevel=info"]