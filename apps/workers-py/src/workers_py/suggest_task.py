"""Journal suggestion task for inbox documents."""

import asyncio
import logging
from typing import Any

from .services.suggest import create_suggestion_service
from .tasks import CallbackTask, celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, base=CallbackTask)
def create_suggestion_task(self, document_id: int, entity_id: int) -> dict[str, Any]:
    """
    Create journal suggestion from extracted document data.

    Args:
        document_id: Document ID with extraction data
        entity_id: Entity ID

    Returns:
        Processing results with suggestion data
    """
    result = {
        "document_id": document_id,
        "entity_id": entity_id,
        "status": "processing",
        "suggestion": None,
        "error": None,
    }

    try:
        logger.info(f"Creating suggestion for document {document_id}")

        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "stage": "loading_extraction",
                "document_id": document_id,
            },
        )

        # Load existing extraction result from database
        async def load_extraction():
            from .database import get_database

            async with get_database() as db:
                result = await db.fetch_one(
                    """
                    SELECT extraction, confidence
                    FROM inbox_documents
                    WHERE id = $1 AND entity_id = $2 AND status = 'extracted'
                    """,
                    document_id,
                    entity_id,
                )
                if not result:
                    raise ValueError(f"No extraction found for document {document_id}")

                return result

        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 40,
                "stage": "loading_extraction",
                "document_id": document_id,
            },
        )

        # Get extraction data from database
        db_result = asyncio.run(load_extraction())

        # Convert JSON back to ExtractionResult model
        from .models import ExtractionResult

        extraction_result = ExtractionResult.model_validate(db_result["extraction"])

        # Create suggestion service
        suggestion_service = create_suggestion_service(entity_id)

        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 70,
                "stage": "generating_lines",
                "document_id": document_id,
            },
        )

        # Create journal suggestion
        async def create_suggestion():
            return await suggestion_service.create_suggestion(
                extraction=extraction_result, entity_id=entity_id
            )

        suggestion_result = asyncio.run(create_suggestion())

        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={"progress": 90, "stage": "saving", "document_id": document_id},
        )

        # Convert to dict for JSON storage
        suggestion_dict = suggestion_result.model_dump()

        # Update document with suggestion
        async def update_document_suggestion():
            from .database import get_database

            async with get_database() as db:
                await db.execute(
                    """
                    UPDATE inbox_documents
                    SET status = 'suggested',
                        suggestion = $1,
                        updated_at = NOW()
                    WHERE id = $2 AND entity_id = $3
                    """,
                    suggestion_dict,
                    document_id,
                    entity_id,
                )

        # Update document in database with suggestion
        try:
            asyncio.run(update_document_suggestion())
            logger.info(f"Updated document {document_id} with suggestion")
        except Exception as db_error:
            logger.error(f"Failed to update document {document_id}: {db_error}")
            # Continue processing - don't fail the task for DB issues

        result.update({"status": "completed", "suggestion": suggestion_dict})

        logger.info(f"Successfully created suggestion for document {document_id}")
        return result

    except Exception as e:
        logger.error(f"Failed to create suggestion for document {document_id}: {e}")

        result.update({"status": "failed", "error": str(e)})

        # Update task state
        self.update_state(
            state="FAILURE", meta={"error": str(e), "document_id": document_id}
        )

        return result
