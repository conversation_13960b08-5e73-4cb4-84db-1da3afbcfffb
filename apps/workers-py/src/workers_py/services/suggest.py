"""Journal suggestion service for converting extractions to balanced accounting entries."""

import logging
from decimal import ROUND_HALF_UP, Decimal
from typing import Any

from ..models import ExtractionResult, Suggestion, SuggestionLine, VatRate

logger = logging.getLogger(__name__)


class SuggestionService:
    """Service to convert extraction results into balanced journal entries."""

    def __init__(self, account_mappings: dict[str, Any] | None = None):
        """
        Initialize suggestion service.

        Args:
            account_mappings: Chart of accounts configuration for the entity
        """
        self.account_mappings = account_mappings or self._default_account_mappings()

    def _default_account_mappings(self) -> dict[str, Any]:
        """Default Belgian chart of accounts mappings."""
        return {
            "expense_account": 6000,  # General expenses
            "vat_receivable": 4510,  # VAT receivable (purchase)
            "accounts_payable": 4400,  # Supplier payables
            "vat_codes": {
                VatRate.STANDARD: 1,  # 21% VAT code
                VatRate.REDUCED: 2,  # 12% VAT code
                VatRate.LOW: 3,  # 6% VAT code
                VatRate.ZERO: 4,  # 0% VAT code
            },
        }

    async def create_suggestion(
        self,
        extraction: ExtractionResult,
        entity_id: int,
        supplier_templates: dict[str, Any] | None = None,
    ) -> Suggestion:
        """
        Create balanced journal suggestion from extraction result.

        Args:
            extraction: AI extraction result
            entity_id: Entity ID for context
            supplier_templates: Optional supplier account preferences

        Returns:
            Balanced journal suggestion ready for posting
        """
        logger.info(f"Creating suggestion for entity {entity_id}")

        try:
            lines = []

            # Process each invoice line to create expense + VAT entries
            for line_item in extraction.lines:
                # Get account ID from hints or default mapping
                account_id = self._get_expense_account(
                    line_item, extraction.supplier, supplier_templates
                )

                # Calculate amounts with proper rounding
                net_amount = Decimal(line_item.unit_price) * Decimal(line_item.quantity)
                vat_rate_decimal = Decimal(line_item.vat_rate.value) / Decimal("100")
                vat_amount = (net_amount * vat_rate_decimal).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )

                # Add expense line (debit)
                expense_line = SuggestionLine(
                    account_id=account_id,
                    debit=str(net_amount),
                    credit="0",
                    vat_code_id=self.account_mappings["vat_codes"].get(
                        line_item.vat_rate
                    ),
                    memo=line_item.description,
                )
                lines.append(expense_line)

                # Add VAT line if applicable (debit)
                if vat_amount > 0:
                    vat_line = SuggestionLine(
                        account_id=self.account_mappings["vat_receivable"],
                        debit=str(vat_amount),
                        credit="0",
                        memo=f"VAT {line_item.vat_rate.value}% - {line_item.description}",
                    )
                    lines.append(vat_line)

            # Add supplier payable line (credit) - balancing entry
            gross_amount = Decimal(extraction.invoice.gross)
            payable_line = SuggestionLine(
                account_id=self.account_mappings["accounts_payable"],
                debit="0",
                credit=str(gross_amount),
                memo=f"{extraction.supplier.name} - {extraction.invoice.number}",
            )
            lines.append(payable_line)

            # Create the suggestion
            suggestion = Suggestion(
                journal_date=extraction.invoice.issue_date,
                reference=extraction.invoice.number,
                description=f"{self._get_journal_description(extraction)}",
                lines=lines,
            )

            # Verify balance
            total_debits = sum(Decimal(line.debit) for line in lines)
            total_credits = sum(Decimal(line.credit) for line in lines)

            if abs(total_debits - total_credits) >= Decimal("0.01"):
                logger.error(
                    f"Unbalanced suggestion: debits={total_debits}, credits={total_credits}"
                )
                raise ValueError("Generated suggestion is not balanced")

            logger.info(
                f"Created balanced suggestion with {len(lines)} lines, total: {gross_amount}"
            )
            return suggestion

        except Exception as e:
            logger.error(f"Failed to create suggestion: {str(e)}")
            raise

    def _get_expense_account(
        self, line_item, supplier_info, supplier_templates: dict[str, Any] | None
    ) -> int:
        """
        Determine the best expense account for this line item.

        Priority:
        1. Line item account hint (from AI extraction)
        2. Supplier template default account
        3. Default expense account
        """
        # Use AI account hint if available
        if line_item.account_hint:
            return line_item.account_hint

        # Use supplier template default if available
        if supplier_templates and supplier_info.vat:
            template = supplier_templates.get(supplier_info.vat)
            if template and template.get("default_account_id"):
                return template["default_account_id"]

        # Fall back to default expense account
        return self.account_mappings["expense_account"]

    def _get_journal_description(self, extraction: ExtractionResult) -> str:
        """Generate appropriate journal description."""
        supplier_name = extraction.supplier.name
        invoice_number = extraction.invoice.number

        # Try to create a meaningful description
        if len(extraction.lines) == 1:
            # Single line - use the line description
            line_desc = extraction.lines[0].description
            return f"{line_desc} - {supplier_name}"
        else:
            # Multiple lines - use generic description
            return f"Invoice {invoice_number} - {supplier_name}"


def create_suggestion_service(
    entity_id: int, account_mappings: dict[str, Any] | None = None
) -> SuggestionService:
    """
    Factory function to create suggestion service for an entity.

    Args:
        entity_id: Entity ID (for future entity-specific configurations)
        account_mappings: Optional custom account mappings

    Returns:
        Configured SuggestionService
    """
    # Future: Load entity-specific chart of accounts here
    return SuggestionService(account_mappings)
