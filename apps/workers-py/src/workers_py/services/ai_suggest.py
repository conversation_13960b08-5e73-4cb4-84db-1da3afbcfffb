"""AI-powered account and VAT code suggestion service using pgvector embeddings."""

import hashlib
import logging
import os
from typing import Any

import numpy as np
from openai import AsyncOpenAI
from pgvector.asyncpg import register_vector
from pydantic import BaseModel

from ..models import ExtractionResult

logger = logging.getLogger(__name__)


class EmbeddingProvider:
    """Base class for embedding providers."""

    async def embed_text(self, text: str) -> list[float]:
        """Generate embedding for given text."""
        raise NotImplementedError


class OpenAIEmbeddingProvider(EmbeddingProvider):
    """OpenAI embedding provider using text-embedding-3-small (1536 dimensions)."""

    def __init__(
        self, api_key: str | None = None, model: str = "text-embedding-3-small"
    ):
        self.client = AsyncOpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.model = model

    async def embed_text(self, text: str) -> list[float]:
        """Generate 1536-dimensional embedding using OpenAI."""
        try:
            response = await self.client.embeddings.create(input=text, model=self.model)
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"OpenAI embedding failed: {e}")
            raise


class StubEmbeddingProvider(EmbeddingProvider):
    """Deterministic stub provider for testing."""

    async def embed_text(self, text: str) -> list[float]:
        """Generate deterministic 1536-dim embedding based on text hash."""
        # Create deterministic embedding from text hash
        hash_bytes = hashlib.sha256(text.encode()).digest()

        # Convert to array of floats in [-1, 1] range
        embedding = []
        for i in range(1536):
            byte_idx = i % len(hash_bytes)
            # Normalize to [-1, 1] range
            value = (hash_bytes[byte_idx] - 128) / 128.0
            embedding.append(float(value))

        return embedding


class InvoiceSignature(BaseModel):
    """Structured representation of an invoice signature."""

    supplier_name: str | None
    supplier_vat: str | None
    line_items: list[dict[str, Any]]
    currency: str | None
    gross_amount: float | None


class AISuggestService:
    """AI suggestion service for account and VAT code recommendations."""

    def __init__(
        self,
        embedding_provider: EmbeddingProvider | None = None,
        db_pool: Any | None = None,
    ):
        # Use OpenAI by default, stub for testing
        provider_type = os.getenv("EMBED_PROVIDER", "openai")
        if embedding_provider:
            self.embedding_provider = embedding_provider
        elif provider_type == "stub":
            self.embedding_provider = StubEmbeddingProvider()
        else:
            self.embedding_provider = OpenAIEmbeddingProvider()

        self.db_pool = db_pool

    def build_signature(
        self,
        extraction: ExtractionResult,
        supplier_name: str | None = None,
        supplier_vat: str | None = None,
    ) -> str:
        """
        Build normalized text signature from extraction result.

        Format: "supplier: NAME; vat: VAT; lines: ITEM1 x QTY @PRICE VAT%; ITEM2..."
        Normalized: lowercase, collapsed whitespace, stripped accents.
        """
        # Use provided values or extract from result
        name = supplier_name or extraction.supplier.name or "unknown"
        vat = supplier_vat or extraction.supplier.vat or ""

        # Build supplier section
        signature_parts = [f"supplier: {name.lower().strip()}"]

        if vat:
            signature_parts.append(f"vat: {vat.upper().strip()}")

        # Build line items section (top 5 lines to keep signature manageable)
        line_descriptions = []
        for line in extraction.lines[:5]:
            desc = line.description or "item"
            qty = line.quantity or 1
            price = line.unit_price or 0
            vat_rate = line.vat_rate.value if line.vat_rate else 0

            # Normalize description
            desc_clean = " ".join(desc.lower().split())[:50]  # Max 50 chars
            line_desc = f"{desc_clean} x{qty} @{price} {vat_rate}%"
            line_descriptions.append(line_desc)

        if line_descriptions:
            signature_parts.append(f"lines: {'; '.join(line_descriptions)}")

        # Add total amount if available
        if extraction.invoice.gross:
            signature_parts.append(f"total: {extraction.invoice.gross}")

        # Join and normalize
        signature = "; ".join(signature_parts)

        # Final normalization: collapse whitespace, lowercase
        signature = " ".join(signature.split()).lower()

        return signature[:2000]  # Keep under 2k chars

    def compute_text_hash(self, text: str) -> str:
        """Compute SHA-256 hash of text for deduplication."""
        return hashlib.sha256(text.encode("utf-8")).hexdigest()

    async def create_coding_event(
        self,
        entity_id: int,
        document_id: int | None,
        extraction: ExtractionResult,
        account_id: int,
        vat_code_id: int | None,
        journal_id: int | None = None,
        supplier_name: str | None = None,
        supplier_vat: str | None = None,
    ) -> int:
        """
        Create a coding event record from a confirmed document.

        Returns the coding_event_id for subsequent embedding generation.
        """
        if not self.db_pool:
            raise ValueError("Database pool not configured")

        signature_text = self.build_signature(extraction, supplier_name, supplier_vat)

        coding_event_id = None
        async with self.db_pool.acquire() as conn:
            # Register pgvector extension
            await register_vector(conn)

            # Insert coding event
            coding_event_id = await conn.fetchval(
                """
                INSERT INTO coding_events (
                    entity_id, document_id, supplier_name, supplier_vat,
                    signature_text, account_id, vat_code_id, journal_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """,
                entity_id,
                document_id,
                supplier_name,
                supplier_vat,
                signature_text,
                account_id,
                vat_code_id,
                journal_id,
            )

        logger.info(f"Created coding event {coding_event_id} for entity {entity_id}")
        return coding_event_id

    async def index_coding_event(self, coding_event_id: int) -> bool:
        """
        Generate and store embedding for a coding event.

        Returns True if successful, False if already exists or failed.
        """
        if not self.db_pool:
            raise ValueError("Database pool not configured")

        async with self.db_pool.acquire() as conn:
            await register_vector(conn)

            # Fetch coding event
            event = await conn.fetchrow(
                """
                SELECT entity_id, signature_text FROM coding_events
                WHERE id = $1
            """,
                coding_event_id,
            )

            if not event:
                logger.warning(f"Coding event {coding_event_id} not found")
                return False

            entity_id = event["entity_id"]
            signature_text = event["signature_text"]
            text_hash = self.compute_text_hash(signature_text)

            # Check if embedding already exists
            existing = await conn.fetchval(
                """
                SELECT id FROM invoice_embeddings
                WHERE entity_id = $1 AND text_hash = $2
            """,
                entity_id,
                text_hash,
            )

            if existing:
                logger.info(
                    f"Embedding already exists for coding event {coding_event_id}"
                )
                return True

            # Generate embedding
            try:
                embedding = await self.embedding_provider.embed_text(signature_text)
                embedding_array = np.array(embedding, dtype=np.float32)

                # Store embedding
                await conn.execute(
                    """
                    INSERT INTO invoice_embeddings (
                        entity_id, coding_event_id, text_hash, embedding
                    ) VALUES ($1, $2, $3, $4)
                    ON CONFLICT (entity_id, text_hash) DO NOTHING
                """,
                    entity_id,
                    coding_event_id,
                    text_hash,
                    embedding_array,
                )

                logger.info(f"Generated embedding for coding event {coding_event_id}")
                return True

            except Exception as e:
                logger.error(
                    f"Failed to generate embedding for coding event {coding_event_id}: {e}"
                )
                return False

    async def reindex_entity(self, entity_id: int, limit: int = 100) -> int:
        """
        Reindex recent coding events for an entity that don't have embeddings.

        Returns count of events processed.
        """
        if not self.db_pool:
            raise ValueError("Database pool not configured")

        async with self.db_pool.acquire() as conn:
            await register_vector(conn)

            # Find recent coding events without embeddings
            events = await conn.fetch(
                """
                SELECT ce.id
                FROM coding_events ce
                LEFT JOIN invoice_embeddings ie ON ie.coding_event_id = ce.id
                WHERE ce.entity_id = $1 AND ie.id IS NULL
                ORDER BY ce.created_at DESC
                LIMIT $2
            """,
                entity_id,
                limit,
            )

            processed = 0
            for event in events:
                if await self.index_coding_event(event["id"]):
                    processed += 1

            logger.info(f"Reindexed {processed} events for entity {entity_id}")
            return processed

    async def get_suggestions(
        self,
        entity_id: int,
        extraction: ExtractionResult,
        supplier_name: str | None = None,
        supplier_vat: str | None = None,
        k: int = 32,
    ) -> list[dict[str, Any]]:
        """
        Get AI suggestions for account and VAT code based on similar documents.

        Returns list of suggestions with scores and explanations.
        """
        if not self.db_pool:
            raise ValueError("Database pool not configured")

        # Build signature and generate embedding
        signature_text = self.build_signature(extraction, supplier_name, supplier_vat)

        try:
            embedding = await self.embedding_provider.embed_text(signature_text)
        except Exception as e:
            logger.error(f"Failed to generate embedding for suggestion: {e}")
            return []

        try:
            async with self.db_pool.acquire() as conn:
                await register_vector(conn)

                # Call k-NN RPC function
                suggestions = await conn.fetch(
                    """
                    SELECT account_id, vat_code_id, score, avg_sim, votes, top_examples
                    FROM rpc_ai_knn_suggest(
                        p_entity => $1,
                        p_query => $2,
                        p_k => $3,
                        p_supplier_vat => $4
                    )
                """,
                    entity_id,
                    embedding,
                    k,
                    supplier_vat,
                )

                # Format results with explanations
                results = []
                for suggestion in suggestions:
                    # Get example details for explanation
                    example_details = []
                    if suggestion["top_examples"]:
                        examples = await conn.fetch(
                            """
                            SELECT supplier_name, confirmed_at::date as confirmed_date
                            FROM rpc_get_coding_event_details($1, $2)
                            LIMIT 3
                        """,
                            entity_id,
                            suggestion["top_examples"],
                        )

                        example_details = [
                            {
                                "supplier_name": ex["supplier_name"],
                                "confirmed_date": (
                                    ex["confirmed_date"].isoformat()
                                    if ex["confirmed_date"]
                                    else None
                                ),
                            }
                            for ex in examples
                        ]

                    # Build explanation
                    confidence_level = (
                        "High"
                        if suggestion["score"] > 0.6
                        else "Medium" if suggestion["score"] > 0.3 else "Low"
                    )

                    supplier_name_clean = (
                        supplier_name or extraction.supplier.name or "this supplier"
                    )
                    explanation = f"Based on {suggestion['votes']} similar invoices"
                    if supplier_vat and any(
                        ex.get("supplier_name") == supplier_name_clean
                        for ex in example_details
                    ):
                        explanation += f" from {supplier_name_clean}"
                    explanation += f" (confidence: {confidence_level})"

                    results.append(
                        {
                            "account_id": suggestion["account_id"],
                            "vat_code_id": suggestion["vat_code_id"],
                            "score": float(suggestion["score"]),
                            "confidence": confidence_level.lower(),
                            "votes": suggestion["votes"],
                            "avg_similarity": float(suggestion["avg_sim"]),
                            "explanation": explanation,
                            "examples": example_details,
                        }
                    )

                logger.info(
                    f"Generated {len(results)} suggestions for entity {entity_id}"
                )
                return results

        except Exception as e:
            logger.error(f"Failed to get suggestions for entity {entity_id}: {e}")
            return []


def create_ai_suggest_service(
    db_pool: Any | None = None, embedding_provider: EmbeddingProvider | None = None
) -> AISuggestService:
    """
    Factory function to create AI suggestion service.

    Args:
        db_pool: AsyncPG connection pool
        embedding_provider: Optional custom embedding provider

    Returns:
        Configured AISuggestService
    """
    return AISuggestService(embedding_provider=embedding_provider, db_pool=db_pool)
