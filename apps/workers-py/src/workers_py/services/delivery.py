"""
Export delivery services for Track F integrations.
Handles SFTP and email delivery of export bundles to external systems.
"""

import logging
import smtplib
import tempfile
import time
from abc import ABC, abstractmethod
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path
from typing import Any

import paramiko
from pydantic import BaseModel

logger = logging.getLogger(__name__)


# =============================================================================
# DELIVERY MODELS
# =============================================================================


class DeliveryFile(BaseModel):
    name: str
    content: bytes
    mime_type: str
    size: int


class DeliveryResult(BaseModel):
    success: bool
    message: str
    files_delivered: int = 0
    duration_ms: int | None = None
    error_details: dict[str, Any] | None = None


class SFTPConfig(BaseModel):
    host: str
    port: int = 22
    username: str
    password: str | None = None
    private_key_path: str | None = None
    remote_dir: str = "/inbox"
    timeout: int = 30
    create_remote_dirs: bool = True


class EmailConfig(BaseModel):
    smtp_host: str
    smtp_port: int = 587
    smtp_user: str
    smtp_password: str
    from_email: str
    to_email: str
    use_tls: bool = True
    timeout: int = 30


# =============================================================================
# DELIVERY INTERFACE
# =============================================================================


class DeliveryConnector(ABC):
    """Abstract base class for delivery connectors."""

    @abstractmethod
    async def deliver_files(
        self, files: list[DeliveryFile], metadata: dict[str, Any]
    ) -> DeliveryResult:
        """Deliver files to the configured destination."""
        pass

    @abstractmethod
    def validate_config(self) -> bool:
        """Validate connector configuration."""
        pass


# =============================================================================
# SFTP DELIVERY CONNECTOR
# =============================================================================


class SFTPDeliveryConnector(DeliveryConnector):
    """SFTP delivery connector using paramiko."""

    def __init__(self, config: SFTPConfig):
        self.config = config
        logger.info(f"Initializing SFTP connector for {config.host}:{config.port}")

    def validate_config(self) -> bool:
        """Validate SFTP configuration."""
        if not self.config.host:
            logger.error("SFTP host is required")
            return False

        if not self.config.username:
            logger.error("SFTP username is required")
            return False

        if not self.config.password and not self.config.private_key_path:
            logger.error("SFTP password or private key is required")
            return False

        return True

    async def deliver_files(
        self, files: list[DeliveryFile], metadata: dict[str, Any]
    ) -> DeliveryResult:
        """Deliver files via SFTP."""
        start_time = time.time()
        files_delivered = 0

        logger.info(
            f"Starting SFTP delivery to {self.config.host}:{self.config.port} for {len(files)} files"
        )

        if not self.validate_config():
            return DeliveryResult(
                success=False,
                message="Invalid SFTP configuration",
                error_details={"type": "config_error"},
            )

        ssh_client = None
        sftp_client = None

        try:
            # Create SSH connection
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect with password or key
            connect_kwargs = {
                "hostname": self.config.host,
                "port": self.config.port,
                "username": self.config.username,
                "timeout": self.config.timeout,
            }

            if (
                self.config.private_key_path
                and Path(self.config.private_key_path).exists()
            ):
                # Use private key authentication
                private_key = paramiko.RSAKey.from_private_key_file(
                    self.config.private_key_path
                )
                connect_kwargs["pkey"] = private_key
                logger.info(
                    f"Connecting to SFTP server with private key: {self.config.host}"
                )
            elif self.config.password:
                # Use password authentication
                connect_kwargs["password"] = self.config.password
                logger.info(
                    f"Connecting to SFTP server with password: {self.config.host}"
                )
            else:
                raise ValueError("No authentication method available")

            ssh_client.connect(**connect_kwargs)

            # Create SFTP client
            sftp_client = ssh_client.open_sftp()

            # Create remote directory structure if needed
            if self.config.create_remote_dirs:
                entity_id = metadata.get("entity_id", "unknown")
                date_path = metadata.get("issue_date", "").replace("-", "")[
                    :6
                ]  # YYYYMM

                if date_path:
                    full_remote_dir = (
                        f"{self.config.remote_dir}/{date_path}/{entity_id}"
                    )
                else:
                    full_remote_dir = f"{self.config.remote_dir}/{entity_id}"

                self._ensure_remote_directory(sftp_client, full_remote_dir)
            else:
                full_remote_dir = self.config.remote_dir

            # Upload files
            for file in files:
                remote_path = f"{full_remote_dir}/{file.name}"

                # Write file content to temporary file first
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    temp_file.write(file.content)
                    temp_file.flush()

                    # Upload via SFTP
                    sftp_client.put(temp_file.name, remote_path)
                    files_delivered += 1

                    logger.info(
                        f"Uploaded file {file.name} to {remote_path} ({file.size} bytes)"
                    )

                # Clean up temp file
                Path(temp_file.name).unlink(missing_ok=True)

            end_time = time.time()
            duration_ms = int((end_time - start_time) * 1000)

            logger.info(
                f"SFTP delivery completed successfully: {files_delivered} files in {duration_ms}ms"
            )

            return DeliveryResult(
                success=True,
                message=f"Successfully delivered {files_delivered} files via SFTP",
                files_delivered=files_delivered,
                duration_ms=duration_ms,
            )

        except paramiko.AuthenticationException as e:
            logger.error(f"SFTP authentication failed: {e}")
            return DeliveryResult(
                success=False,
                message=f"SFTP authentication failed: {str(e)}",
                files_delivered=files_delivered,
                error_details={"type": "auth_error", "details": str(e)},
            )

        except paramiko.SSHException as e:
            logger.error(f"SFTP connection error: {e}")
            return DeliveryResult(
                success=False,
                message=f"SFTP connection error: {str(e)}",
                files_delivered=files_delivered,
                error_details={"type": "connection_error", "details": str(e)},
            )

        except Exception as e:
            logger.error(f"SFTP delivery failed: {e}")
            return DeliveryResult(
                success=False,
                message=f"SFTP delivery failed: {str(e)}",
                files_delivered=files_delivered,
                error_details={"type": "delivery_error", "details": str(e)},
            )

        finally:
            # Clean up connections
            if sftp_client:
                try:
                    sftp_client.close()
                except Exception:
                    pass

            if ssh_client:
                try:
                    ssh_client.close()
                except Exception:
                    pass

    def _ensure_remote_directory(
        self, sftp_client: paramiko.SFTPClient, remote_dir: str
    ) -> None:
        """Create remote directory structure if it doesn't exist."""
        try:
            # Try to stat the directory
            sftp_client.stat(remote_dir)
            logger.debug(f"Remote directory exists: {remote_dir}")
        except FileNotFoundError:
            # Directory doesn't exist, create it
            logger.info(f"Creating remote directory: {remote_dir}")

            # Create parent directories recursively
            parent_dir = str(Path(remote_dir).parent)
            if parent_dir != remote_dir and parent_dir != "/":
                self._ensure_remote_directory(sftp_client, parent_dir)

            # Create the directory
            sftp_client.mkdir(remote_dir)
            logger.info(f"Created remote directory: {remote_dir}")


# =============================================================================
# EMAIL DELIVERY CONNECTOR
# =============================================================================


class EmailDeliveryConnector(DeliveryConnector):
    """Email delivery connector using SMTP."""

    def __init__(self, config: EmailConfig):
        self.config = config
        logger.info(
            f"Initializing email connector for {config.smtp_host}:{config.smtp_port}"
        )

    def validate_config(self) -> bool:
        """Validate email configuration."""
        if not self.config.smtp_host:
            logger.error("SMTP host is required")
            return False

        if not self.config.smtp_user:
            logger.error("SMTP user is required")
            return False

        if not self.config.smtp_password:
            logger.error("SMTP password is required")
            return False

        if not self.config.from_email:
            logger.error("From email is required")
            return False

        if not self.config.to_email:
            logger.error("To email is required")
            return False

        return True

    async def deliver_files(
        self, files: list[DeliveryFile], metadata: dict[str, Any]
    ) -> DeliveryResult:
        """Deliver files via email."""
        start_time = logger.info().timestamp()  # Mock timestamp

        if not self.validate_config():
            return DeliveryResult(
                success=False,
                message="Invalid email configuration",
                error_details={"type": "config_error"},
            )

        try:
            # Create email message
            message = MIMEMultipart()
            message["From"] = self.config.from_email
            message["To"] = self.config.to_email
            message["Subject"] = self._generate_subject(metadata)

            # Add body text
            body_text = self._generate_body(files, metadata)
            message.attach(MIMEText(body_text, "plain"))

            # Add file attachments
            for file in files:
                attachment = MIMEApplication(file.content, _subtype="octet-stream")
                attachment.add_header(
                    "Content-Disposition", "attachment", filename=file.name
                )
                message.attach(attachment)
                logger.info(f"Added attachment: {file.name} ({file.size} bytes)")

            # Send email
            with smtplib.SMTP(
                self.config.smtp_host,
                self.config.smtp_port,
                timeout=self.config.timeout,
            ) as smtp:
                if self.config.use_tls:
                    smtp.starttls()

                smtp.login(self.config.smtp_user, self.config.smtp_password)
                smtp.send_message(message)

            end_time = logger.info().timestamp()  # Mock timestamp
            duration_ms = int((end_time - start_time) * 1000)

            logger.info(
                f"Email sent successfully to {self.config.to_email} with {len(files)} attachments"
            )

            return DeliveryResult(
                success=True,
                message=f"Successfully sent email with {len(files)} attachments",
                files_delivered=len(files),
                duration_ms=duration_ms,
            )

        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"Email authentication failed: {e}")
            return DeliveryResult(
                success=False,
                message=f"Email authentication failed: {str(e)}",
                error_details={"type": "auth_error", "details": str(e)},
            )

        except smtplib.SMTPException as e:
            logger.error(f"SMTP error: {e}")
            return DeliveryResult(
                success=False,
                message=f"SMTP error: {str(e)}",
                error_details={"type": "smtp_error", "details": str(e)},
            )

        except Exception as e:
            logger.error(f"Email delivery failed: {e}")
            return DeliveryResult(
                success=False,
                message=f"Email delivery failed: {str(e)}",
                error_details={"type": "delivery_error", "details": str(e)},
            )

    def _generate_subject(self, metadata: dict[str, Any]) -> str:
        """Generate email subject line."""
        entity_id = metadata.get("entity_id", "Unknown")
        doc_number = metadata.get("doc_number", "Unknown")
        source = metadata.get("source", "Unknown")

        return f"Ledgerly Export - {source} {doc_number} (Entity {entity_id})"

    def _generate_body(
        self, files: list[DeliveryFile], metadata: dict[str, Any]
    ) -> str:
        """Generate email body text."""
        entity_id = metadata.get("entity_id", "Unknown")
        doc_number = metadata.get("doc_number", "Unknown")
        source = metadata.get("source", "Unknown")
        issue_date = metadata.get("issue_date", "Unknown")
        partner_name = metadata.get("partner_name", "Unknown")

        body = f"""
Ledgerly Export Delivery

Document Details:
- Type: {source} ({"Accounts Payable" if source == "AP" else "Accounts Receivable" if source == "AR" else "Unknown"})
- Document Number: {doc_number}
- Entity ID: {entity_id}
- Issue Date: {issue_date}
- Partner: {partner_name}

Attached Files ({len(files)} total):
"""

        for file in files:
            file_size_kb = round(file.size / 1024, 1)
            body += f"- {file.name} ({file_size_kb} KB, {file.mime_type})\n"

        body += f"""
This export was generated automatically by Ledgerly.
For questions, please contact your accounting team.

Generated at: {logger.info().isoformat()}  # Mock timestamp
"""

        return body


# =============================================================================
# CONNECTOR FACTORY
# =============================================================================


def create_delivery_connector(
    connector_type: str, config: dict[str, Any]
) -> DeliveryConnector:
    """Create delivery connector instance based on type and config."""

    if connector_type == "winbooks_sftp":
        sftp_config = SFTPConfig(**config)
        return SFTPDeliveryConnector(sftp_config)

    elif connector_type == "email":
        email_config = EmailConfig(**config)
        return EmailDeliveryConnector(email_config)

    else:
        raise ValueError(f"Unsupported connector type: {connector_type}")


# =============================================================================
# DRY RUN CONNECTOR
# =============================================================================


class DryRunDeliveryConnector(DeliveryConnector):
    """Dry run connector that simulates delivery without actually sending files."""

    def __init__(self, storage_path: str | None = None):
        self.storage_path = (
            Path(storage_path)
            if storage_path
            else Path(tempfile.gettempdir()) / "ledgerly-dry-run"
        )
        logger.info(f"Initializing dry-run connector, storage: {self.storage_path}")

    def validate_config(self) -> bool:
        """Always valid for dry run."""
        return True

    async def deliver_files(
        self, files: list[DeliveryFile], metadata: dict[str, Any]
    ) -> DeliveryResult:
        """Simulate delivery by writing files to local storage."""
        start_time = logger.info().timestamp()  # Mock timestamp

        try:
            # Create storage directory
            entity_id = metadata.get("entity_id", "unknown")
            doc_number = metadata.get("doc_number", "unknown")
            run_dir = self.storage_path / f"entity_{entity_id}" / doc_number
            run_dir.mkdir(parents=True, exist_ok=True)

            # Write files to storage
            files_delivered = 0
            for file in files:
                file_path = run_dir / file.name
                file_path.write_bytes(file.content)
                files_delivered += 1
                logger.info(
                    f"Dry-run: Saved {file.name} to {file_path} ({file.size} bytes)"
                )

            # Write metadata
            import json

            metadata_path = run_dir / "metadata.json"
            metadata_path.write_text(json.dumps(metadata, indent=2))

            end_time = logger.info().timestamp()  # Mock timestamp
            duration_ms = int((end_time - start_time) * 1000)

            return DeliveryResult(
                success=True,
                message=f"Dry-run: Saved {files_delivered} files to {run_dir}",
                files_delivered=files_delivered,
                duration_ms=duration_ms,
            )

        except Exception as e:
            logger.error(f"Dry-run delivery failed: {e}")
            return DeliveryResult(
                success=False,
                message=f"Dry-run delivery failed: {str(e)}",
                error_details={"type": "dry_run_error", "details": str(e)},
            )
