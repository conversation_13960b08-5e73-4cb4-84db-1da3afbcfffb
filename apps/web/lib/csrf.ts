import { NextRequest } from 'next/server'

export interface CSRFConfig {
  secret: string
  tokenLength: number
  headerName: string
  cookieName: string
}

const defaultConfig: CSRFConfig = {
  secret: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production',
  tokenLength: 32,
  headerName: 'x-csrf-token',
  cookieName: 'csrf-token'
}

export async function generateCSRFToken(config: CSRFConfig = defaultConfig): Promise<string> {
  // Use Web Crypto API for Edge Runtime compatibility
  const randomBytes = new Uint8Array(config.tokenLength)
  crypto.getRandomValues(randomBytes)
  const timestamp = Date.now().toString()
  const data = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('') + timestamp

  // Use Web Crypto API for HMAC
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(config.secret),
    { name: 'HM<PERSON>', hash: 'SHA-256' },
    false,
    ['sign']
  )

  const signature = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(data))
  const signatureHex = Array.from(new Uint8Array(signature), byte => byte.toString(16).padStart(2, '0')).join('')

  return btoa(data + '.' + signatureHex)
}

export async function validateCSRFToken(
  token: string,
  config: CSRFConfig = defaultConfig,
  maxAge: number = 3600000 // 1 hour in milliseconds
): Promise<boolean> {
  try {
    const decoded = atob(token)
    const [data, signature] = decoded.split('.')

    if (!data || !signature) {
      return false
    }

    // Verify signature using Web Crypto API
    const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(config.secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['verify']
    )

    const signatureBytes = new Uint8Array(signature.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)))
    const isValid = await crypto.subtle.verify('HMAC', key, signatureBytes, new TextEncoder().encode(data))

    if (!isValid) {
      return false
    }

    // Extract timestamp and check age
    const timestampStr = data.slice(-13) // Last 13 characters should be timestamp
    const timestamp = parseInt(timestampStr, 10)

    if (isNaN(timestamp)) {
      return false
    }

    const age = Date.now() - timestamp
    return age <= maxAge

  } catch (error) {
    return false
  }
}

export function getCSRFTokenFromRequest(
  request: NextRequest,
  config: CSRFConfig = defaultConfig
): string | null {
  // Try header first
  const headerToken = request.headers.get(config.headerName)
  if (headerToken) {
    return headerToken
  }
  
  // Try cookie
  const cookieToken = request.cookies.get(config.cookieName)?.value
  if (cookieToken) {
    return cookieToken
  }
  
  return null
}

export async function validateCSRFFromRequest(
  request: NextRequest,
  config: CSRFConfig = defaultConfig
): Promise<boolean> {
  const token = getCSRFTokenFromRequest(request, config)

  if (!token) {
    return false
  }

  return await validateCSRFToken(token, config)
}

// Middleware function for CSRF validation
export async function validateCSRFMiddleware(request: NextRequest): Promise<boolean> {
  return await validateCSRFFromRequest(request)
}
