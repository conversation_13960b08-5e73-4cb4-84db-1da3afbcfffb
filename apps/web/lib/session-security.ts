import { createClientSupabaseClient } from './auth'
import { extractRequestInfo, SecurityEvents } from './security-monitoring'

/**
 * Request-like object for extracting security information
 */
interface RequestLike {
  ip?: string
  headers?: Record<string, string | string[] | undefined> | Headers
  connection?: {
    remoteAddress?: string
  }
}

export function createSecureBrowserClient() {
  return createClientSupabaseClient()
}

export interface SessionSecurityConfig {
  maxIdleTime: number // in milliseconds
  warningTime: number // in milliseconds before timeout
  checkInterval: number // in milliseconds
  maxAbsoluteTime?: number // maximum session duration regardless of activity
  maxConcurrentSessions?: number // maximum concurrent sessions per user
}

export interface SessionMetadata {
  sessionId: string
  userId: string
  ipAddress: string
  userAgent: string
  deviceFingerprint: string
  isActive: boolean
  createdAt: number
  lastActivity: number
  expiresAt: number
  absoluteExpiresAt?: number
}

export interface SessionTimeoutInfo {
  timeUntilTimeout: number
  isWarning: boolean
  warningTime: number
  maxIdleTime: number
  absoluteTimeRemaining?: number
  isTimedOut?: boolean
  reason?: 'idle' | 'absolute' | null
  // Additional properties expected by tests
  idleTimeRemaining?: number
  showWarning?: boolean
  shouldTimeout?: boolean
}

export interface SessionValidationResult {
  valid: boolean
  reason?: string
  timeoutInfo?: SessionTimeoutInfo
  securityViolation?: boolean
}

export const DEFAULT_SESSION_CONFIG = {
  development: {
    idleTimeout: 60 * 60 * 1000, // 1 hour in development
    warningTime: 10 * 60 * 1000, // 10 minutes warning
    checkInterval: 60 * 1000, // check every minute
    maxAbsoluteTime: 4 * 60 * 60 * 1000, // 4 hours absolute maximum
    maxConcurrentSessions: 5, // max 5 concurrent sessions per user
  },
  production: {
    idleTimeout: 30 * 60 * 1000, // 30 minutes in production
    warningTime: 5 * 60 * 1000, // 5 minutes warning
    checkInterval: 60 * 1000, // check every minute
    maxAbsoluteTime: 2 * 60 * 60 * 1000, // 2 hours absolute maximum
    maxConcurrentSessions: 3, // max 3 concurrent sessions per user
  },
}

export const defaultSessionConfig: SessionSecurityConfig = {
  maxIdleTime: 30 * 60 * 1000, // 30 minutes
  warningTime: 5 * 60 * 1000, // 5 minutes warning
  checkInterval: 60 * 1000, // check every minute
  maxAbsoluteTime: 2 * 60 * 60 * 1000, // 2 hours absolute maximum
  maxConcurrentSessions: 3, // max 3 concurrent sessions per user
}

export class SessionSecurityManager {
  private sessions: Map<string, SessionMetadata> = new Map()
  private userSessions: Map<string, Set<string>> = new Map()
  private config: SessionSecurityConfig
  private warningCallback?: () => void
  private timeoutCallback?: () => void
  private intervalId?: NodeJS.Timeout

  constructor(config?: SessionSecurityConfig) {
    if (config) {
      this.config = config
    } else {
      // Use environment-specific configuration
      const env = process.env.NODE_ENV || 'development'
      const envConfig =
        env === 'production'
          ? DEFAULT_SESSION_CONFIG.production
          : DEFAULT_SESSION_CONFIG.development
      this.config = {
        maxIdleTime: envConfig.idleTimeout,
        warningTime: envConfig.warningTime,
        checkInterval: envConfig.checkInterval,
        maxAbsoluteTime: envConfig.maxAbsoluteTime,
        maxConcurrentSessions: envConfig.maxConcurrentSessions,
      }
    }

    // Only set up activity listeners in browser environment
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      this.setupActivityListeners()
    }
  }

  public getConfig() {
    const env = process.env.NODE_ENV || 'development'
    return env === 'production'
      ? DEFAULT_SESSION_CONFIG.production
      : DEFAULT_SESSION_CONFIG.development
  }

  private setupActivityListeners() {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
    ]

    events.forEach(event => {
      document.addEventListener(event, () => this.updateGlobalActivity(), true)
    })
  }

  private updateGlobalActivity() {
    // Update activity for all active sessions in browser environment
    const now = Date.now()
    for (const session of Array.from(this.sessions.values())) {
      if (session.isActive) {
        session.lastActivity = now
      }
    }
  }

  public generateDeviceFingerprint(
    ipAddress: string,
    userAgent: string
  ): string {
    const fingerprint = `${ipAddress}:${userAgent}`
    // Environment-agnostic, deterministic non-cryptographic hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash |= 0 // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(16, '0').substring(0, 16)
  }

  public initializeSession(
    sessionId: string,
    userId: string,
    request?: RequestLike
  ): Promise<SessionMetadata> {
    const now = Date.now()
    let ipAddress = 'unknown'
    let userAgent = 'unknown'

    if (request) {
      const requestInfo = extractRequestInfo(request)
      ipAddress = requestInfo.ip_address
      userAgent = requestInfo.user_agent
    }

    const deviceFingerprint = this.generateDeviceFingerprint(
      ipAddress,
      userAgent
    )

    const metadata: SessionMetadata = {
      sessionId,
      userId,
      ipAddress,
      userAgent,
      deviceFingerprint,
      isActive: true,
      createdAt: now,
      lastActivity: now,
      expiresAt: now + this.config.maxIdleTime,
      absoluteExpiresAt: this.config.maxAbsoluteTime
        ? now + this.config.maxAbsoluteTime
        : undefined,
    }

    this.sessions.set(sessionId, metadata)

    // Track user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set())
    }
    this.userSessions.get(userId)!.add(sessionId)

    // Automatically enforce max sessions
    this.enforceMaxSessions(userId, sessionId)

    // Log successful login
    void SecurityEvents.successfulLogin(userId, sessionId, ipAddress, userAgent)

    return Promise.resolve(metadata)
  }

  public updateActivity(sessionId: string, request?: RequestLike): boolean {
    const session = this.sessions.get(sessionId)
    if (!session || !session.isActive) {
      return false
    }

    const now = Date.now()

    // Check if session has expired
    if (this.isSessionExpired(session, now)) {
      void this.terminateSession(sessionId, 'timeout')
      return false
    }

    // Validate device fingerprint if request is provided
    if (request) {
      const requestInfo = extractRequestInfo(request)
      const currentFingerprint = this.generateDeviceFingerprint(
        requestInfo.ip_address,
        requestInfo.user_agent
      )

      if (currentFingerprint !== session.deviceFingerprint) {
        // Device fingerprint mismatch - potential session hijacking
        void SecurityEvents.suspiciousRequest(
          session.userId,
          'device_fingerprint_mismatch',
          requestInfo.ip_address,
          requestInfo.user_agent,
          {
            sessionId,
            originalFingerprint: session.deviceFingerprint,
            newFingerprint: currentFingerprint,
          }
        )
        void this.terminateSession(sessionId, 'security_violation')
        return false
      }
    }

    // Update activity
    session.lastActivity = now
    session.expiresAt = now + this.config.maxIdleTime

    return true
  }

  private isSessionExpired(session: SessionMetadata, now: number): boolean {
    // Check idle timeout
    if (now > session.expiresAt) {
      return true
    }

    // Check absolute timeout
    if (session.absoluteExpiresAt && now > session.absoluteExpiresAt) {
      return true
    }

    return false
  }

  public hasActiveSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    return session
      ? session.isActive && !this.isSessionExpired(session, Date.now())
      : false
  }

  public terminateSession(sessionId: string, reason?: string): Promise<void> {
    const session = this.sessions.get(sessionId)
    if (!session) {
      return Promise.resolve()
    }

    session.isActive = false

    // Remove from user sessions tracking
    const userSessionSet = this.userSessions.get(session.userId)
    if (userSessionSet) {
      userSessionSet.delete(sessionId)
      if (userSessionSet.size === 0) {
        this.userSessions.delete(session.userId)
      }
    }

    // Log security events for suspicious terminations
    if (reason === 'security_violation') {
      void SecurityEvents.suspiciousRequest(
        session.userId,
        'session_terminated',
        session.ipAddress,
        session.userAgent,
        { sessionId, reason }
      )
    }

    this.sessions.delete(sessionId)
    return Promise.resolve()
  }

  public startMonitoring(
    warningCallback: () => void,
    timeoutCallback: () => void
  ) {
    this.warningCallback = warningCallback
    this.timeoutCallback = timeoutCallback

    this.intervalId = setInterval(() => {
      this.checkAllSessionTimeouts()
    }, this.config.checkInterval)
  }

  private checkAllSessionTimeouts() {
    const now = Date.now()

    for (const [sessionId, session] of Array.from(this.sessions.entries())) {
      if (!session.isActive) continue

      const timeSinceActivity = now - session.lastActivity
      const timeUntilIdleTimeout = this.config.maxIdleTime - timeSinceActivity
      const timeUntilAbsoluteTimeout = session.absoluteExpiresAt
        ? session.absoluteExpiresAt - now
        : Infinity

      const timeUntilTimeout = Math.min(
        timeUntilIdleTimeout,
        timeUntilAbsoluteTimeout
      )

      if (timeUntilTimeout <= 0) {
        void this.terminateSession(sessionId, 'timeout')
        this.timeoutCallback?.()
      } else if (timeUntilTimeout <= this.config.warningTime) {
        this.warningCallback?.()
      }
    }
  }

  public stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = undefined
    }
  }

  public extendSession(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (session && session.isActive) {
      const now = Date.now()
      session.lastActivity = now
      session.expiresAt = now + this.config.maxIdleTime
    }
  }

  public getTimeoutInfo(sessionId: string): SessionTimeoutInfo {
    const session = this.sessions.get(sessionId)
    if (!session || !session.isActive) {
      return {
        timeUntilTimeout: 0,
        isWarning: false,
        warningTime: this.config.warningTime,
        maxIdleTime: this.config.maxIdleTime,
        isTimedOut: true,
        reason: 'idle',
        idleTimeRemaining: 0,
        absoluteTimeRemaining: 0,
        showWarning: false,
        shouldTimeout: true,
      }
    }

    const now = Date.now()
    const timeSinceActivity = now - session.lastActivity
    const idleTimeRemainingMs = this.config.maxIdleTime - timeSinceActivity
    const absoluteTimeRemainingMs = session.absoluteExpiresAt
      ? session.absoluteExpiresAt - now
      : Infinity

    // Convert to seconds for the test expectations
    const idleTimeRemaining = Math.floor(idleTimeRemainingMs / 1000)
    const absoluteTimeRemaining =
      absoluteTimeRemainingMs === Infinity
        ? Infinity
        : Math.floor(absoluteTimeRemainingMs / 1000)

    const timeUntilTimeout = Math.min(
      idleTimeRemainingMs,
      absoluteTimeRemainingMs === Infinity
        ? idleTimeRemainingMs
        : absoluteTimeRemainingMs
    )
    const isWarning =
      timeUntilTimeout > 0 && timeUntilTimeout <= this.config.warningTime
    const showWarning = isWarning
    const isTimedOut = timeUntilTimeout <= 0
    const shouldTimeout = isTimedOut

    let reason: 'idle' | 'absolute' | null = null
    if (isTimedOut) {
      reason =
        idleTimeRemainingMs <=
        (absoluteTimeRemainingMs === Infinity
          ? idleTimeRemainingMs
          : absoluteTimeRemainingMs)
          ? 'idle'
          : 'absolute'
    }

    return {
      timeUntilTimeout,
      isWarning,
      warningTime: this.config.warningTime,
      maxIdleTime: this.config.maxIdleTime,
      absoluteTimeRemaining,
      isTimedOut,
      reason,
      idleTimeRemaining,
      showWarning,
      shouldTimeout,
    }
  }

  public getSession(sessionId: string): SessionMetadata | null {
    return this.sessions.get(sessionId) || null
  }

  public getUserSessions(
    userId: string
  ): Array<SessionMetadata & { isCurrent?: boolean }> {
    const userSessionIds = this.userSessions.get(userId) || new Set()
    const sessions: Array<SessionMetadata & { isCurrent?: boolean }> = []

    for (const sessionId of Array.from(userSessionIds)) {
      const session = this.sessions.get(sessionId)
      if (session && session.isActive) {
        sessions.push({
          ...session,
          isCurrent: false, // This would be determined by comparing with current session
        })
      }
    }

    // Sort by last activity (most recent first)
    return sessions.sort((a, b) => b.lastActivity - a.lastActivity)
  }

  public enforceMaxSessions(userId: string, currentSessionId: string): void {
    const maxSessions = this.config.maxConcurrentSessions || 3
    const userSessionIds = this.userSessions.get(userId) || new Set()

    if (userSessionIds.size <= maxSessions) {
      return
    }

    // Get all sessions for the user, sorted by last activity (oldest first)
    const sessions = Array.from(userSessionIds)
      .map(id => this.sessions.get(id))
      .filter(
        (session): session is SessionMetadata =>
          session !== undefined && session.isActive
      )
      .sort((a, b) => a.lastActivity - b.lastActivity)

    // Terminate oldest sessions, keeping the current session and most recent ones
    const sessionsToTerminate = sessions.slice(
      0,
      sessions.length - maxSessions + 1
    )

    for (const session of sessionsToTerminate) {
      if (session.sessionId !== currentSessionId) {
        void this.terminateSession(session.sessionId, 'max_sessions_exceeded')
      }
    }
  }

  public cleanupExpiredSessions(): number {
    const now = Date.now()
    let cleanedCount = 0

    for (const [sessionId, session] of Array.from(this.sessions.entries())) {
      if (this.isSessionExpired(session, now)) {
        void this.terminateSession(sessionId, 'expired')
        cleanedCount++
      }
    }

    return cleanedCount
  }

  public clearAllSessions(): void {
    this.sessions.clear()
    this.userSessions.clear()
  }

  public getSessionStats() {
    const now = Date.now()
    let totalActiveSessions = 0

    for (const session of Array.from(this.sessions.values())) {
      if (session.isActive && !this.isSessionExpired(session, now)) {
        totalActiveSessions++
      }
    }

    const uniqueUsers = this.userSessions.size
    const averageSessionsPerUser =
      uniqueUsers > 0 ? totalActiveSessions / uniqueUsers : 0

    // Calculate max concurrent sessions per user
    let maxConcurrentSessions = 0
    for (const userSessionIds of Array.from(this.userSessions.values())) {
      const activeSessionsForUser = Array.from(userSessionIds)
        .map((id: string) => this.sessions.get(id))
        .filter(
          session =>
            session && session.isActive && !this.isSessionExpired(session, now)
        ).length
      maxConcurrentSessions = Math.max(
        maxConcurrentSessions,
        activeSessionsForUser
      )
    }

    return {
      totalActiveSessions,
      uniqueUsers,
      averageSessionsPerUser,
      maxConcurrentSessions,
    }
  }
}

// Global session manager instance
export const sessionManager = new SessionSecurityManager()

export function validateSessionSecurity(
  request?: RequestLike,
  sessionId?: string
): Promise<SessionValidationResult> {
  if (!sessionId) {
    return Promise.resolve({
      valid: false,
      reason: 'No session ID provided',
    })
  }

  const session = sessionManager.getSession(sessionId)
  if (!session) {
    return Promise.resolve({
      valid: false,
      reason: 'Session not found',
    })
  }

  if (!session.isActive) {
    return Promise.resolve({
      valid: false,
      reason: 'Session is inactive',
    })
  }

  const timeoutInfo = sessionManager.getTimeoutInfo(sessionId)
  if (timeoutInfo.isTimedOut) {
    return Promise.resolve({
      valid: false,
      reason: `Session timeout (${timeoutInfo.reason})`,
      timeoutInfo,
    })
  }

  // Validate device fingerprint if request is provided
  if (request) {
    const requestInfo = extractRequestInfo(request)
    const currentFingerprint = sessionManager.generateDeviceFingerprint(
      requestInfo.ip_address,
      requestInfo.user_agent
    )

    if (currentFingerprint !== session.deviceFingerprint) {
      return Promise.resolve({
        valid: false,
        reason: 'Device fingerprint mismatch',
        securityViolation: true,
        timeoutInfo,
      })
    }
  }

  return Promise.resolve({
    valid: true,
    timeoutInfo,
  })
}
