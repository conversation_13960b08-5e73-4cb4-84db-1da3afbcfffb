import { createServerClient } from '@supabase/ssr'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { cookies } from 'next/headers'

export function createSecureServerClient(): SupabaseClient<Database> {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: { path?: string }) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: { path?: string }) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )
}
