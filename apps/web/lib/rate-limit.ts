import { NextRequest } from 'next/server'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

interface RateLimitEntry {
  count: number
  resetTime: number
}

// In-memory store for rate limiting
// In production, you'd want to use Redis or another persistent store
const rateLimitStore = new Map<string, RateLimitEntry>()

export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = {
      keyGenerator: (request) => this.getClientIP(request),
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    }
  }

  private getClientIP(request: NextRequest): string {
    // Try to get real IP from various headers
    const forwarded = request.headers.get('x-forwarded-for')
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    const realIP = request.headers.get('x-real-ip')
    if (realIP) {
      return realIP
    }
    
    // Fallback to connection remote address
    return request.ip || 'unknown'
  }

  public checkLimit(request: NextRequest): {
    allowed: boolean
    remaining: number
    resetTime: number
    totalHits: number
  } {
    const key = this.config.keyGenerator!(request)
    const now = Date.now()
    const windowStart = now - this.config.windowMs
    
    // Clean up old entries
    this.cleanup(windowStart)
    
    let entry = rateLimitStore.get(key)
    
    if (!entry || entry.resetTime <= now) {
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs
      }
    }
    
    entry.count++
    rateLimitStore.set(key, entry)
    
    const allowed = entry.count <= this.config.maxRequests
    const remaining = Math.max(0, this.config.maxRequests - entry.count)
    
    return {
      allowed,
      remaining,
      resetTime: entry.resetTime,
      totalHits: entry.count
    }
  }

  private cleanup(windowStart: number) {
    // Remove entries that are outside the current window
    const keysToDelete: string[] = []
    rateLimitStore.forEach((entry, key) => {
      if (entry.resetTime <= windowStart) {
        keysToDelete.push(key)
      }
    })
    keysToDelete.forEach(key => rateLimitStore.delete(key))
  }

  public reset(key?: string) {
    if (key) {
      rateLimitStore.delete(key)
    } else {
      rateLimitStore.clear()
    }
  }
}

// Predefined rate limiters for common use cases
export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5 // 5 attempts per 15 minutes
})

export const apiRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100 // 100 requests per minute
})

export const strictRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10 // 10 requests per minute
})

export function createRateLimitResponse(result: ReturnType<RateLimiter['checkLimit']>) {
  return new Response(
    JSON.stringify({
      error: 'Too many requests',
      retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Limit': String(result.totalHits),
        'X-RateLimit-Remaining': String(result.remaining),
        'X-RateLimit-Reset': String(Math.ceil(result.resetTime / 1000)),
        'Retry-After': String(Math.ceil((result.resetTime - Date.now()) / 1000))
      }
    }
  )
}

// Helper function to get client IP
export function getClientIP(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  const realIP = request.headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }

  // Fallback to connection remote address
  return request.ip || 'unknown'
}

// Additional exports for middleware
export const getClientIdentifier = getClientIP

export const RATE_LIMIT_CONFIG = {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100 // 100 requests per minute
}

export function checkRateLimit(request: NextRequest) {
  return apiRateLimiter.checkLimit(request)
}

export function createRateLimitHeaders(result: ReturnType<RateLimiter['checkLimit']>) {
  return {
    'X-RateLimit-Limit': String(result.totalHits),
    'X-RateLimit-Remaining': String(result.remaining),
    'X-RateLimit-Reset': String(Math.ceil(result.resetTime / 1000)),
    'Retry-After': String(Math.ceil((result.resetTime - Date.now()) / 1000))
  }
}

export function logRateLimitViolation(request: NextRequest, result: ReturnType<RateLimiter['checkLimit']>) {
  console.warn('Rate limit violation:', {
    ip: getClientIP(request),
    path: request.nextUrl.pathname,
    remaining: result.remaining,
    resetTime: new Date(result.resetTime).toISOString()
  })
}
