// API request/response type definitions for type-safe API routes

export interface InviteRequest {
  scope: 'tenant' | 'entity'
  scopeId: string | number
  email: string
  role: string
}

export interface InviteResponse {
  success: boolean
  inviteToken?: string
  message: string
}

export interface InviteListResponse {
  invites: Array<{
    created_at: string
    email: string
    expires_at: string
    inviter_user_id: string
    role: string
    scope: string
    scope_id: string
    token: string
  }>
}

export interface AcceptInviteRequest {
  token: string
}

export interface AcceptInviteResponse {
  success: boolean
  message: string
}

export interface RoleRequest {
  scope: 'tenant' | 'entity'
  scopeId: string | number
  targetUserId: string
  role: string
}

export interface RoleResponse {
  success: boolean
  message: string
}

export interface TenantRequest {
  name: string
  description?: string
  org_type?: 'sme' | 'firm'
}

export interface TenantResponse {
  tenant: {
    id: number
    name: string
    kind: string | null
    org_type: 'sme' | 'firm'
    created_at: string
    updated_at: string
  }
}

export interface TenantsListResponse {
  tenants: Array<{
    role: string | null
    tenant_id: number | null
    tenant_kind: string | null
    org_type: 'sme' | 'firm'
    tenant_name: string | null
    user_id: string | null
  }>
}

export interface UserContextResponse {
  tenant: {
    id: number
    name: string
    org_type: 'sme' | 'firm'
    role: string
  } | null
  entities: Array<{
    id: number
    name: string
    is_default: boolean
    role: string
  }>
  rolesPerEntity: Record<number, string>
}

export interface ErrorResponse {
  error: string
}

// Type guards for runtime validation
export function isInviteRequest(body: unknown): body is InviteRequest {
  return (
    typeof body === 'object' &&
    body !== null &&
    typeof (body as InviteRequest).scope === 'string' &&
    ['tenant', 'entity'].includes((body as InviteRequest).scope) &&
    ((typeof (body as InviteRequest).scopeId === 'string') || (typeof (body as InviteRequest).scopeId === 'number')) &&
    typeof (body as InviteRequest).email === 'string' &&
    typeof (body as InviteRequest).role === 'string'
  )
}

export function isRoleRequest(body: unknown): body is RoleRequest {
  return (
    typeof body === 'object' &&
    body !== null &&
    typeof (body as RoleRequest).scope === 'string' &&
    ['tenant', 'entity'].includes((body as RoleRequest).scope) &&
    ((typeof (body as RoleRequest).scopeId === 'string') || (typeof (body as RoleRequest).scopeId === 'number')) &&
    typeof (body as RoleRequest).targetUserId === 'string' &&
    typeof (body as RoleRequest).role === 'string'
  )
}

export function isTenantRequest(body: unknown): body is TenantRequest {
  return (
    typeof body === 'object' &&
    body !== null &&
    typeof (body as TenantRequest).name === 'string' &&
    (body as TenantRequest).name.trim().length > 0 &&
    (
      (body as TenantRequest).description === undefined ||
      typeof (body as TenantRequest).description === 'string'
    ) &&
    (
      (body as TenantRequest).org_type === undefined ||
      ['sme', 'firm'].includes((body as TenantRequest).org_type!)
    )
  )
}