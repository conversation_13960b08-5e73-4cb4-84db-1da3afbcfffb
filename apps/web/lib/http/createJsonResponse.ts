/**
 * Creates a standard JSON Response that works with Next.js route handlers
 * without requiring NextResponse mocking in tests.
 * 
 * @param data - The data to serialize as JSON
 * @param init - Either a status code number or full ResponseInit object
 * @returns Standard Response object compatible with Next.js route handlers
 */
export function createJsonResponse<T>(
  data: T,
  init: number | ResponseInit = 200
): Response {
  const baseInit: ResponseInit = typeof init === 'number' ? { status: init } : init;
  const headers = new Headers(baseInit.headers);
  
  if (!headers.has('content-type')) {
    headers.set('content-type', 'application/json; charset=utf-8');
  }
  
  return new Response(JSON.stringify(data), { ...baseInit, headers });
}
