export interface PasswordRequirement {
  id: string
  label: string
  test: (password: string) => boolean
}

export interface PasswordValidationResult {
  isValid: boolean
  failedRequirements: PasswordRequirement[]
}

export const passwordRequirements: PasswordRequirement[] = [
  {
    id: 'length',
    label: 'At least 8 characters',
    test: (password: string) => password.length >= 8
  },
  {
    id: 'uppercase',
    label: 'At least one uppercase letter',
    test: (password: string) => /[A-Z]/.test(password)
  },
  {
    id: 'lowercase',
    label: 'At least one lowercase letter',
    test: (password: string) => /[a-z]/.test(password)
  },
  {
    id: 'number',
    label: 'At least one number',
    test: (password: string) => /\d/.test(password)
  },
  {
    id: 'special',
    label: 'At least one special character',
    test: (password: string) => /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
]

export function validatePassword(password: string): PasswordValidationResult {
  const failedRequirements = passwordRequirements.filter(req => !req.test(password))
  
  return {
    isValid: failedRequirements.length === 0,
    failedRequirements
  }
}

export function getPasswordStrength(password: string): {
  score: number
  label: string
  color: string
} {
  const passedRequirements = passwordRequirements.filter(req => req.test(password)).length
  const score = (passedRequirements / passwordRequirements.length) * 100

  if (score < 40) {
    return { score, label: 'Weak', color: 'red' }
  } else if (score < 80) {
    return { score, label: 'Medium', color: 'yellow' }
  } else {
    return { score, label: 'Strong', color: 'green' }
  }
}

export function getPasswordStrengthIndicators(password: string) {
  const strength = getPasswordStrength(password)
  const validation = validatePassword(password)

  return {
    strength,
    validation,
    requirements: passwordRequirements.map(req => ({
      ...req,
      met: req.test(password)
    }))
  }
}

// Synchronous password validation (alias for validatePassword)
export const validatePasswordSync = validatePassword

// Default password policy export
export const DEFAULT_PASSWORD_POLICY = {
  requirements: passwordRequirements,
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true
}
