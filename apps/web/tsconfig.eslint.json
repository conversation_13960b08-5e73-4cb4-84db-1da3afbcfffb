{"extends": "./tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@belbooks/types": ["../../packages/types/src/index.ts"], "@belbooks/types/*": ["../../packages/types/src/*"], "@belbooks/dal": ["../../packages/dal/src/index.ts"], "@belbooks/dal/*": ["../../packages/dal/src/*"], "@belbooks/import-service": ["../../packages/import-service/src/index.ts"], "@belbooks/import-service/*": ["../../packages/import-service/src/*"], "@belbooks/domain-invoicing": ["../../packages/domain-invoicing/src/index.ts"], "@belbooks/domain-invoicing/*": ["../../packages/domain-invoicing/src/*"]}}, "include": ["app/**/*.ts", "app/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "contexts/**/*.ts", "contexts/**/*.tsx", "hooks/**/*.ts", "hooks/**/*.tsx", "lib/**/*.ts", "lib/**/*.tsx", "../../packages/dal/src/**/*.ts", "../../packages/types/src/**/*.ts", "../../packages/import-service/src/**/*.ts", "../../packages/domain-invoicing/src/**/*.ts"]}