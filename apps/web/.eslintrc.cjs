module.exports = {
  root: true,
  extends: ['next/core-web-vitals', require('path').relative(__dirname, require.resolve('../../.eslintrc.json'))],
  parserOptions: {
    tsconfigRootDir: __dirname,
    project: ['./tsconfig.eslint.json', './tsconfig.json'],
  },
  settings: {
    react: { version: '18' },
    next: { rootDir: '.' },
  },
  rules: {
    '@next/next/no-html-link-for-pages': 'off',
    'react/no-unescaped-entities': 'error',
    'react-hooks/exhaustive-deps': 'error',
  },
  overrides: [
    {
      files: ['lib/security-monitoring.ts'],
      rules: {
        '@typescript-eslint/unbound-method': 'off',
      },
    },
    {
      files: ['lib/session-security.ts'],
      rules: {
        '@typescript-eslint/unbound-method': 'off',
      },
    },
  ],
  ignorePatterns: ['__tests__/**/*', '**/*.test.ts', '**/*.test.tsx', 'vitest.*.ts', 'dist/', 'node_modules/'],
}
