'use client'

import { useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { signUpWithPassword, signInWithEmail } from '@belbooks/dal'
import { useAuth } from '@/contexts/AuthContext'
import { useCSRF } from '@/hooks/useCSRF'
import {
  PasswordStrength,
  usePasswordValidation,
} from '@/components/PasswordStrength'
import { GoogleSignInButton } from '@/components/GoogleSignInButton'

export default function SignUpPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirm, setShowConfirm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [useMagicLink, setUseMagicLink] = useState(false)
  // Verification message state (disabled for now - auto-confirmation enabled)
  // const [showVerificationMessage, setShowVerificationMessage] = useState(false)
  // const [userEmail, setUserEmail] = useState('')

  const router = useRouter()
  const { user } = useAuth()
  const { createCSRFInput } = useCSRF()

  // Memoize searchParams to avoid recreating on every render
  const searchParams = useMemo(() => {
    return new URLSearchParams(
      typeof window !== 'undefined' ? window.location.search : ''
    )
  }, [])

  const { isValid: isPasswordValid, indicators } =
    usePasswordValidation(password)

  // Redirect if already logged in (effect to avoid router updates during render)
  const redirectTarget = useMemo(() => {
    const redirectTo = searchParams.get('redirectTo')
    return redirectTo || '/onboarding'
  }, [searchParams])

  // Extract invitation token if present in redirect URL
  const inviteToken = useMemo(() => {
    const redirectTo = searchParams.get('redirectTo')
    if (redirectTo) {
      try {
        const url = new URL(redirectTo, window.location.origin)
        return url.searchParams.get('invite_token')
      } catch {
        return null
      }
    }
    return null
  }, [searchParams])

  useEffect(() => {
    if (user) router.replace(redirectTarget)
  }, [user, redirectTarget, router])

  const handleSubmitAsync = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setMessage('')

    try {
      if (!useMagicLink) {
        if (
          password.length !== confirmPassword.length ||
          password !== confirmPassword
        ) {
          throw new Error('Passwords do not match')
        }
        if (!isPasswordValid) {
          throw new Error('Please meet the password requirements')
        }
        // Create account via password
        const redirectUrl = new URL('/auth/callback', window.location.origin)
        redirectUrl.searchParams.set('redirectTo', '/onboarding')
        const { user } = await signUpWithPassword(
          email,
          password,
          redirectUrl.toString()
        )

        if (user) {
          // User is automatically signed in (autoconfirm enabled)
          setMessage(
            'Account created successfully! Redirecting to your dashboard...'
          )
          // The useEffect hook will handle the redirect when user state updates
        } else {
          // Fallback case - shouldn't happen with autoconfirm enabled
          setMessage('Account created. Please wait while we sign you in...')
        }
        return
      }

      // Magic link fallback
      const redirectUrl = new URL('/auth/callback', window.location.origin)
      redirectUrl.searchParams.set('redirectTo', '/onboarding')
      await signInWithEmail(email, redirectUrl.toString())
      setMessage('Check your email for the signup link!')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent): void => {
    void handleSubmitAsync(e)
  }

  // Resend verification function (disabled - auto-confirmation enabled)
  // const handleResendVerification = async (): Promise<void> => {
  //   setLoading(true)
  //   setError('')
  //   try {
  //     const redirectUrl = new URL('/auth/callback', window.location.origin)
  //     redirectUrl.searchParams.set('redirectTo', '/onboarding')
  //     await signInWithEmail(userEmail, redirectUrl.toString())
  //     setMessage('Verification email sent! Please check your inbox.')
  //   } catch (err) {
  //     setError(err instanceof Error ? err.message : 'Failed to resend verification email')
  //   } finally {
  //     setLoading(false)
  //   }
  // }

  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <p className="text-gray-700">Redirecting…</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Create your BelBooks account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {useMagicLink
              ? 'Enter your email to receive a signup link'
              : 'Sign up with email and password'}
          </p>
        </div>

        {/* Email Verification Message - Disabled (auto-confirmation enabled) */}
        {false && (
          <div className="bg-green-50 border border-green-200 rounded-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-green-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  Account created successfully! 🎉
                </h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>
                    To continue, please check your email and click the
                    verification link we just sent you.
                  </p>
                </div>
                <div className="mt-4">
                  <div className="flex space-x-4">
                    <button
                      disabled={loading}
                      className="text-sm bg-green-100 text-green-800 hover:bg-green-200 px-3 py-1 rounded-md disabled:opacity-50"
                    >
                      {loading ? 'Sending...' : 'Resend verification email'}
                    </button>
                    <button className="text-sm text-green-600 hover:text-green-500">
                      Use different email
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Google Sign-Up Option */}
        <div className="mt-8">
          <GoogleSignInButton
            redirectTo={redirectTarget}
            inviteToken={inviteToken || undefined}
            disabled={loading}
          >
            {loading ? 'Signing up...' : 'Sign up with Google'}
          </GoogleSignInButton>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-gray-50 text-gray-500">
              Or sign up with email
            </span>
          </div>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit}>
          {createCSRFInput()}
          <div>
            <label htmlFor="email" className="sr-only">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              placeholder="Enter your email address"
            />
          </div>

          {!useMagicLink && (
            <>
              <div>
                <label htmlFor="password" className="sr-only">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    placeholder="Create a password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(v => !v)}
                    className="absolute inset-y-0 right-0 px-3 text-gray-500 hover:text-gray-700"
                  >
                    {showPassword ? '🙈' : '👁️'}
                  </button>
                </div>
              </div>
              <div>
                <label htmlFor="confirm" className="sr-only">
                  Confirm password
                </label>
                <div className="relative">
                  <input
                    id="confirm"
                    name="confirm"
                    type={showConfirm ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                    className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirm(v => !v)}
                    className="absolute inset-y-0 right-0 px-3 text-gray-500 hover:text-gray-700"
                  >
                    {showConfirm ? '🙈' : '👁️'}
                  </button>
                </div>
              </div>
              <div>
                <PasswordStrength password={password} />
                {indicators && (
                  <p className="mt-1 text-xs text-gray-500">
                    Use at least 8 characters, with upper/lowercase, a number
                    and a symbol.
                  </p>
                )}
              </div>
            </>
          )}

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {message && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="text-sm text-green-700">{message}</div>
            </div>
          )}

          <div className="flex flex-col gap-3">
            <button
              type="submit"
              disabled={loading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading
                ? 'Creating…'
                : useMagicLink
                  ? 'Send signup link'
                  : 'Create account'}
            </button>
            <button
              type="button"
              onClick={() => setUseMagicLink(v => !v)}
              className="w-full text-sm text-blue-600 hover:text-blue-500"
            >
              {useMagicLink ? 'Use password instead' : 'Use magic link instead'}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <a
                href="/login"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Sign in instead
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
