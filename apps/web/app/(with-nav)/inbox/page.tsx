'use client'

import { useState, useMemo, CSSProperties } from 'react'
import { useRouter } from 'next/navigation'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'
import { ZenIcons } from '@/components/ZenIcons'

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'



// Zen UI Theme - Following the style guide exactly
const zenTheme = {
  // Primary Colors - CORRECTED to match style guide
  bg: '#FBFAF5', // soft cream background (was wrong before)
  surface: '#FFFFFC', // pure white for cards
  primaryText: '#1a1a1a', // near black
  secondaryText: '#6b7280', // warm gray
  subtleText: '#9ca3af', // light gray

  // Borders and Surfaces
  border: '#f3f4f6', // subtle border
  borderHover: '#e5e7eb', // slightly darker on hover

  // Accent Colors (use sparingly)
  success: '#10b981', // soft green
  warning: '#f59e0b', // warm amber
  error: '#ef4444', // soft red
  primaryAction: '#3b82f6', // calm blue (not black for primary action)

  // Shadows and Effects
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)', // very subtle
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', // slightly more on hover
}

// Zen UI Style functions
const getPageStyle = (theme: typeof zenTheme): CSSProperties => ({
  color: theme.primaryText,
  fontFamily:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
  lineHeight: 1.5, // improved readability
})

const getCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced for cleaner look
  boxShadow: theme.shadow,
  transition: 'all 0.15s ease', // subtle interaction
})

const getButtonStyle = (
  theme: typeof zenTheme,
  variant: 'primary' | 'secondary' = 'secondary'
): CSSProperties => ({
  padding: '10px 20px', // following style guide
  border: variant === 'primary' ? 'none' : `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for cleaner look
  background: variant === 'primary' ? '#1a1a1a' : 'transparent', // confident near-black for buttons
  color: variant === 'primary' ? '#FFFFFF' : theme.secondaryText,
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
  transition: 'all 0.15s ease', // faster, more subtle
})

const getSelectStyle = (theme: typeof zenTheme): CSSProperties => ({
  padding: '10px 12px', // slightly increased
  border: `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for consistency
  background: theme.surface,
  color: theme.primaryText,
  fontSize: '14px',
  outline: 'none',
  transition: 'all 0.15s ease',
})

export default function InboxPage() {
  const router = useRouter()
  const supabase = useMemo(() => createSecureBrowserClient(), [])
  const { user } = useAuth()
  const {
    currentEntity,
    entities,
    isValid,
    isSME,
    isFirm,
    shouldShowEntitySelector,
    userContext
  } = useOrgEntitySelection()

  const [showUpload, setShowUpload] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [selectedEntityId, setSelectedEntityId] = useState<string | null>(
    currentEntity?.entity_id ? String(currentEntity.entity_id) : null
  )
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Use Zen UI theme
  const theme = zenTheme

  // Derived destination for SMEs for display purposes
  const smeDestinationName: string | null = isSME
    ? (
        currentEntity?.entity_name ||
        userContext?.entities?.[0]?.name ||
        (entities.length === 1 ? entities[0].entity_name : null) ||
        null
      )
    : null

  const onOpenUpload = () => {
    setError(null)
    setSuccess(null)
    setFile(null)
    // Default the selection smartly depending on context
    //  - Firms: preselect the current entity if any
    //  - Otherwise fall back to a single available entity if exactly one exists
    const defaultEntityId =
      currentEntity?.entity_id ??
      (userContext?.entities?.length === 1
        ? userContext.entities[0].id
        : entities.length === 1
          ? entities[0].entity_id
          : null)
    setSelectedEntityId(defaultEntityId ? String(defaultEntityId) : null)

    // Debug logging
    console.log('Upload modal opening with:', {
      isSME,
      isFirm,
      shouldShowEntitySelector,
      isValid,
      currentEntity: currentEntity?.entity_name,
      entities: entities.length
    })

    setShowUpload(true)
  }

  const sanitizeName = (name: string) =>
    name.replace(/[^a-zA-Z0-9._-]/g, '-').toLowerCase()

  const handleUpload = async () => {
    try {
      setError(null)
      setSuccess(null)

      if (!user) {
        setError('You must be signed in to upload')
        return
      }
      if (!file) {
        setError('Please choose a file')
        return
      }
      // Resolve destination entity id robustly
      // SMEs: auto-pick current entity; if not yet resolved, fall back to single available entity
      // Firms: prefer explicit selection; otherwise use currently active entity
      let entityId: number | null = null
      if (isSME) {
        entityId =
          currentEntity?.entity_id ??
          (userContext?.entities?.length === 1
            ? userContext.entities[0].id
            : entities.length === 1
              ? entities[0].entity_id
              : null)
      } else {
        entityId = selectedEntityId
          ? Number(selectedEntityId)
          : (currentEntity?.entity_id ?? null)
      }

      // Final universal fallback: if there is exactly one entity visible, use it
      if (!entityId) {
        entityId = (userContext?.entities?.length === 1
          ? userContext.entities[0].id
          : entities.length === 1
            ? entities[0].entity_id
            : null)
      }

      if (!entityId) {
        setError(isFirm ? 'Please select a client file' : 'No entity available for upload')
        return
      }

      setUploading(true)

      const safeName = sanitizeName(file.name)
      const now = new Date()
      const yyyy = now.getFullYear()
      const mm = String(now.getMonth() + 1).padStart(2, '0')
      const objectKeyBase = `${yyyy}/${mm}/${Date.now()}-${safeName}`

      // 1) Upload file to Supabase Storage with bucket fallback
      const getObjectKeyForBucket = (bucket: string) => {
        // For 'inbox' bucket, do not prefix with 'inbox/' to avoid double nesting
        if (bucket === 'inbox') return `${entityId}/${objectKeyBase}`
        // For other buckets, keep files under an 'inbox/' prefix
        return `inbox/${entityId}/${objectKeyBase}`
      }

      const uploadToBucket = async (bucket: string) => {
        const key = getObjectKeyForBucket(bucket)
        const res = await supabase.storage
          .from(bucket)
          .upload(key, file, {
            cacheControl: '3600',
            upsert: false,
            contentType: file.type || 'application/octet-stream',
          })
        return { ...res, key }
      }

      let uploadData: { path: string } | null = null
      let uploadError: { message?: string } | null = null
      let bucketUsed = 'inbox'

      try {
        const preferred = process.env.NEXT_PUBLIC_STORAGE_BUCKET
        const candidates = [preferred, 'inbox', 'documents', 'invoices'].filter(Boolean) as string[]
        for (const bucket of candidates) {
          const res = await uploadToBucket(bucket)
          uploadData = res.data as { path: string } | null
          uploadError = (res as unknown as { error?: { message: string } }).error || null
          if (uploadData && !uploadError) {
            bucketUsed = bucket
            break
          }
        }
      } catch (e) {
        uploadError = { message: e instanceof Error ? e.message : 'Upload failed' }
      }

      if (uploadError || !uploadData) {
        setError(uploadError?.message || 'Upload failed')
        setUploading(false)
        return
      }

      // 2) Notify BFF to create document and start processing
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const resp = await fetch(
        `${BFF_BASE_URL}/entities/${entityId}/documents`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
          body: JSON.stringify({
            entity_id: entityId,
            path: uploadData.path,
            mime_type: file.type || 'application/octet-stream',
            source: 'upload',
            // Optional: include bucketUsed in case server wants to use it later
            bucket: bucketUsed,
          }),
        }
      )

      if (!resp.ok) {
        const text = await resp.text()
        throw new Error(text || `BFF responded with ${resp.status}`)
      }

      setSuccess('Document uploaded. Processing has started.')
      setTimeout(() => {
        setShowUpload(false)
        // Optionally refresh the page or navigate; for now, stay on inbox
        router.refresh()
      }, 900)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Unexpected error')
    } finally {
      setUploading(false)
    }
  }
  return (
    <div style={getPageStyle(theme)}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ marginBottom: '32px' }}>
          <h1
            style={{
              fontSize: '32px',
              fontWeight: 600,
              color: theme.primaryText,
              margin: '0 0 8px 0',
            }}
          >
            Inbox
          </h1>
          <p
            style={{
              fontSize: '16px',
              color: theme.secondaryText,
              margin: 0,
            }}
          >
            Manage incoming documents and transactions
          </p>
        </div>

        {/* Filters and Actions */}
        <div
          style={{
            marginBottom: '24px',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '16px',
            }}
          >
            <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
              <select style={getSelectStyle(theme)}>
                <option>All Items</option>
                <option>Unprocessed</option>
                <option>Processed</option>
                <option>Requires Review</option>
              </select>
              <select style={getSelectStyle(theme)}>
                <option>All Types</option>
                <option>Invoices</option>
                <option>Receipts</option>
                <option>Bank Statements</option>
              </select>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={onOpenUpload}
                style={getButtonStyle(theme, 'primary')}
                aria-label="Upload document"
              >
                Upload Document
              </button>
              <button style={getButtonStyle(theme, 'secondary')}>
                Batch Process
              </button>
            </div>
          </div>
        </div>

        {/* Inbox List */}
        <div style={getCardStyle(theme)}>
          <div
            style={{
              padding: '24px',
              borderBottom: `1px solid ${theme.border}`,
            }}
          >
            <h2
              style={{
                fontSize: '20px',
                fontWeight: 600,
                color: theme.primaryText,
                margin: 0,
              }}
            >
              Document Queue
            </h2>
          </div>

          <div>
            {/* Sample inbox items */}
            {[
              {
                id: 1,
                type: 'Invoice',
                description: 'Office Supplies - Invoice #INV-001',
                date: '2024-08-20',
                amount: '€234.50',
                status: 'Unprocessed',
                statusColor: { bg: 'transparent', text: theme.warning }, // muted amber
              },
              {
                id: 2,
                type: 'Receipt',
                description: 'Business Lunch - Receipt',
                date: '2024-08-19',
                amount: '€45.20',
                status: 'Requires Review',
                statusColor: { bg: 'transparent', text: '#3b82f6' }, // calm blue
              },
              {
                id: 3,
                type: 'Bank Statement',
                description: 'Monthly Bank Statement - August 2024',
                date: '2024-08-18',
                amount: '€12,450.00',
                status: 'Processed',
                statusColor: { bg: 'transparent', text: theme.success }, // soft green
              },
            ].map(item => (
              <div
                key={item.id}
                style={{
                  padding: '20px 24px',
                  borderBottom: `1px solid ${theme.border}`,
                  transition: 'background-color 0.2s ease',
                  cursor: 'pointer',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = theme.bg
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                      }}
                    >
                      <div style={{ flexShrink: 0 }}>
                        <div
                          style={{
                            width: '40px',
                            height: '40px',
                            backgroundColor: theme.border,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <span style={{ fontSize: '16px' }}>
                            {item.type === 'Invoice'
                              ? '📄'
                              : item.type === 'Receipt'
                                ? '🧾'
                                : '🏦'}
                          </span>
                        </div>
                      </div>

                      <div style={{ flex: 1 }}>
                        <p
                          style={{
                            fontSize: '16px',
                            fontWeight: 500,
                            color: theme.primaryText,
                            margin: '0 0 4px 0',
                          }}
                        >
                          {item.description}
                        </p>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                          }}
                        >
                          <span
                            style={{
                              fontSize: '14px',
                              color: theme.secondaryText,
                            }}
                          >
                            {item.type}
                          </span>
                          <span
                            style={{
                              fontSize: '14px',
                              color: theme.secondaryText,
                            }}
                          >
                            {item.date}
                          </span>
                          <span
                            style={{
                              fontSize: '14px',
                              fontWeight: 500,
                              color: theme.primaryText,
                            }}
                          >
                            {item.amount}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                    }}
                  >
                    <span
                      style={{
                        fontSize: '12px',
                        fontWeight: 500,
                        color: item.statusColor.text,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                      }}
                    >
                      {item.status}
                    </span>

                    <div style={{ display: 'flex', gap: '4px' }}>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: theme.secondaryText,
                          cursor: 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = theme.primaryText;
                          e.currentTarget.style.backgroundColor = theme.border;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = theme.secondaryText;
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                        aria-label="View document"
                      >
                        {ZenIcons.view()}
                      </button>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: theme.secondaryText,
                          cursor: 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = theme.primaryText;
                          e.currentTarget.style.backgroundColor = theme.border;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = theme.secondaryText;
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                        aria-label="Edit document"
                      >
                        {ZenIcons.edit()}
                      </button>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: theme.secondaryText,
                          cursor: 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = theme.error;
                          e.currentTarget.style.backgroundColor = theme.border;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = theme.secondaryText;
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                        aria-label="Delete document"
                      >
                        {ZenIcons.delete()}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div
            style={{
              padding: '20px 24px',
              textAlign: 'center',
              borderTop: `1px solid ${theme.border}`,
            }}
          >
            <p
              style={{
                fontSize: '14px',
                color: theme.secondaryText,
                margin: 0,
              }}
            >
              Showing 3 of 3 items
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div
          style={{
            marginTop: '32px',
            display: 'grid',
            gap: '24px',
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          }}
        >
          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>⏳</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Pending
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>🔍</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Review Required
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>✅</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Processed
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Modal */}
        {showUpload && (
          <div
            style={{
              position: 'fixed',
              inset: 0,
              zIndex: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.4)',
            }}
          >
            <div
              style={{
                ...getCardStyle(theme),
                width: '100%',
                maxWidth: '480px',
                padding: '32px',
                margin: '16px',
              }}
            >
              <h3
                style={{
                  fontSize: '20px',
                  fontWeight: 600,
                  color: theme.primaryText,
                  margin: '0 0 8px 0',
                }}
              >
                Upload Document
              </h3>
              <p
                style={{
                  fontSize: '14px',
                  color: theme.secondaryText,
                  margin: '0 0 24px 0',
                }}
              >
                {isSME
                  ? 'PDFs and images are supported. Documents will be added to your company books.'
                  : 'PDFs and images are supported.'
                }
              </p>

              {/* Entity selection - only show for firms when they need to select/confirm entity */}
              {isFirm && (
                <div style={{ marginBottom: '20px' }}>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: theme.primaryText,
                      marginBottom: '8px',
                    }}
                  >
                    Client File
                  </label>
                  <select
                    value={selectedEntityId ?? ''}
                    onChange={e => setSelectedEntityId(e.target.value || null)}
                    style={{
                      ...getSelectStyle(theme),
                      width: '100%',
                    }}
                  >
                    <option value="">Choose a client file…</option>
                    {entities.map(e => (
                      <option key={e.entity_id} value={String(e.entity_id)}>
                        {e.entity_name}
                      </option>
                    ))}
                  </select>
                  {selectedEntityId && (
                    <p style={{
                      fontSize: '12px',
                      color: theme.secondaryText,
                      margin: '4px 0 0 0',
                    }}>
                      Document will be uploaded to {entities.find(e => e.entity_id === Number(selectedEntityId))?.entity_name}
                    </p>
                  )}
                </div>
              )}

              {/* SME destination info */}
              {isSME && smeDestinationName && (
                <div style={{
                  marginBottom: '20px',
                  padding: '12px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <p style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: 0,
                  }}>
                    <strong>Destination:</strong> {smeDestinationName}
                  </p>
                </div>
              )}

              <div style={{ marginBottom: '20px' }}>
                <label
                  style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: theme.primaryText,
                    marginBottom: '8px',
                  }}
                >
                  Choose File
                </label>
                <input
                  type="file"
                  accept="application/pdf,image/*"
                  onChange={e => setFile(e.target.files?.[0] || null)}
                  style={{
                    display: 'block',
                    width: '100%',
                    fontSize: '14px',
                    color: theme.primaryText,
                    padding: '8px 12px',
                    border: `1px solid ${theme.border}`,
                    borderRadius: '8px',
                    backgroundColor: theme.surface,
                  }}
                />
              </div>

              {error && (
                <p
                  style={{
                    fontSize: '14px',
                    color: '#DC2626',
                    margin: '0 0 16px 0',
                  }}
                >
                  {error}
                </p>
              )}
              {success && (
                <p
                  style={{
                    fontSize: '14px',
                    color: '#059669',
                    margin: '0 0 16px 0',
                  }}
                >
                  {success}
                </p>
              )}

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  gap: '12px',
                  marginTop: '24px',
                }}
              >
                <button
                  onClick={() => setShowUpload(false)}
                  style={{
                    ...getButtonStyle(theme, 'secondary'),
                    opacity: uploading ? 0.6 : 1,
                    cursor: uploading ? 'not-allowed' : 'pointer',
                  }}
                  disabled={uploading}
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    void handleUpload()
                  }}
                  style={{
                    ...getButtonStyle(theme, 'primary'),
                    opacity: uploading ? 0.6 : 1,
                    cursor: uploading ? 'not-allowed' : 'pointer',
                  }}
                  disabled={uploading}
                >
                  {uploading ? 'Uploading…' : 'Upload'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
