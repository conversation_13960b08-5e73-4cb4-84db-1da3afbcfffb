export default function LedgerPage() {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Trial Balance</h1>
        <p className="text-gray-600">
          General ledger accounts and their balances
        </p>
      </div>

      {/* Controls */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>Current Period</option>
            <option>Previous Period</option>
            <option>Year to Date</option>
            <option>Custom Range</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>All Accounts</option>
            <option>Assets</option>
            <option>Liabilities</option>
            <option>Equity</option>
            <option>Income</option>
            <option>Expenses</option>
          </select>
        </div>

        <div className="flex gap-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors">
            Export PDF
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors">
            Export Excel
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors">
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        {[
          {
            label: 'Total Assets',
            amount: '€125,450.00',
            change: '+2.3%',
            positive: true,
          },
          {
            label: 'Total Liabilities',
            amount: '€45,320.00',
            change: '-1.2%',
            positive: true,
          },
          {
            label: 'Total Equity',
            amount: '€80,130.00',
            change: '+3.1%',
            positive: true,
          },
          {
            label: 'Net Income',
            amount: '€12,850.00',
            change: '+15.4%',
            positive: true,
          },
        ].map((item, index) => (
          <div
            key={index}
            className="bg-white rounded-lg shadow p-4 border border-gray-200"
          >
            <p className="text-sm font-medium text-gray-500">{item.label}</p>
            <p className="text-xl font-bold text-gray-900 mt-1">
              {item.amount}
            </p>
            <p
              className={`text-xs mt-1 ${item.positive ? 'text-green-600' : 'text-red-600'}`}
            >
              {item.change} from last period
            </p>
          </div>
        ))}
      </div>

      {/* Trial Balance Table */}
      <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Account Balances
          </h2>
          <p className="text-sm text-gray-500 mt-1">As of August 31, 2024</p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Account Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Account Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Account Type
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Debit Balance
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Credit Balance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {[
                {
                  code: '1000',
                  name: 'Cash and Cash Equivalents',
                  type: 'Assets',
                  debit: '25,450.00',
                  credit: '0.00',
                },
                {
                  code: '1200',
                  name: 'Accounts Receivable',
                  type: 'Assets',
                  debit: '18,750.00',
                  credit: '0.00',
                },
                {
                  code: '1300',
                  name: 'Inventory',
                  type: 'Assets',
                  debit: '35,200.00',
                  credit: '0.00',
                },
                {
                  code: '1500',
                  name: 'Equipment',
                  type: 'Assets',
                  debit: '46,050.00',
                  credit: '0.00',
                },
                {
                  code: '2000',
                  name: 'Accounts Payable',
                  type: 'Liabilities',
                  debit: '0.00',
                  credit: '12,450.00',
                },
                {
                  code: '2100',
                  name: 'VAT Payable',
                  type: 'Liabilities',
                  debit: '0.00',
                  credit: '8,750.00',
                },
                {
                  code: '2200',
                  name: 'Accrued Expenses',
                  type: 'Liabilities',
                  debit: '0.00',
                  credit: '6,120.00',
                },
                {
                  code: '3000',
                  name: 'Share Capital',
                  type: 'Equity',
                  debit: '0.00',
                  credit: '50,000.00',
                },
                {
                  code: '3100',
                  name: 'Retained Earnings',
                  type: 'Equity',
                  debit: '0.00',
                  credit: '30,130.00',
                },
                {
                  code: '4000',
                  name: 'Sales Revenue',
                  type: 'Income',
                  debit: '0.00',
                  credit: '85,450.00',
                },
                {
                  code: '5000',
                  name: 'Cost of Goods Sold',
                  type: 'Expenses',
                  debit: '42,250.00',
                  credit: '0.00',
                },
                {
                  code: '6000',
                  name: 'Operating Expenses',
                  type: 'Expenses',
                  debit: '18,350.00',
                  credit: '0.00',
                },
                {
                  code: '6100',
                  name: 'Rent Expense',
                  type: 'Expenses',
                  debit: '12,000.00',
                  credit: '0.00',
                },
              ].map((account, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {account.code}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {account.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        account.type === 'Assets'
                          ? 'bg-blue-100 text-blue-800'
                          : account.type === 'Liabilities'
                            ? 'bg-red-100 text-red-800'
                            : account.type === 'Equity'
                              ? 'bg-green-100 text-green-800'
                              : account.type === 'Income'
                                ? 'bg-purple-100 text-purple-800'
                                : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {account.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                    {account.debit !== '0.00' ? `€${account.debit}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                    {account.credit !== '0.00' ? `€${account.credit}` : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-end">
            <div className="text-right">
              <div className="flex gap-12">
                <div>
                  <p className="text-sm font-medium text-gray-700">
                    Total Debits
                  </p>
                  <p className="text-lg font-bold text-gray-900">€198,050.00</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">
                    Total Credits
                  </p>
                  <p className="text-lg font-bold text-gray-900">€192,900.00</p>
                </div>
              </div>
              <div className="mt-2 pt-2 border-t border-gray-200">
                <p className="text-sm font-medium text-gray-700">
                  Balance Check
                </p>
                <p className="text-sm text-red-600">
                  Out of balance by €5,150.00
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Actions */}
      <div className="mt-6 flex justify-center">
        <button className="px-4 py-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
          View Detailed General Ledger →
        </button>
      </div>
    </div>
  )
}
