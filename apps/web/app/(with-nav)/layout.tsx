import { Navigation } from '@/components/Navigation'

export default function WithNavLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen" style={{ background: 'var(--zen-bg)' }}>
      <Navigation />
      <main
        className="flex-1"
        style={{
          background: 'var(--zen-bg)',
          padding: '32px',
        }}
      >
        {children}
      </main>
    </div>
  )
}
