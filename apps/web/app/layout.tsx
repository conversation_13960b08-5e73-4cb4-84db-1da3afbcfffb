import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { OrgEntityProvider } from '@/contexts/OrgEntityContext'
import { cookies, headers } from 'next/headers'
import { CSPProvider } from '@/contexts/CSPContext'

export const metadata: Metadata = {
  title: 'BelBooks - Accounting Management',
  description: 'Modern accounting and ledger management application',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Read CSRF token and CSP nonce from headers/cookies.
  // The CSRF cookie is created in middleware; we mirror it into a meta tag for the client.
  const headerStore = headers()
  const cspNonce = headerStore.get('X-CSP-Nonce')
  const csrfHeader = headerStore.get('X-CSRF-Token')
  const cookieStore = cookies()
  const csrfCookie = cookieStore.get('csrf-token')?.value || null
  const csrfToken = csrfHeader || csrfCookie || ''

  return (
    <html lang="en">
      <head>
        <meta name="csrf-token" content={csrfToken} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="min-h-screen bg-background text-foreground">
        <CSPProvider nonce={cspNonce}>
          <AuthProvider>
            <OrgEntityProvider>{children}</OrgEntityProvider>
          </AuthProvider>
        </CSPProvider>
      </body>
    </html>
  )
}
