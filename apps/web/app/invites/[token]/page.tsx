'use client'

import { useEffect, useMemo, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useCSRF } from '@/hooks/useCSRF'
import { GoogleSignInButton } from '@/components/GoogleSignInButton'

type InviteDetails = {
  scope: 'tenant' | 'entity'
  role: string
  inviterEmail: string
  expiresAt: string
  isExpired: boolean
}

type InviteError = { error: string }

export default function AcceptInvitePage({
  params,
}: {
  params: { token: string }
}) {
  const router = useRouter()
  const { user } = useAuth()
  const { addCSRFHeaders } = useCSRF()
  const token = useMemo(() => params.token, [params.token])

  const [loading, setLoading] = useState(true)
  const [details, setDetails] = useState<InviteDetails | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [accepting, setAccepting] = useState(false)
  const [successMsg, setSuccessMsg] = useState<string | null>(null)

  const loadDetails = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const resp = await fetch(`/api/invites/${encodeURIComponent(token)}`)
      const data = (await resp.json()) as InviteDetails | InviteError
      if (!resp.ok || 'error' in data) {
        const message =
          'error' in data ? data.error : 'Failed to load invitation'
        throw new Error(message)
      }
      setDetails(data)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Failed to load invitation')
    } finally {
      setLoading(false)
    }
  }, [token])

  useEffect(() => {
    void loadDetails()
  }, [loadDetails])

  const acceptInvite = async () => {
    setAccepting(true)
    setError(null)
    try {
      const resp = await fetch('/api/invites/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...addCSRFHeaders(),
        },
        body: JSON.stringify({ token }),
      })
      const data = (await resp.json()) as {
        success?: boolean
        message?: string
        error?: string
      }
      if (!resp.ok || data.success !== true) {
        throw new Error(data.error || 'Failed to accept invitation')
      }
      setSuccessMsg('Invitation accepted successfully. Redirecting...')
      // Give users a brief confirmation, then go to dashboard
      setTimeout(() => {
        router.replace('/')
      }, 1000)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Failed to accept invitation')
    } finally {
      setAccepting(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="bg-white rounded-lg shadow p-6 w-full max-w-md">
        <h1 className="text-xl font-semibold text-gray-900 mb-4">
          Accept Invitation
        </h1>

        {loading ? (
          <div className="text-gray-600">Loading invitation…</div>
        ) : error ? (
          <div className="space-y-4">
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
              {error}
            </div>
            <button
              onClick={() => void loadDetails()}
              className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
            >
              Try again
            </button>
          </div>
        ) : details && !user ? (
          // Unauthenticated user - show sign-in options
          <div className="space-y-4">
            <div className="space-y-1">
              <p className="text-sm text-gray-700">
                You&apos;ve been invited to join this{' '}
                {details.scope === 'tenant' ? 'organization' : 'entity'} with
                the role <span className="font-medium">{details.role}</span>.
              </p>
              <p className="text-sm text-gray-700">
                Invited by: {details.inviterEmail}
              </p>
              <p className="text-xs text-gray-500">
                Expires: {new Date(details.expiresAt).toLocaleString()}
              </p>
            </div>

            {details.isExpired ? (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800 text-sm">
                This invitation has expired. Please contact the inviter for a
                new link.
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 text-sm">
                  Please sign in to accept this invitation.
                </div>

                {/* Google Sign-In Option */}
                <GoogleSignInButton
                  redirectTo="/auth/callback"
                  inviteToken={token}
                  disabled={accepting}
                >
                  Sign in with Google to accept
                </GoogleSignInButton>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or</span>
                  </div>
                </div>

                {/* Email/Password Sign-In Option */}
                <button
                  onClick={() => {
                    const loginUrl = new URL('/login', window.location.origin)
                    loginUrl.searchParams.set(
                      'redirectTo',
                      `/auth/callback?invite_token=${token}`
                    )
                    router.replace(loginUrl.toString())
                  }}
                  className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                >
                  Sign in with email/password
                </button>

                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Don&apos;t have an account?{' '}
                    <button
                      onClick={() => {
                        const signupUrl = new URL(
                          '/signup',
                          window.location.origin
                        )
                        signupUrl.searchParams.set(
                          'redirectTo',
                          `/auth/callback?invite_token=${token}`
                        )
                        router.replace(signupUrl.toString())
                      }}
                      className="text-blue-600 hover:text-blue-500 font-medium"
                    >
                      Sign up
                    </button>
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : details ? (
          // Authenticated user - show accept button
          <div className="space-y-4">
            <div className="space-y-1">
              <p className="text-sm text-gray-700">
                You&apos;ve been invited to join this{' '}
                {details.scope === 'tenant' ? 'organization' : 'entity'} with
                the role <span className="font-medium">{details.role}</span>.
              </p>
              <p className="text-sm text-gray-700">
                Invited by: {details.inviterEmail}
              </p>
              <p className="text-xs text-gray-500">
                Expires: {new Date(details.expiresAt).toLocaleString()}
              </p>
            </div>

            {details.isExpired ? (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800 text-sm">
                This invitation has expired. Please contact the inviter for a
                new link.
              </div>
            ) : (
              <button
                onClick={() => void acceptInvite()}
                disabled={accepting}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                {accepting ? 'Accepting…' : 'Accept Invitation'}
              </button>
            )}

            {successMsg && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-800 text-sm">
                {successMsg}
              </div>
            )}

            <button
              onClick={() => router.replace('/')}
              className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
            >
              Go to Dashboard
            </button>
          </div>
        ) : null}
      </div>
    </div>
  )
}
