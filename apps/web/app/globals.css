@tailwind base;
@tailwind components;
@tailwind utilities;

/* Zen UI Global Theme Variables */
:root {
  /* Primary Colors - Following Zen UI Style Guide */
  --zen-bg: #FBFAF5; /* soft cream background */
  --zen-surface: #FFFFFC; /* pure white for cards */
  --zen-primary-text: #1a1a1a; /* near black */
  --zen-secondary-text: #6b7280; /* warm gray */
  --zen-subtle-text: #9ca3af; /* light gray */

  /* Borders and Surfaces */
  --zen-border: #f3f4f6; /* subtle border */
  --zen-border-hover: #e5e7eb; /* slightly darker on hover */

  /* Accent Colors (use sparingly) */
  --zen-success: #10b981; /* soft green */
  --zen-warning: #f59e0b; /* warm amber */
  --zen-error: #ef4444; /* soft red */
  --zen-primary-action: #3b82f6; /* calm blue */

  /* Shadows and Effects */
  --zen-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05); /* very subtle */
  --zen-shadow-hover: 0 1px 3px 0 rgba(0, 0, 0, 0.1); /* slightly more on hover */

  /* Legacy support */
  --background: var(--zen-bg);
  --foreground: var(--zen-primary-text);
}

/* Remove dark mode override - Zen UI is light-only */
body {
  color: var(--zen-primary-text);
  background: var(--zen-bg);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  line-height: 1.5;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}