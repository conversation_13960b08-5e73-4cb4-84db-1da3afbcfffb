'use client'

import { useEffect, useMemo, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signInWithEmail, signInWithPassword } from '@belbooks/dal'
import { useAuth } from '@/contexts/AuthContext'
import { useCSRF } from '@/hooks/useCSRF'
import { createSecureBrowserClient } from '@/lib/session-security'
import { GoogleSignInButton } from '@/components/GoogleSignInButton'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [usePassword, setUsePassword] = useState(false)
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const { createCSRFInput } = useCSRF()
  const supabase = useMemo(() => createSecureBrowserClient(), [])

  // Compute redirect target once per render cycle
  const redirectTarget = useMemo(
    () => searchParams.get('redirectTo') || '/',
    [searchParams]
  )

  // Extract invitation token if present in redirect URL
  const inviteToken = useMemo(() => {
    const redirectTo = searchParams.get('redirectTo')
    if (redirectTo) {
      try {
        const url = new URL(redirectTo, window.location.origin)
        return url.searchParams.get('invite_token')
      } catch {
        return null
      }
    }
    return null
  }, [searchParams])

  // If already logged in, redirect after first render using an effect
  useEffect(() => {
    if (user) {
      router.replace(redirectTarget)
    }
  }, [user, router, redirectTarget])

  const handleSubmitAsync = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setMessage('')

    try {
      if (usePassword) {
        // Email + password sign in
        await signInWithPassword(email, password)

        // Ensure SSR auth cookies are synced for middleware before navigating
        try {
          const {
            data: { session },
          } = await supabase.auth.getSession()
          if (session) {
            await fetch('/auth/callback', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ event: 'SIGNED_IN', session }),
            })
          }
        } catch (syncErr) {
          console.warn('Auth cookie sync failed (continuing):', syncErr)
        }

        const redirectTo = searchParams.get('redirectTo') || '/'
        router.replace(redirectTo)
        return
      }

      // Magic link sign in
      const redirectTo = searchParams.get('redirectTo') || '/'
      const redirectUrl = new URL('/auth/callback', window.location.origin)
      redirectUrl.searchParams.set('redirectTo', redirectTo)
      await signInWithEmail(email, redirectUrl.toString())
      setMessage('Check your email for the login link!')
    } catch (err) {
      // Sanitize error messages for security - never expose internal details
      const message = err instanceof Error ? err.message : 'An error occurred'

      // Only show safe, user-friendly messages
      if (
        message.includes('Invalid email') ||
        message.includes('Email not found')
      ) {
        setError('Please check your email address and try again.')
      } else if (
        usePassword &&
        (message.toLowerCase().includes('invalid login') ||
          message.toLowerCase().includes('invalid credentials') ||
          message.toLowerCase().includes('invalid email or password'))
      ) {
        setError('Invalid email or password.')
      } else if (
        message.includes('rate limit') ||
        message.includes('Too many')
      ) {
        setError('Too many attempts. Please wait a moment before trying again.')
      } else {
        // Generic error for anything else to prevent information disclosure
        setError(
          usePassword
            ? 'Unable to sign in. Please try again.'
            : 'Unable to send login link. Please try again.'
        )
      }
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent): void => {
    void handleSubmitAsync(e)
  }

  // While redirecting, show a lightweight screen to avoid blank page and avoid state updates during render
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <p className="text-gray-700">Redirecting…</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Sign in to BelBooks
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {usePassword
              ? 'Enter your email and password to sign in'
              : "Enter your email and we'll send you a secure login link"}
          </p>
        </div>

        {/* Google Sign-In Option */}
        <div className="mt-8">
          <GoogleSignInButton
            redirectTo={redirectTarget}
            inviteToken={inviteToken || undefined}
            disabled={loading}
          />
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-gray-50 text-gray-500">
              Or continue with email
            </span>
          </div>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit}>
          {createCSRFInput()}
          <div>
            <label htmlFor="email" className="sr-only">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              placeholder="Enter your email address"
            />
          </div>

          {usePassword && (
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(v => !v)}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                  className="absolute inset-y-0 right-0 px-3 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>
          )}

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {message && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="text-sm text-green-700">{message}</div>
            </div>
          )}

          <div className="flex flex-col gap-3">
            <button
              type="submit"
              disabled={loading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading
                ? usePassword
                  ? 'Signing in…'
                  : 'Sending…'
                : usePassword
                  ? 'Sign in'
                  : 'Send login link'}
            </button>
            <button
              type="button"
              onClick={() => setUsePassword(v => !v)}
              className="w-full text-sm text-blue-600 hover:text-blue-500"
            >
              {usePassword ? 'Use magic link instead' : 'Use password instead'}
            </button>
          </div>

          <div className="text-center space-y-2">
            <a
              href="/forgot-password"
              className="block text-sm text-blue-600 hover:text-blue-500"
            >
              Forgot your password?
            </a>
            <p className="text-sm text-gray-600">
              Don&apos;t have an account?{' '}
              <a
                href="/signup"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Sign up
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
