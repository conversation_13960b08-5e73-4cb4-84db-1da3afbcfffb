import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { getPendingInvite, cancelInvite } from '@ledgerly/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import { createSecureServerClient } from '@/lib/supabase-server'

// GET /api/invites/[token] - View invitation details
export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
): Promise<Response> {
  try {
    const { token } = params

    if (!token) {
      return createJsonResponse({ error: 'Missing invitation token' }, 400)
    }

    // Get invitation details (this doesn't require authentication)
    try {
      const rawInvite: unknown = await getPendingInvite(token)
      if (!rawInvite) {
        return createJsonResponse(
          { error: 'Invalid or expired invitation' },
          404
        )
      }

      // Return safe invitation details (without sensitive info)
      const inv = rawInvite as Record<string, unknown>
      const scope = String(inv.scope ?? '')
      const role = String(inv.role ?? '')
      const inviterEmail = String(inv.inviter_user_id ?? '')
      const expiresAt = String(inv.expires_at ?? '')
      const isExpired = expiresAt ? new Date(expiresAt) < new Date() : true

      return createJsonResponse({
        scope,
        role,
        inviterEmail,
        expiresAt,
        isExpired,
      })
    } catch (error) {
      console.error('Error fetching invitation:', error)
      return createJsonResponse(
        { error: 'Failed to fetch invitation details' },
        400
      )
    }
  } catch (error) {
    console.error('Get invitation API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}

// DELETE /api/invites/[token] - Cancel invitation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { token: string } }
): Promise<Response> {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      void SecurityEvents.suspiciousRequest(
        'unknown',
        'CSRF validation failed',
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown',
        { endpoint: `/api/invites/${params.token}` }
      )

      return createJsonResponse({ error: 'Invalid CSRF token' }, 403)
    }

    // Get authenticated user
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    const { token } = params

    if (!token) {
      return createJsonResponse({ error: 'Missing invitation token' }, 400)
    }

    // Cancel invitation using DAL
    try {
      await cancelInvite(token)

      // Log successful invitation cancellation
      void SecurityEvents.adminAction(
        user.id,
        'cancel_invitation',
        token,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse({
        success: true,
        message: 'Invitation cancelled successfully',
      })
    } catch (error) {
      console.error('Error cancelling invitation:', error)

      // Log failed invitation cancellation
      void SecurityEvents.adminAction(
        user.id,
        'cancel_invitation_failed',
        token,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to cancel invitation',
        },
        400
      )
    }
  } catch (error) {
    console.error('Cancel invitation API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
