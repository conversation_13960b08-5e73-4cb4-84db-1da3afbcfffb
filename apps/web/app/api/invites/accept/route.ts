import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@belbooks/types'
import { acceptInvite } from '@belbooks/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { logSecurityEvent, SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type { AcceptInviteRequest, AcceptInviteResponse, ErrorResponse } from '@/lib/api-types'

function isAcceptInviteRequest(body: unknown): body is AcceptInviteRequest {
  if (typeof body !== 'object' || body === null) return false
  const obj = body as Record<string, unknown>
  return typeof obj.token === 'string' && obj.token.length > 0
}

export async function POST(request: NextRequest): Promise<Response> {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      void logSecurityEvent({
        type: SecurityEvents.SUSPICIOUS_ACTIVITY,
        severity: 'medium',
        details: {
          endpoint: '/api/invites/accept',
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
      
      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid CSRF token' },
        403
      )
    }

    // Get authenticated user
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>(
        { error: 'Unauthorized' },
        401
      )
    }

    // Parse and validate request body
    const body: unknown = await request.json()
    
    if (!isAcceptInviteRequest(body)) {
      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid request body. Required field: token' },
        400
      )
    }

    const { token } = body

    // Accept invitation using DAL
    try {
      await acceptInvite(token)
      
      // Log successful invitation acceptance
      void SecurityEvents.adminAction(
        user.id,
        'accept_invitation',
        token,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse<AcceptInviteResponse>({
        success: true,
        message: 'Invitation accepted successfully'
      })
      
    } catch (error) {
      console.error('Error accepting invitation:', error)
      
      // Log failed invitation acceptance
      void SecurityEvents.adminAction(
        user.id,
        'accept_invitation_failed',
        token,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )
      
      return createJsonResponse<ErrorResponse>(
        { error: error instanceof Error ? error.message : 'Failed to accept invitation' },
        400
      )
    }
    
  } catch (error) {
    console.error('Accept invitation API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}
