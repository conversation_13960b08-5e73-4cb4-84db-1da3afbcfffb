/**
 * Keep-Alive API Route
 * Extends session activity to prevent timeout
 */

import { NextRequest } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { sessionManager } from '@/lib/session-security'
import { SecurityEvents } from '@/lib/security-monitoring'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!user || !session) {
      return createJsonResponse({ error: 'Not authenticated' }, 401)
    }

    // Update session activity
    const activityUpdated = sessionManager.updateActivity(
      session.access_token,
      request
    )

    if (!activityUpdated) {
      // Session security violation
      void SecurityEvents.suspiciousRequest(
        user.id,
        'Keep-alive failed - session security violation',
        'unknown',
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse({ error: 'Session security violation' }, 403)
    }

    // Get updated timeout info
    const timeoutInfo = sessionManager.getTimeoutInfo(session.access_token)

    return createJsonResponse({
      success: true,
      timeoutInfo,
      message: 'Session activity updated',
    })
  } catch (error) {
    console.error('Keep-alive error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
