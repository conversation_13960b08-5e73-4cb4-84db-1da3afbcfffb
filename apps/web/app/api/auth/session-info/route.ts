/**
 * Session Info API Route
 * Provides session timeout and security information
 */

import { NextRequest } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { sessionManager } from '@/lib/session-security'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'

export async function GET(_request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!user || !session) {
      return createJsonResponse({ error: 'Not authenticated' }, 401)
    }

    // Get session metadata
    const sessionMetadata = sessionManager.getSession(session.access_token)
    const timeoutInfo = sessionManager.getTimeoutInfo(session.access_token)
    const userSessions = sessionManager.getUserSessions(user.id)

    return createJsonResponse({
      user: {
        id: user.id,
        email: user.email,
      },
      session: {
        id: session.access_token.substring(0, 8) + '...',
        createdAt: sessionMetadata?.createdAt,
        lastActivity: sessionMetadata?.lastActivity,
        expiresAt: sessionMetadata?.expiresAt,
        deviceFingerprint:
          sessionMetadata?.deviceFingerprint?.substring(0, 8) + '...',
      },
      timeoutInfo,
      security: {
        activeSessions: userSessions.length,
        sessionStats: sessionManager.getSessionStats(),
      },
    })
  } catch (error) {
    console.error('Session info error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
