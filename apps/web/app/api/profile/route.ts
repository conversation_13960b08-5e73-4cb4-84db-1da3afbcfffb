import { NextRequest } from 'next/server'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type { ErrorResponse } from '@/lib/api-types'

/**
 * Update user profile
 * This route requires CSRF protection as it performs state-changing operations
 */
export function PUT(_request: NextRequest): Response {
  try {
    // Profile updates temporarily disabled - schema doesn't include profiles table
    return createJsonResponse<ErrorResponse>({ error: 'Profile updates not available' }, 501)
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>({ error: 'Internal server error' }, 500)
  }
}

/**
 * Get user profile
 * This is a safe method and doesn't require CSRF protection
 */
export function GET(): Response {
  try {
    // Profile fetching temporarily disabled - schema doesn't include profiles table
    return createJsonResponse<ErrorResponse>({ error: 'Profile fetching not available' }, 501)
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>({ error: 'Internal server error' }, 500)
  }
}