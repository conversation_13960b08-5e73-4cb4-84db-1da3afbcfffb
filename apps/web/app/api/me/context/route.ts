import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type {
  UserContextResponse,
  ErrorResponse,
} from '@/lib/api-types'
import { createSecureServerClient } from '@/lib/supabase-server'

/**
 * Get user context including tenant org_type, entities, and roles
 * This endpoint provides all the context needed for conditional UX
 */
export async function GET(_request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Get user's tenants with org_type
    const { data: tenants, error: tenantsError } = await supabase
      .from('v_user_tenants')
      .select('*')
      .order('created_at', { ascending: false })

    if (tenantsError) {
      console.error('Error fetching user tenants:', tenantsError)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to fetch user context' },
        500
      )
    }

    // Get user's entities with roles
    const { data: entities, error: entitiesError } = await supabase
      .from('v_user_entities')
      .select('*')
      .order('entity_name', { ascending: true })

    if (entitiesError) {
      console.error('Error fetching user entities:', entitiesError)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to fetch user context' },
        500
      )
    }

    // For now, we'll use the first tenant as the primary tenant
    // In the future, we might need to track the user's "current" tenant selection
    const primaryTenant = tenants?.[0]

    // Build roles per entity map
    const rolesPerEntity: Record<number, string> = {}
    entities?.forEach(entity => {
      if (entity.entity_id && entity.role) {
        rolesPerEntity[entity.entity_id] = entity.role
      }
    })

    const response: UserContextResponse = {
      tenant: primaryTenant ? {
        id: primaryTenant.tenant_id!,
        name: primaryTenant.tenant_name!,
        org_type: primaryTenant.org_type as 'sme' | 'firm',
        role: primaryTenant.role!,
      } : null,
      entities: entities?.map(entity => ({
        id: entity.entity_id!,
        name: entity.entity_name!,
        is_default: false, // Will be updated when we have the is_default column in the view
        role: entity.role!,
      })) || [],
      rolesPerEntity,
    }

    return createJsonResponse<UserContextResponse>(response)
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}