import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { grantTenantRole, grantEntityRole } from '@ledgerly/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { logSecurityEvent } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type { RoleResponse, ErrorResponse } from '@/lib/api-types'
import { isRoleRequest } from '@/lib/api-types'
import { createSecureServerClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest): Promise<Response> {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      // Log via generic logger for test observability
      void logSecurityEvent({
        type: 'SUSPICIOUS_REQUEST',
        severity: 'medium',
        details: {
          endpoint: '/api/roles',
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })

      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid CSRF token' },
        403
      )
    }

    // Get authenticated user
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Parse and validate request body
    const body: unknown = await request.json()

    if (!isRoleRequest(body)) {
      return createJsonResponse<ErrorResponse>(
        {
          error:
            'Invalid request body. Required fields: scope, scopeId, targetUserId, role',
        },
        400
      )
    }

    const { scope, scopeId, targetUserId, role } = body

    // Validate role based on scope
    const validTenantRoles = [
      'tenant_owner',
      'tenant_admin',
      'tenant_billing',
      'tenant_member',
    ]
    const validEntityRoles = [
      'owner',
      'admin',
      'accountant',
      'bookkeeper',
      'viewer',
    ]

    if (scope === 'tenant' && !validTenantRoles.includes(role)) {
      return createJsonResponse<ErrorResponse>(
        {
          error: `Invalid tenant role. Must be one of: ${validTenantRoles.join(', ')}`,
        },
        400
      )
    }

    if (scope === 'entity' && !validEntityRoles.includes(role)) {
      return createJsonResponse<ErrorResponse>(
        {
          error: `Invalid entity role. Must be one of: ${validEntityRoles.join(', ')}`,
        },
        400
      )
    }

    // Grant role using appropriate DAL function
    try {
      const scopeIdNumber =
        typeof scopeId === 'string' ? parseInt(scopeId, 10) : scopeId

      if (scope === 'tenant') {
        await grantTenantRole(scopeIdNumber, targetUserId, role)
      } else {
        await grantEntityRole(scopeIdNumber, targetUserId, role)
      }

      // Log role assignment via generic logger for test observability
      void logSecurityEvent({
        type: 'ADMIN_ACTION',
        userId: user.id,
        severity: 'low',
        details: {
          action: 'grant_role',
          scope,
          scopeId: String(scopeIdNumber),
          targetUserId,
          role,
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })

      return createJsonResponse<RoleResponse>({
        success: true,
        message: `${scope === 'tenant' ? 'Tenant' : 'Entity'} role granted successfully`,
      })
    } catch (error) {
      console.error('Error granting role:', error)

      // Log failed role assignment
      void logSecurityEvent({
        type: 'PERMISSION_DENIED',
        userId: user.id,
        severity: 'low',
        details: {
          error: error instanceof Error ? error.message : String(error),
        },
      })

      return createJsonResponse<ErrorResponse>(
        {
          error:
            error instanceof Error ? error.message : 'Failed to grant role',
        },
        400
      )
    }
  } catch (error) {
    console.error('Grant role API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}
