import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import {
  rpcVatExportCsv,
  checkVATEnabled,
  createVATDisabledError,
} from '@ledgerly/dal'
import type { Database, VATError } from '@ledgerly/types'
import { createJsonResponse } from '@/lib/http/createJsonResponse'

function isVatError(err: unknown): err is VATError {
  return err instanceof Error && 'code' in err
}

export async function GET(
  request: NextRequest,
  { params }: { params: { entityId: string } }
): Promise<Response> {
  try {
    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse('Unauthorized', 401)
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url)
    const start = searchParams.get('start')
    const end = searchParams.get('end')

    if (!start || !end) {
      return createJsonResponse('Start and end date parameters are required', 400)
    }

    const entityId = parseInt(params.entityId, 10)

    // Check VAT feature flag using typed DAL function
    const isVATEnabled = await checkVATEnabled(supabase, entityId)
    if (!isVATEnabled) {
      const vatError = createVATDisabledError()
      return new Response(`Error: ${vatError.message}`, { status: 403, headers: { 'Content-Type': 'text/plain' } })
    }

    // Call the typed VAT CSV export function
    const csvData = await rpcVatExportCsv(supabase, {
      p_entity: entityId,
      p_start: start,
      p_end: end,
    })

    // Return CSV with appropriate headers
    const filename = `vat-export-${params.entityId}-${start}-to-${end}.csv`
    return new Response(csvData, { status: 200, headers: { 'Content-Type': 'text/csv; charset=utf-8', 'Content-Disposition': `attachment; filename="${filename}"` } })
  } catch (error: unknown) {
    console.error('VAT CSV export API error:', error)

    // For CSV endpoint, return error as text since we're expecting CSV format
    if (isVatError(error)) {
      const vatError = error
      let statusCode = 500

      if (vatError.code === 'INVALID_DATE_FORMAT') {
        statusCode = 400
      } else if (vatError.code === 'VAT_DISABLED') {
        statusCode = 403
      }

      return new Response(`Error: ${vatError.message}`, { status: statusCode, headers: { 'Content-Type': 'text/plain' } })
    }

    const message =
      error instanceof Error ? error.message : 'Internal server error'
    return new Response(`Error: ${message}`, { status: 500, headers: { 'Content-Type': 'text/plain' } })
  }
}
