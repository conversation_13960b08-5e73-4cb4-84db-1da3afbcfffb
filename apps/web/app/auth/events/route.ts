import { NextResponse } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'

export async function POST(request: Request) {
  try {
    const supabase = createSecureServerClient()
    const requestData = (await request.json()) as {
      event: string
      session: unknown
    }
    const { event, session } = requestData

    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      // Set/update the session cookies for SSR (middleware)
      await supabase.auth.setSession(session as never)
    }

    if (event === 'SIGNED_OUT') {
      await supabase.auth.signOut()
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.json(
      { success: false, error: 'callback_failed' },
      { status: 400 }
    )
  }
}

export function OPTIONS() {
  return NextResponse.json({})
}
