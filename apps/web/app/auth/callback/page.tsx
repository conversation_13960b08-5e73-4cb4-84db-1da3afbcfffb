'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClientSupabaseClient } from '@/lib/auth'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { listUserTenants } from '@ledgerly/dal'

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase: SupabaseClient<Database> = createClientSupabaseClient()
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Auth callback error:', error)
          router.replace('/login?error=auth_callback_failed')
          return
        }

        if (data.session) {
          // Successfully authenticated - determine routing based on context
          try {
            const tenants = await listUserTenants()

            // Check for invitation context in URL parameters
            const inviteToken = searchParams.get('invite_token')
            const redirectTo = searchParams.get('redirectTo')

            if (tenants.length === 0) {
              // New user with no tenants
              if (inviteToken) {
                // Has invitation context - redirect to invitation acceptance
                router.replace(`/invites/${inviteToken}`)
              } else {
                // No invitation context - redirect to company creation onboarding
                router.replace('/onboarding')
              }
            } else {
              // Existing user with tenants
              if (inviteToken) {
                // Existing user accepting new invitation
                router.replace(`/invites/${inviteToken}`)
              } else {
                // Normal login - redirect to dashboard or specified page
                router.replace(redirectTo || '/')
              }
            }
          } catch (tenantError) {
            console.error('Error checking tenants:', tenantError)
            // If tenant check fails, check for invitation context
            const inviteToken = searchParams.get('invite_token')
            if (inviteToken) {
              router.replace(`/invites/${inviteToken}`)
            } else {
              router.replace('/onboarding')
            }
          }
        } else {
          // No session found
          router.replace('/login?error=no_session')
        }
      } catch (err) {
        console.error('Auth callback error:', err)
        router.replace('/login?error=callback_error')
      }
    }

    void handleAuthCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Completing sign in...</p>
      </div>
    </div>
  )
}
