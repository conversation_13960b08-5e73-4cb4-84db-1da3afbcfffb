'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClientSupabaseClient } from '@/lib/auth'
import { validatePassword } from '@/lib/password-policy'
import {
  PasswordStrength,
  usePasswordValidation,
} from '@/components/PasswordStrength'

export default function ResetPasswordPage() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [isValidSession, setIsValidSession] = useState(false)

  const router = useRouter()
  const { isValid: isPasswordValid } = usePasswordValidation(password)

  useEffect(() => {
    // Check if this is a valid password reset session
    const checkSession = async () => {
      const supabase = createClientSupabaseClient()
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession()
      if (session && !error) {
        setIsValidSession(true)
      } else {
        setError('Invalid or expired reset link. Please request a new one.')
      }
    }

    void checkSession()
  }, [])

  const handleSubmitAsync = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()

    // eslint-disable-next-line security/detect-possible-timing-attacks
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    setLoading(true)
    setError('')

    try {
      // Comprehensive password validation
      const passwordValidation = validatePassword(password)

      if (!passwordValidation.isValid) {
        setError(
          passwordValidation.failedRequirements[0]?.label ||
            'Password does not meet security requirements'
        )
        setLoading(false)
        return
      }

      const supabase = createClientSupabaseClient()
      const { error } = await supabase.auth.updateUser({
        password: password,
      })

      if (error) throw error

      // Success - redirect to dashboard
      router.replace('/?message=password_updated')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent): void => {
    void handleSubmitAsync(e)
  }

  if (!isValidSession) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              Invalid Reset Link
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              This password reset link is invalid or has expired.
            </p>
            <button
              onClick={() => router.push('/forgot-password')}
              className="mt-4 text-blue-600 hover:text-blue-500"
            >
              Request a new reset link
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Set new password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your new password below
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="password" className="sr-only">
                New password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={password}
                onChange={e => setPassword(e.target.value)}
                className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                placeholder="New password"
              />

              {/* Password strength indicator */}
              {password && (
                <div className="mt-3">
                  <PasswordStrength password={password} />
                </div>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="sr-only">
                Confirm new password
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                className="relative block w-full appearance-none rounded border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                placeholder="Confirm new password"
              />
            </div>
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={
                loading ||
                !isPasswordValid ||
                password !== confirmPassword ||
                !password
              }
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update password'}
            </button>

            {/* Additional validation feedback */}
            {password && confirmPassword && password !== confirmPassword && (
              <p className="mt-2 text-sm text-red-600">
                Passwords do not match
              </p>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}
