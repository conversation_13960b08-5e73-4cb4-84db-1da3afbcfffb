import React from 'react'
import { render } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/contexts/AuthContext'
import { OrgEntityProvider } from '@/contexts/OrgEntityContext'
import { CSPProvider } from '@/contexts/CSPContext'

// Minimal router context for components using next/navigation hooks indirectly
const RouterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

export function renderWithProviders(ui: React.ReactElement, { queryClient } = { queryClient: new QueryClient() }) {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <CSPProvider nonce={undefined}>
      <AuthProvider>
        <OrgEntityProvider>
          <QueryClientProvider client={queryClient}>
            <RouterProvider>{children}</RouterProvider>
          </QueryClientProvider>
        </OrgEntityProvider>
      </AuthProvider>
    </CSPProvider>
  )
  return { user: userEvent.setup(), ...render(ui, { wrapper: Wrapper }) }
}

export * from '@testing-library/react'
export { userEvent }

