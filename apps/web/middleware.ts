import { createServer<PERSON>lient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from '@ledgerly/types'
import {
  checkRateLimit,
  getClientIdentifier,
  getClientIP,
  createRateLimitHeaders,
  logRateLimitViolation,
  createRateLimitResponse,
} from '@/lib/rate-limit'
import { validateCSRFMiddleware, generateCSRFToken } from '@/lib/csrf'
import { logSecurityEvent } from '@/lib/security-monitoring'
import { addCSPNonce } from '@/lib/csp-nonce'
import { validateSessionSecurity, sessionManager } from '@/lib/session-security'

export async function middleware(request: NextRequest) {
  // Skip all Next.js internal assets defensively (even though matcher excludes them)
  if (request.nextUrl.pathname.startsWith('/_next')) {
    return NextResponse.next()
  }

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // 1. Rate Limiting Check (API routes only)
  // Next.js RSC triggers many GET requests to page routes like '/' with ?_rsc=...
  // To avoid accidental blocking of navigation, we only rate-limit /api/* routes.
  const isApiRoute = request.nextUrl.pathname.startsWith('/api/')
  if (isApiRoute) {
    getClientIdentifier(request)
    const rateLimit = checkRateLimit(request)
    const rateLimitHeaders = createRateLimitHeaders(rateLimit)
    Object.entries(rateLimitHeaders).forEach(([key, value]) => {
      response.headers.set(key, value)
    })
    if (!rateLimit.allowed) {
      logRateLimitViolation(request, rateLimit)
      return createRateLimitResponse(rateLimit)
    }
  }

  // 2. CSRF Protection Check (only for API routes that modify state)
  if (isApiRoute) {
    const csrfResult = await validateCSRFMiddleware(request)
    if (!csrfResult) {
      void logSecurityEvent({
        type: 'CSRF_VIOLATION',
        severity: 'high',
        details: {
          path: request.nextUrl.pathname,
          method: request.method,
          reason: 'CSRF validation failed',
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })

      return new NextResponse(JSON.stringify({ error: 'Invalid CSRF token' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      })
    }
  }

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: Record<string, unknown>) {
          // Avoid recreating the response object unnecessarily; reuse and set cookie.
          response.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: Record<string, unknown>) {
          response.cookies.set({ name, value: '', ...options })
        },
      },
    }
  )

  // Get user and session for security validation
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()
  const {
    data: { session },
    error: sessionError,
  } = await supabase.auth.getSession()

  // 3. Session Security Validation
  if (user && session && !userError && !sessionError) {
    const sessionValidation = await validateSessionSecurity()

    if (!sessionValidation) {
      // Session security violation - force logout
      void logSecurityEvent({
        type: 'SUSPICIOUS_REQUEST',
        userId: user.id,
        severity: 'high',
        details: {
          reason: 'Session security validation failed',
          path: request.nextUrl.pathname,
          ipAddress: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })

      // Clear session and redirect to login
      response.cookies.delete('sb-access-token')
      response.cookies.delete('sb-refresh-token')

      return NextResponse.redirect(
        new URL('/login?session_expired=true', request.url)
      )
    }

    // Initialize or update session in session manager
    if (session) {
      try {
        // Update session activity
        sessionManager.updateActivity(session.access_token, request)
      } catch (error) {
        console.warn('Session manager error:', error)
        // Continue - don't block user for session manager issues
      }
    }
  }

  // Define route patterns
  const isAuthPage =
    ['/login', '/signup', '/forgot-password', '/reset-password'].includes(
      request.nextUrl.pathname
    ) || request.nextUrl.pathname.startsWith('/auth/')
  const isProtectedPage = ['/ledger', '/vat', '/inbox', '/onboarding'].some(
    path => request.nextUrl.pathname.startsWith(path)
  )

  // Redirect authenticated users away from auth pages
  if (user && isAuthPage && request.nextUrl.pathname !== '/auth/callback') {
    // If a safe redirectTo is present, honor it to avoid landing on a blank page
    const redirectParam = request.nextUrl.searchParams.get('redirectTo')
    const isSafe =
      redirectParam &&
      redirectParam.startsWith('/') &&
      !redirectParam.startsWith('//')
    const target = isSafe ? redirectParam! : '/'
    return NextResponse.redirect(new URL(target, request.url))
  }

  // Redirect unauthenticated users to login
  if (!user && isProtectedPage) {
    const redirectUrl = new URL('/login', request.url)
    if (request.nextUrl.pathname !== '/login') {
      redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)
    }
    return NextResponse.redirect(redirectUrl)
  }

  // For authenticated users accessing root, check if they need onboarding
  if (user && request.nextUrl.pathname === '/' && !isAuthPage) {
    try {
      const { data: tenants, error } = await supabase
        .from('v_user_tenants')
        .select('tenant_id')
        .limit(1)

      if (!error && (!tenants || tenants.length === 0)) {
        return NextResponse.redirect(new URL('/onboarding', request.url))
      }
    } catch (error) {
      console.error('Error checking tenant membership:', error)
      // On error, allow through - will be handled by client-side redirect
    }
  }

  // 4. Add CSP nonce for production security
  if (process.env.NODE_ENV === 'production') {
    addCSPNonce(request, response)
  }

  // 5. Ensure CSRF token cookie exists and expose it via response header for meta tag
  try {
    let csrfToken = request.cookies.get('csrf-token')?.value || ''
    if (!csrfToken) {
      csrfToken = await generateCSRFToken()
      response.cookies.set('csrf-token', csrfToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 60 * 60 * 24, // 24 hours
      })
    }
    response.headers.set('X-CSRF-Token', csrfToken)
  } catch {
    // best-effort; do not block the request if generation fails
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public images and assets
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
