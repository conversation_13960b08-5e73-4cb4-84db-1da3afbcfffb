/**
 * Session Timeout Warning Dialog
 * Shows warning before automatic logout due to session timeout
 */

'use client'

import { useEffect, useState } from 'react'
import { formatTimeRemaining } from '@/hooks/useSessionSecurity'
import type { SessionTimeoutInfo } from '@/lib/session-security'

interface SessionTimeoutDialogProps {
  show: boolean
  timeoutInfo: SessionTimeoutInfo | null
  onExtendSession: () => void
  onLogoutNow: () => void
  onClose?: () => void
}

export function SessionTimeoutDialog({
  show,
  timeoutInfo,
  onExtendSession,
  onLogoutNow,
  onClose
}: SessionTimeoutDialogProps) {
  const [remainingTime, setRemainingTime] = useState(0)

  useEffect(() => {
    if (!show || !timeoutInfo) return

    const updateTimer = () => {
      const time = timeoutInfo.timeUntilTimeout
      setRemainingTime(time)
      
      if (time <= 0) {
        // Time's up - the parent will handle logout
        return
      }
    }

    // Update immediately
    updateTimer()

    // Update every second
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [show, timeoutInfo])

  if (!show || !timeoutInfo) return null

  const timeoutReason = 'inactivity'

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Background overlay */}
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Center the modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              {/* Warning icon */}
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg className="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                </svg>
              </div>

              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  Session Timeout Warning
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    Your session will expire in <strong className="text-red-600">{formatTimeRemaining(remainingTime)}</strong> due to {timeoutReason}.
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    You will be automatically logged out to protect your account security.
                  </p>
                </div>

                {/* Countdown display */}
                <div className="mt-4 bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Time remaining:</span>
                    <span className={`text-lg font-bold ${remainingTime <= 60 ? 'text-red-600' : remainingTime <= 180 ? 'text-yellow-600' : 'text-gray-900'}`}>
                      {formatTimeRemaining(remainingTime)}
                    </span>
                  </div>
                  
                  {/* Progress bar */}
                  <div className="mt-2">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          remainingTime <= 60 ? 'bg-red-500' : 
                          remainingTime <= 180 ? 'bg-yellow-500' : 
                          'bg-green-500'
                        }`}
                        style={{ 
                          width: `${Math.max(0, Math.min(100, (remainingTime / (5 * 60)) * 100))}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Session info */}
                <div className="mt-3 text-xs text-gray-400">
                  <div>Time until timeout: {formatTimeRemaining(timeoutInfo.timeUntilTimeout)}</div>
                  <div>Max idle time: {formatTimeRemaining(timeoutInfo.maxIdleTime)}</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            {/* Extend Session button */}
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={() => {
                onExtendSession()
                onClose?.()
              }}
            >
              Continue Session
            </button>

            {/* Logout Now button */}
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={() => {
                onLogoutNow()
                onClose?.()
              }}
            >
              Logout Now
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Compact session timeout indicator for the navigation bar
 */
export function SessionTimeoutIndicator({ 
  timeRemaining, 
  isWarning = false,
  onClick 
}: { 
  timeRemaining: number
  isWarning?: boolean
  onClick?: () => void 
}) {
  if (timeRemaining <= 0) return null

  const isUrgent = timeRemaining <= 120 // 2 minutes
  const isClose = timeRemaining <= 300   // 5 minutes

  return (
    <div 
      className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
        isUrgent ? 'bg-red-100 text-red-800 hover:bg-red-200' :
        isClose ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
        'bg-gray-100 text-gray-600 hover:bg-gray-200'
      }`}
      onClick={onClick}
      title={isWarning ? 'Click to extend session' : 'Session time remaining'}
    >
      {/* Clock icon */}
      <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6l4 2m6-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      
      <span className="font-medium">
        {formatTimeRemaining(timeRemaining)}
      </span>

      {isWarning && (
        <svg className="h-4 w-4 animate-pulse" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
      )}
    </div>
  )
}