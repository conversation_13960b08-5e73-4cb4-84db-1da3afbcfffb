'use client'

import { useState } from 'react'
import { useCSRF } from '@/hooks/useCSRF'
import { useOrgEntity } from '@/contexts/OrgEntityContext'

interface InvitationFormProps {
  onInviteSent?: (email: string, role: string) => void
  onClose?: () => void
}

export function InvitationForm({ onInviteSent, onClose }: InvitationFormProps) {
  const { csrfToken, addCSRFHeaders } = useCSRF()
  const { selection } = useOrgEntity()
  
  const [formData, setFormData] = useState({
    email: '',
    role: '',
    scope: 'tenant' as 'tenant' | 'entity'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Role options based on scope
  const tenantRoles = [
    { value: 'tenant_member', label: 'Member', description: 'Basic access to tenant resources' },
    { value: 'tenant_billing', label: 'Billing', description: 'Manage billing and subscriptions' },
    { value: 'tenant_admin', label: 'Admin', description: 'Full tenant management access' },
    { value: 'tenant_owner', label: 'Owner', description: 'Complete tenant control' }
  ]

  const entityRoles = [
    { value: 'viewer', label: 'Viewer', description: 'Read-only access to entity data' },
    { value: 'bookkeeper', label: 'Bookkeeper', description: 'Enter transactions and basic reports' },
    { value: 'accountant', label: 'Accountant', description: 'Full accounting features and reports' },
    { value: 'admin', label: 'Admin', description: 'Manage entity settings and users' },
    { value: 'owner', label: 'Owner', description: 'Complete entity control' }
  ]

  const currentRoles = formData.scope === 'tenant' ? tenantRoles : entityRoles
  
  // Set default role when scope changes
  const handleScopeChange = (scope: 'tenant' | 'entity') => {
    setFormData({
      ...formData,
      scope,
      role: scope === 'tenant' ? 'tenant_member' : 'viewer'
    })
  }

  // Get the appropriate scope ID
  const getScopeId = () => {
    if (formData.scope === 'tenant') {
      return selection.tenantId
    } else {
      return selection.entityId || selection.tenantId // Fall back to tenant if no entity selected
    }
  }

  const handleSubmitAsync = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const scopeId = getScopeId()
      if (!scopeId) {
        throw new Error('No organization or entity selected')
      }

      const response = await fetch('/api/invites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...addCSRFHeaders()
        },
        body: JSON.stringify({
          scope: formData.scope,
          scopeId,
          email: formData.email,
          role: formData.role
        })
      })

      const result = await response.json() as { error?: string; message?: string; success?: boolean }

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send invitation')
      }

      setSuccess(result.message || 'Invitation sent successfully!')
      setFormData({ email: '', role: formData.scope === 'tenant' ? 'tenant_member' : 'viewer', scope: formData.scope })
      
      onInviteSent?.(formData.email, formData.role)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send invitation')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent): void => {
    void handleSubmitAsync(e)
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Send Invitation</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Scope Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Invitation Level
          </label>
          <div className="grid grid-cols-2 gap-2">
            <button
              type="button"
              onClick={() => handleScopeChange('tenant')}
              className={`px-3 py-2 text-sm rounded-md border ${
                formData.scope === 'tenant'
                  ? 'bg-blue-50 border-blue-300 text-blue-900'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Organization
            </button>
            <button
              type="button"
              onClick={() => handleScopeChange('entity')}
              disabled={!selection.entityId}
              className={`px-3 py-2 text-sm rounded-md border ${
                formData.scope === 'entity'
                  ? 'bg-blue-50 border-blue-300 text-blue-900'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              } ${!selection.entityId ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Entity
            </button>
          </div>
          {!selection.entityId && (
            <p className="text-xs text-gray-500 mt-1">
              Select an entity to invite users at entity level
            </p>
          )}
        </div>

        {/* Email Input */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Role Selection */}
        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
            Role
          </label>
          <select
            id="role"
            value={formData.role}
            onChange={(e) => setFormData({ ...formData, role: e.target.value })}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a role</option>
            {currentRoles.map((role) => (
              <option key={role.value} value={role.value}>
                {role.label} - {role.description}
              </option>
            ))}
          </select>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-700">{success}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-3 pt-2">
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading || !formData.email || !formData.role}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Sending...' : 'Send Invitation'}
          </button>
        </div>
      </form>

      {/* CSRF Token */}
      {csrfToken && (
        <input type="hidden" name="_csrf_token" value={csrfToken} />
      )}
    </div>
  )
}