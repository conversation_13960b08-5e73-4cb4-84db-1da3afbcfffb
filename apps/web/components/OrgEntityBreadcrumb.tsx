'use client'

import { useOrgEntity } from '@/contexts/OrgEntityContext'

export function OrgEntityBreadcrumb() {
  const { selection, loading } = useOrgEntity()

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <div className="w-20 h-4 bg-gray-200 animate-pulse rounded"></div>
        <span>/</span>
        <div className="w-16 h-4 bg-gray-200 animate-pulse rounded"></div>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500">
      <span className="font-medium">{selection.tenantName || 'No Organization'}</span>
      {selection.mode === 'entity' && selection.entityName && (
        <>
          <span>/</span>
          <span className="font-medium text-gray-900">{selection.entityName}</span>
        </>
      )}
    </div>
  )
}