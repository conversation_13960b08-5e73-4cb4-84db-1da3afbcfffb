'use client'

import { useSubscription } from '@/hooks/useSubscription'

interface SubscriptionCardProps {
  tenantId?: number
}

export function SubscriptionCard({ tenantId }: SubscriptionCardProps) {
  const { subscription, usage, loading, error, isWithinLimits } = useSubscription(tenantId)

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6 border border-red-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Subscription</h2>
        <p className="text-sm text-red-600">{error}</p>
      </div>
    )
  }

  if (!subscription || !usage) {
    return (
      <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Subscription</h2>
        <p className="text-sm text-gray-600">No subscription information available</p>
      </div>
    )
  }

  const withinLimits = isWithinLimits()
  const isTrialPlan = subscription.plan === 'trial'

  return (
    <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
      <div className="flex justify-between items-start mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {isTrialPlan ? 'Trial Plan' : `${subscription.plan} Plan`}
        </h2>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          subscription.status === 'active' ? 'bg-green-100 text-green-800' :
          subscription.status === 'trial' ? 'bg-blue-100 text-blue-800' :
          'bg-red-100 text-red-800'
        }`}>
          {subscription.status}
        </span>
      </div>

      {isTrialPlan && subscription.trial_ends_at && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-700">
            Trial expires on {new Date(subscription.trial_ends_at).toLocaleDateString()}
          </p>
        </div>
      )}

      {!withinLimits && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700">
            You&apos;ve exceeded your plan limits. Consider upgrading your subscription.
          </p>
        </div>
      )}

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Entities</span>
          <span className="text-sm text-gray-600">
            {usage.entities_used} / {subscription.limits.entities === -1 ? '∞' : subscription.limits.entities}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Active users</span>
          <span className="text-sm text-gray-600">
            {usage.users_active} / {subscription.limits.users === -1 ? '∞' : subscription.limits.users}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Transactions this month</span>
          <span className="text-sm text-gray-600">
            {usage.transactions_count} / {subscription.limits.transactions_per_month === -1 ? '∞' : subscription.limits.transactions_per_month}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Storage</span>
          <span className="text-sm text-gray-600">
            {usage.storage_used_gb.toFixed(2)} GB / {subscription.limits.storage_gb === -1 ? '∞' : subscription.limits.storage_gb} GB
          </span>
        </div>
      </div>

      {isTrialPlan && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Upgrade Plan
          </button>
        </div>
      )}
    </div>
  )
}