'use client'

import React, { useState, CSSProperties } from 'react'
import { useRouter } from 'next/navigation'
import styles from './MinimalDashboard.module.css'
import { useDashboardMetrics } from '@/hooks/useDashboardMetrics'
import { useVATSummary } from '@/hooks/useVATSummary'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'
import GettingStartedChecklist from './GettingStartedChecklist'
import ActionableEmptyState, { EmptyStateConfigs } from './ActionableEmptyState'
import AutomationHelpers from './AutomationHelpers'
import AICopilotNudges, { createWelcomeNudges } from './AICopilotNudges'
import VATSetupWizard from './VATSetupWizard'

interface MinimalDashboardProps {
  className?: string
  'data-theme'?: 'light' | 'dark'
}

// Zen UI Theme - Following the style guide exactly
const zenTheme = {
  // Primary Colors - CORRECTED to match style guide
  bg: '#FBFAF5', // soft cream background (was wrong before)
  surface: '#FFFFFC', // pure white for cards
  primaryText: '#1a1a1a', // near black
  secondaryText: '#6b7280', // warm gray
  subtleText: '#9ca3af', // light gray

  // Borders and Surfaces
  border: '#f3f4f6', // subtle border
  borderHover: '#e5e7eb', // slightly darker on hover

  // Accent Colors (use sparingly)
  success: '#10b981', // soft green
  warning: '#f59e0b', // warm amber
  error: '#ef4444', // soft red
  primaryAction: '#3b82f6', // calm blue (not black for primary action)

  // Shadows and Effects
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)', // very subtle
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', // slightly more on hover
}

// Zen UI Style functions
const getHeaderStyle = (theme: typeof zenTheme): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '24px 32px', // increased padding for generous whitespace
  borderBottom: `1px solid ${theme.border}`,
  background: theme.surface,
})

const getBrandStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: 0,
  color: theme.primaryText,
  letterSpacing: '-0.025em', // slightly tighter for modern look
})

const getSearchInputStyle = (theme: typeof zenTheme): CSSProperties => ({
  width: '320px',
  padding: '10px 16px', // slightly increased for better touch targets
  border: `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for cleaner look
  background: theme.surface,
  color: theme.primaryText,
  fontSize: '14px',
  outline: 'none',
  transition: 'all 0.15s ease',
})

const getUserAvatarStyle = (theme: typeof zenTheme): CSSProperties => ({
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  background: theme.primaryAction, // using primary action color
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: '#FFFFFF',
  fontSize: '12px',
  fontWeight: 600,
  cursor: 'pointer',
  transition: 'all 0.15s ease',
})

const getTrialBannerStyle = (
  dismissed: boolean,
  theme: typeof zenTheme
): CSSProperties => ({
  display: dismissed ? 'none' : 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '16px 32px', // increased padding
  background: '#FEF3C7', // keeping warm amber background
  borderBottom: `1px solid ${theme.warning}`, // using theme warning color
  color: '#92400E',
  fontSize: '14px',
})

const getDismissButtonStyle = (_theme: typeof zenTheme): CSSProperties => ({
  background: 'none',
  border: 'none',
  color: '#92400E',
  cursor: 'pointer',
  padding: '6px', // slightly increased
  borderRadius: '4px',
  transition: 'all 0.15s ease',
})

const getMainContentStyle = (): CSSProperties => ({
  display: 'grid',
  gridTemplateColumns: 'minmax(0, 1fr) 320px',
  gap: '48px', // increased for generous whitespace
  padding: '48px', // increased padding
  maxWidth: '1400px',
  margin: '0 auto',
})

const getContentSectionStyle = (): CSSProperties => ({
  display: 'flex',
  flexDirection: 'column',
  gap: '32px', // generous spacing between sections
})

const getMetricsGridStyle = (): CSSProperties => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
  gap: '16px',
})

const getMetricCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced from 16px for cleaner look
  padding: '24px', // increased padding for generous whitespace
  boxShadow: theme.shadow,
  transition: 'all 0.15s ease', // subtle interaction
})

const getMetricLabelStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '12px', // caption style from guide
  fontWeight: 500,
  color: theme.secondaryText,
  margin: '0 0 8px 0',
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
})

const getMetricValueStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '32px', // display size for emphasis
  fontWeight: 600,
  color: theme.primaryText,
  margin: '0',
  lineHeight: 1.2,
})

const getQuickActionsStyle = (): CSSProperties => ({
  display: 'flex',
  gap: '12px',
  flexWrap: 'wrap',
})

const getActionButtonStyle = (
  theme: typeof zenTheme,
  variant: 'primary' | 'secondary' = 'secondary'
): CSSProperties => ({
  padding: '10px 20px', // following style guide
  border: variant === 'primary' ? 'none' : `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for cleaner look
  background: variant === 'primary' ? '#1a1a1a' : 'transparent', // confident near-black for buttons
  color: variant === 'primary' ? '#FFFFFF' : theme.secondaryText,
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
  transition: 'all 0.15s ease', // faster, more subtle
})

const getDisabledActionButtonStyle = (
  theme: typeof zenTheme
): CSSProperties => ({
  ...getActionButtonStyle(theme, 'secondary'),
  opacity: 0.5,
  cursor: 'not-allowed',
  background: 'transparent',
  color: theme.subtleText,
})

const getAgentsPanelStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced for consistency
  padding: '24px', // generous whitespace
  boxShadow: theme.shadow,
  height: 'fit-content',
})

const getAgentsPanelTitleStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: '0 0 20px 0',
  color: theme.primaryText,
})

const getAgentCardStyle = (
  theme: typeof zenTheme,
  isLast: boolean
): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 0',
  borderBottom: isLast ? 'none' : `1px solid ${theme.border}`,
})

const getAgentInfoStyle = (): CSSProperties => ({
  flex: 1,
})

const getAgentNameStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '14px',
  fontWeight: 500,
  margin: '0 0 4px 0',
  color: theme.primaryText,
})

const getAgentPurposeStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '12px',
  color: theme.secondaryText,
  margin: '0',
})

const getAgentControlsStyle = (): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
})

const getStatusPillStyle = (
  status: 'idle' | 'running' | 'done'
): CSSProperties => {
  const baseStyle: CSSProperties = {
    padding: '4px 8px',
    borderRadius: '12px',
    fontSize: '12px',
    fontWeight: 500,
  }

  switch (status) {
    case 'idle':
      return { ...baseStyle, background: '#F1F5F9', color: '#0B0F14' }
    case 'running':
      return { ...baseStyle, background: '#D6E4FF', color: '#1D4ED8' }
    case 'done':
      return { ...baseStyle, background: '#DCFCE7', color: '#166534' }
  }
}

const getRunButtonStyle = (theme: typeof zenTheme): CSSProperties => ({
  padding: '6px 12px',
  border: `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for consistency
  background: theme.surface,
  color: theme.primaryText,
  fontSize: '12px',
  cursor: 'pointer',
  transition: 'all 0.15s ease',
})

const getToggleStyle = (): CSSProperties => ({
  // Most styles are handled by CSS module for pseudo-selectors
})

const getRecentActivityCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced for consistency
  padding: '24px',
  boxShadow: theme.shadow,
})

const getSectionTitleStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: '0 0 16px 0',
  color: theme.primaryText,
})

const getEmptyStateStyle = (theme: typeof zenTheme): CSSProperties => ({
  textAlign: 'center',
  padding: '32px',
  color: theme.secondaryText,
})

const getEmptyStateTextStyle = (): CSSProperties => ({
  fontSize: '16px',
  margin: '0 0 8px 0',
})

const getEmptyStateSubtextStyle = (): CSSProperties => ({
  fontSize: '14px',
  margin: '0',
})

export default function MinimalDashboard({
  className,
  'data-theme': themeMode = 'light',
}: MinimalDashboardProps) {
  const [bannerDismissed, setBannerDismissed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [showVATSetup, setShowVATSetup] = useState(false)
  const theme = zenTheme // Always use Zen UI theme
  const router = useRouter()
  const { user } = useAuth()

  // Data hooks
  const {
    metrics,
    loading: metricsLoading,
    error: metricsError,
    refreshMetrics,
  } = useDashboardMetrics()
  const {
    vatPosition,
    loading: vatLoading,
    error: vatError,
    refreshVAT,
  } = useVATSummary()
  const {
    currentEntity,
    isValid,
    loading: entityLoading,
  } = useOrgEntitySelection()

  // Debug entity context (can be removed in production)
  React.useEffect(() => {
    console.log('Dashboard state:', {
      user: user ? 'authenticated' : 'not authenticated',
      isValid,
      currentEntity: currentEntity ? currentEntity.entity_name : 'none',
      entityLoading,
      navigating,
    })
  }, [user, isValid, currentEntity, entityLoading, navigating])

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!user && !entityLoading) {
      console.log('User not authenticated, redirecting to login...')
      router.push('/login')
    }
  }, [user, entityLoading, router])

  // Clear navigation state on unmount to prevent stale state
  React.useEffect(() => {
    return () => {
      setNavigating(null)
    }
  }, [])

  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1200)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Clear navigation state after a delay to handle route transitions
  React.useEffect(() => {
    if (navigating) {
      const timer = setTimeout(() => {
        setNavigating(null)
      }, 2000) // Clear after 2 seconds
      return () => clearTimeout(timer)
    }
  }, [navigating])

  const dashboardStyle: CSSProperties = {
    color: theme.primaryText,
    fontFamily:
      "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
    lineHeight: 1.5, // improved readability
  }

  const responsiveMainContentStyle: CSSProperties = {
    ...getMainContentStyle(),
    gridTemplateColumns: isMobile ? '1fr' : 'minmax(0, 1fr) 320px',
    gap: isMobile ? '32px' : '48px', // increased gap for generous whitespace
    padding: isMobile ? '24px' : '48px', // added generous padding
  }

  const responsiveSearchStyle: CSSProperties = {
    ...getSearchInputStyle(theme),
    width: isMobile ? '200px' : '320px',
  }

  // Format metrics for display
  const formatCurrency = (amount: number, currency = 'EUR') => {
    return new Intl.NumberFormat('en-BE', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const formatVATPosition = () => {
    if (vatLoading) return 'Loading...'
    if (vatError) return 'Error loading VAT'
    if (!vatPosition) return 'VAT not configured'
    const amount = formatCurrency(vatPosition.amount, vatPosition.currency)
    return `${amount} ${vatPosition.type}`
  }

  const formatBankBalance = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading balance'
    if (!metrics.bankBalance) return 'No accounts'
    return formatCurrency(
      metrics.bankBalance.total,
      metrics.bankBalance.currency
    )
  }

  const formatOpenInvoices = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading invoices'
    if (!metrics.openInvoices) return 'No invoices'
    const amount = formatCurrency(
      metrics.openInvoices.totalAmount,
      metrics.openInvoices.currency
    )
    return `${metrics.openInvoices.count} (${amount})`
  }

  const formatInboxCount = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading inbox'
    if (!metrics.inboxToReview) return 'No documents'
    const count = metrics.inboxToReview.count
    return `${count} document${count !== 1 ? 's' : ''}`
  }

  const formatReconciliationTasks = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading tasks'
    if (!metrics.reconciliationTasks) return 'No tasks'
    const count = metrics.reconciliationTasks.count
    return `${count} pending`
  }

  const dashboardMetrics = [
    { label: 'Next VAT', value: formatVATPosition() },
    { label: 'Bank Balance', value: formatBankBalance() },
    { label: 'Open Invoices', value: formatOpenInvoices() },
    { label: 'Inbox to Review', value: formatInboxCount() },
    { label: 'Reconciliation Tasks', value: formatReconciliationTasks() },
  ]

  const agents = [
    {
      name: 'Invoice Extractor',
      purpose: 'Extracts data from uploaded invoices',
      status: 'idle' as const,
    },
    {
      name: 'Bank Reconciler',
      purpose: 'Matches transactions automatically',
      status: 'running' as const,
    },
    {
      name: 'VAT Co-Pilot',
      purpose: 'Assists with VAT calculations',
      status: 'done' as const,
    },
    {
      name: 'Compliance Reminders',
      purpose: 'Tracks deadlines and requirements',
      status: 'idle' as const,
    },
  ]

  const handleRefreshData = async () => {
    await Promise.all([refreshMetrics(), refreshVAT()])
  }

  // Helper functions to determine completion status for getting started checklist
  const hasUploadedDocument = () => {
    return metrics?.inboxToReview?.count !== undefined && metrics.inboxToReview.count > 0
  }

  const hasBankConnection = () => {
    return metrics?.bankBalance?.total !== undefined && metrics.bankBalance.total !== null
  }

  const hasVATConfigured = () => {
    return vatPosition !== null && !vatError
  }

  // Show entity context in header
  const getEntityDisplayName = () => {
    if (!user) return 'Not Authenticated'
    if (!isValid || !currentEntity) return 'No Entity Selected'
    return currentEntity.entity_name
  }

  // Show loading state if user is not authenticated
  if (!user) {
    return (
      <div
        style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.primaryText,
          fontFamily:
            "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <p>Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Navigation handlers for action buttons
  const handleNavigateToInbox = () => {
    console.log('Upload Document button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Always allow navigation. The Inbox page will guide entity selection and upload.
    try {
      console.log('Starting navigation to inbox...')
      setNavigating('inbox')
      router.push('/inbox')
    } catch (error) {
      console.error('Navigation to inbox failed:', error)
      setNavigating(null)
    }
  }

  const handleNavigateToInvoiceCreation = () => {
    console.log('Create Invoice button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Allow navigation even if entity isn't selected; the ledger will handle context.
    try {
      console.log('Starting navigation to ledger (invoice creation)...')
      setNavigating('invoice')
      // Navigate to ledger as invoice creation UI doesn't exist yet
      router.push('/ledger')
    } catch (error) {
      console.error('Navigation to invoice creation failed:', error)
      setNavigating(null)
    }
  }

  const handleNavigateToBankReconciliation = () => {
    console.log('Reconcile Bank button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Allow navigation even if entity isn't selected; the ledger will handle context.
    try {
      console.log('Starting navigation to ledger (bank reconciliation)...')
      setNavigating('reconcile')
      // Navigate to ledger as bank reconciliation UI doesn't exist yet
      router.push('/ledger')
    } catch (error) {
      console.error('Navigation to bank reconciliation failed:', error)
      setNavigating(null)
    }
  }

  return (
    <div
      className={`${className} ${themeMode === 'dark' ? styles.dark : styles.light}`}
      style={dashboardStyle}
    >
      <header className={styles.header} style={getHeaderStyle(theme)}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <h1 className={styles.brand} style={getBrandStyle(theme)}>
            BelBooks
          </h1>
          <p
            style={{ fontSize: '12px', color: theme.secondaryText, margin: 0 }}
          >
            {getEntityDisplayName()}
          </p>
        </div>
        <input
          className={styles.searchInput}
          style={responsiveSearchStyle}
          placeholder="Search transactions, invoices, contacts..."
        />
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <button
            onClick={() => {
              void handleRefreshData()
            }}
            style={{
              background: 'none',
              border: `1px solid ${theme.border}`,
              borderRadius: '8px',
              padding: '6px 12px',
              color: theme.primaryText,
              fontSize: '12px',
              cursor: 'pointer',
            }}
            aria-label="Refresh data"
          >
            ↻
          </button>
          <div
            className={styles.userAvatar}
            style={getUserAvatarStyle(theme)}
            tabIndex={0}
            role="button"
            aria-label="User menu"
          >
            JK
          </div>
        </div>
      </header>

      <div style={getTrialBannerStyle(bannerDismissed, theme)}>
        <span>
          Trial expires in 14 days - Upgrade to continue using BelBooks
        </span>
        <button
          className={styles.dismissButton}
          style={getDismissButtonStyle(theme)}
          onClick={() => setBannerDismissed(true)}
          aria-label="Dismiss banner"
        >
          ×
        </button>
      </div>

      <main className={styles.mainContent} style={responsiveMainContentStyle}>
        <section style={getContentSectionStyle()}>
          <div style={getMetricsGridStyle()}>
            {/* VAT Position Card */}
            <div style={getMetricCardStyle(theme)}>
              <p style={getMetricLabelStyle(theme)}>Next VAT</p>
              {!hasVATConfigured() ? (
                <ActionableEmptyState
                  {...EmptyStateConfigs.vatNotConfigured}
                  onAction={() => setShowVATSetup(true)}
                />
              ) : (
                <p style={getMetricValueStyle(theme)}>{formatVATPosition()}</p>
              )}
            </div>

            {/* Bank Balance Card */}
            <div style={getMetricCardStyle(theme)}>
              <p style={getMetricLabelStyle(theme)}>Bank Balance</p>
              {!hasBankConnection() ? (
                <ActionableEmptyState
                  {...EmptyStateConfigs.noAccounts}
                  onAction={() => router.push('/ledger')}
                />
              ) : (
                <p style={getMetricValueStyle(theme)}>{formatBankBalance()}</p>
              )}
            </div>

            {/* Open Invoices Card */}
            <div style={getMetricCardStyle(theme)}>
              <p style={getMetricLabelStyle(theme)}>Open Invoices</p>
              <p style={getMetricValueStyle(theme)}>{formatOpenInvoices()}</p>
            </div>

            {/* Inbox Card */}
            <div style={getMetricCardStyle(theme)}>
              <p style={getMetricLabelStyle(theme)}>Inbox to Review</p>
              {!hasUploadedDocument() ? (
                <ActionableEmptyState
                  {...EmptyStateConfigs.noDocuments}
                  onAction={handleNavigateToInbox}
                />
              ) : (
                <p style={getMetricValueStyle(theme)}>{formatInboxCount()}</p>
              )}
            </div>

            {/* Reconciliation Tasks Card */}
            <div style={getMetricCardStyle(theme)}>
              <p style={getMetricLabelStyle(theme)}>Reconciliation Tasks</p>
              <p style={getMetricValueStyle(theme)}>{formatReconciliationTasks()}</p>
            </div>
          </div>

          {/* Getting Started Checklist - Show for new users */}
          <GettingStartedChecklist
            hasUploadedDocument={hasUploadedDocument()}
            hasBankConnection={hasBankConnection()}
            hasVATConfigured={hasVATConfigured()}
            onItemClick={(itemId) => {
              console.log('Getting started item clicked:', itemId)
            }}
          />

          <div style={getQuickActionsStyle()}>
            <button
              className={styles.actionButton}
              style={
                navigating === 'inbox'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme, 'primary') // Make primary action
              }
              onClick={handleNavigateToInbox}
              disabled={navigating === 'inbox'}
              aria-label="Upload Document - Navigate to Inbox"
              title={'Upload and manage documents in the inbox'}
            >
              {navigating === 'inbox' ? 'Loading...' : 'Upload Document'}
            </button>
            <button
              className={styles.actionButton}
              style={
                navigating === 'invoice'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme)
              }
              onClick={handleNavigateToInvoiceCreation}
              disabled={navigating === 'invoice'}
              aria-label="Create Invoice - Navigate to Ledger"
              title={'Create and manage invoices (opens ledger)'}
            >
              {navigating === 'invoice' ? 'Loading...' : 'Create Invoice'}
            </button>
            <button
              className={styles.actionButton}
              style={
                navigating === 'reconcile'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme)
              }
              onClick={handleNavigateToBankReconciliation}
              disabled={navigating === 'reconcile'}
              aria-label="Reconcile Bank - Navigate to Ledger"
              title={'Reconcile bank transactions (opens ledger)'}
            >
              {navigating === 'reconcile' ? 'Loading...' : 'Reconcile Bank'}
            </button>
          </div>

          <div style={getRecentActivityCardStyle(theme)}>
            <h2 style={getSectionTitleStyle(theme)}>Recent Activity</h2>
            <div style={getEmptyStateStyle(theme)}>
              <p style={getEmptyStateTextStyle()}>
                No recent activity to display
              </p>
              <p style={getEmptyStateSubtextStyle()}>
                Activity will appear here once you start using the application
              </p>
            </div>
          </div>
        </section>

        <aside
          className={styles.agentsPanel}
          style={getAgentsPanelStyle(theme)}
        >
          <AutomationHelpers
            onToggle={(helperId, enabled) => {
              console.log('Automation helper toggled:', helperId, enabled)
              // TODO: Implement actual toggle logic for automation features
            }}
          />
        </aside>
      </main>

      {/* AI Copilot Nudges for onboarding guidance */}
      <AICopilotNudges
        nudges={createWelcomeNudges({
          onUploadDocument: handleNavigateToInbox,
          onSetupVAT: () => setShowVATSetup(true),
          onConnectBank: () => router.push('/ledger'),
        })}
        onDismiss={(nudgeId) => {
          console.log('Nudge dismissed:', nudgeId)
          // TODO: Store dismissed nudges in user preferences
        }}
      />

      {/* VAT Setup Wizard */}
      <VATSetupWizard
        isOpen={showVATSetup}
        onClose={() => setShowVATSetup(false)}
        onComplete={(config) => {
          console.log('VAT setup completed:', config)
          setShowVATSetup(false)
          // TODO: Save VAT configuration to backend
          // TODO: Refresh VAT data after setup
          void refreshVAT()
        }}
      />
    </div>
  )
}
