'use client'

import React from 'react'
import { useRouter } from 'next/navigation'

interface ChecklistItem {
  id: string
  title: string
  description: string
  completed: boolean
  action: string
  route?: string
  onClick?: () => void
}

interface GettingStartedChecklistProps {
  className?: string
  onItemClick?: (itemId: string) => void
  // Props to determine completion status
  hasUploadedDocument?: boolean
  hasBankConnection?: boolean
  hasVATConfigured?: boolean
}

const zenTheme = {
  surface: '#FFFFFF',
  border: '#E5E7EB',
  primaryText: '#0B0F14',
  secondaryText: '#475569',
  accent: '#111827',
  accentBlue: '#2563EB',
  success: '#059669',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

export default function GettingStartedChecklist({
  className = '',
  onItemClick,
  hasUploadedDocument = false,
  hasBankConnection = false,
  hasVATConfigured = false,
}: GettingStartedChecklistProps) {
  const router = useRouter()

  const handleVATSetup = () => {
    // For now, navigate to VAT page - we'll create a setup wizard later
    router.push('/vat')
    onItemClick?.('vat-setup')
  }

  const handleBankConnection = () => {
    // Navigate to bank import/connection - we'll enhance this later
    router.push('/ledger') // Placeholder until bank connection UI exists
    onItemClick?.('bank-connection')
  }

  const handleDocumentUpload = () => {
    router.push('/inbox')
    onItemClick?.('document-upload')
  }

  const checklistItems: ChecklistItem[] = [
    {
      id: 'document-upload',
      title: 'Upload your first supplier invoice',
      description: 'Start by uploading a PDF or photo of a supplier invoice to see our AI extraction in action',
      completed: hasUploadedDocument,
      action: 'Upload Document',
      route: '/inbox',
      onClick: handleDocumentUpload,
    },
    {
      id: 'bank-connection',
      title: 'Connect your bank account',
      description: 'Import bank statements (CODA/CSV) to enable automatic reconciliation',
      completed: hasBankConnection,
      action: 'Connect Bank',
      onClick: handleBankConnection,
    },
    {
      id: 'vat-setup',
      title: 'Configure VAT settings',
      description: 'Set up VAT rates and preferences to track your VAT position in real-time',
      completed: hasVATConfigured,
      action: 'Setup VAT',
      onClick: handleVATSetup,
    },
  ]

  const completedCount = checklistItems.filter(item => item.completed).length
  const totalCount = checklistItems.length
  const progressPercentage = (completedCount / totalCount) * 100

  const getCardStyle = () => ({
    background: zenTheme.surface,
    border: `1px solid ${zenTheme.border}`,
    borderRadius: '8px',
    padding: '24px',
    boxShadow: zenTheme.shadow,
  })

  const getHeaderStyle = () => ({
    marginBottom: '20px',
  })

  const getTitleStyle = () => ({
    fontSize: '20px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: '0 0 8px 0',
  })

  const getSubtitleStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: '0 0 16px 0',
  })

  const getProgressBarContainerStyle = () => ({
    width: '100%',
    height: '8px',
    backgroundColor: '#F3F4F6',
    borderRadius: '4px',
    overflow: 'hidden',
    marginBottom: '4px',
  })

  const getProgressBarStyle = () => ({
    height: '100%',
    backgroundColor: zenTheme.success,
    borderRadius: '4px',
    width: `${progressPercentage}%`,
    transition: 'width 0.3s ease',
  })

  const getProgressTextStyle = () => ({
    fontSize: '12px',
    color: zenTheme.secondaryText,
    textAlign: 'right' as const,
  })

  const getItemStyle = (completed: boolean) => ({
    display: 'flex',
    alignItems: 'flex-start',
    gap: '16px',
    padding: '16px 0',
    borderBottom: `1px solid ${zenTheme.border}`,
  })

  const getCheckboxStyle = (completed: boolean) => ({
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    border: `2px solid ${completed ? zenTheme.success : zenTheme.border}`,
    backgroundColor: completed ? zenTheme.success : 'transparent',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
    marginTop: '2px',
  })

  const getCheckmarkStyle = () => ({
    color: 'white',
    fontSize: '12px',
    fontWeight: 'bold',
  })

  const getItemContentStyle = () => ({
    flex: 1,
    minWidth: 0,
  })

  const getItemTitleStyle = (completed: boolean) => ({
    fontSize: '16px',
    fontWeight: 500,
    color: completed ? zenTheme.secondaryText : zenTheme.primaryText,
    margin: '0 0 4px 0',
    textDecoration: completed ? 'line-through' : 'none',
  })

  const getItemDescriptionStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: '0',
    lineHeight: 1.4,
  })

  const getActionButtonStyle = (completed: boolean) => ({
    padding: '8px 16px',
    borderRadius: '6px',
    border: 'none',
    fontSize: '14px',
    fontWeight: 500,
    cursor: completed ? 'default' : 'pointer',
    backgroundColor: completed ? '#F3F4F6' : zenTheme.accentBlue,
    color: completed ? zenTheme.secondaryText : 'white',
    opacity: completed ? 0.6 : 1,
    transition: 'all 0.2s ease',
    flexShrink: 0,
  })

  return (
    <div className={className} style={getCardStyle()}>
      <div style={getHeaderStyle()}>
        <h2 style={getTitleStyle()}>Getting Started</h2>
        <p style={getSubtitleStyle()}>
          Complete these steps to get the most out of BelBooks
        </p>
        <div style={getProgressBarContainerStyle()}>
          <div style={getProgressBarStyle()} />
        </div>
        <div style={getProgressTextStyle()}>
          {completedCount} of {totalCount} completed
        </div>
      </div>

      <div>
        {checklistItems.map((item, index) => (
          <div
            key={item.id}
            style={{
              ...getItemStyle(item.completed),
              borderBottom: index === checklistItems.length - 1 ? 'none' : `1px solid ${zenTheme.border}`,
            }}
          >
            <div style={getCheckboxStyle(item.completed)}>
              {item.completed && <span style={getCheckmarkStyle()}>✓</span>}
            </div>
            <div style={getItemContentStyle()}>
              <h3 style={getItemTitleStyle(item.completed)}>{item.title}</h3>
              <p style={getItemDescriptionStyle()}>{item.description}</p>
            </div>
            <button
              style={getActionButtonStyle(item.completed)}
              onClick={item.onClick}
              disabled={item.completed}
              aria-label={`${item.action} - ${item.title}`}
            >
              {item.completed ? 'Done' : item.action}
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}
