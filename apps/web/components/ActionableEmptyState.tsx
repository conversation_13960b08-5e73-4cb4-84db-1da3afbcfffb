'use client'

import React from 'react'

interface ActionableEmptyStateProps {
  title: string
  description: string
  actionText: string
  onAction: () => void
  icon?: React.ReactNode
  variant?: 'default' | 'primary' | 'secondary'
  className?: string
}

const zenTheme = {
  surface: '#FFFFFF',
  border: '#E5E7EB',
  primaryText: '#0B0F14',
  secondaryText: '#475569',
  accent: '#111827',
  accentBlue: '#2563EB',
  success: '#059669',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

export default function ActionableEmptyState({
  title,
  description,
  actionText,
  onAction,
  icon,
  variant = 'default',
  className = '',
}: ActionableEmptyStateProps) {
  const getContainerStyle = () => ({
    textAlign: 'center' as const,
    padding: '32px 24px',
    color: zenTheme.secondaryText,
  })

  const getIconStyle = () => ({
    fontSize: '48px',
    marginBottom: '16px',
    opacity: 0.6,
  })

  const getTitleStyle = () => ({
    fontSize: '18px',
    fontWeight: 600,
    margin: '0 0 8px 0',
    color: zenTheme.primaryText,
  })

  const getDescriptionStyle = () => ({
    fontSize: '14px',
    margin: '0 0 20px 0',
    lineHeight: 1.5,
    color: zenTheme.secondaryText,
  })

  const getButtonStyle = () => {
    const baseStyle = {
      padding: '12px 24px',
      borderRadius: '8px',
      border: 'none',
      fontSize: '14px',
      fontWeight: 500,
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      textDecoration: 'none',
      display: 'inline-block',
    }

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: zenTheme.accentBlue,
          color: 'white',
        }
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          color: zenTheme.accentBlue,
          border: `1px solid ${zenTheme.accentBlue}`,
        }
      default:
        return {
          ...baseStyle,
          backgroundColor: zenTheme.accent,
          color: 'white',
        }
    }
  }

  return (
    <div className={className} style={getContainerStyle()}>
      {icon && <div style={getIconStyle()}>{icon}</div>}
      <h3 style={getTitleStyle()}>{title}</h3>
      <p style={getDescriptionStyle()}>{description}</p>
      <button style={getButtonStyle()} onClick={onAction}>
        {actionText}
      </button>
    </div>
  )
}

// Predefined empty state configurations for common use cases
export const EmptyStateConfigs = {
  vatNotConfigured: {
    title: 'VAT not configured',
    description: 'Set up your VAT rates and preferences to track your VAT position in real-time.',
    actionText: 'Configure VAT →',
    icon: '📊',
    variant: 'primary' as const,
  },
  noAccounts: {
    title: 'No bank accounts connected',
    description: 'Connect your bank account to import transactions and enable automatic reconciliation.',
    actionText: 'Connect Bank →',
    icon: '🏦',
    variant: 'primary' as const,
  },
  noInvoices: {
    title: 'No invoices yet',
    description: 'Create your first invoice to start tracking sales and managing your business.',
    actionText: 'Create Invoice →',
    icon: '📄',
    variant: 'primary' as const,
  },
  noDocuments: {
    title: 'No documents uploaded',
    description: 'Upload a supplier invoice (PDF or photo) to see our AI extraction in action.',
    actionText: 'Upload Document →',
    icon: '📎',
    variant: 'primary' as const,
  },
  noReconciliation: {
    title: 'No reconciliation tasks',
    description: 'Import bank transactions to start matching them with your invoices and journal entries.',
    actionText: 'Import Transactions →',
    icon: '🔗',
    variant: 'secondary' as const,
  },
}
