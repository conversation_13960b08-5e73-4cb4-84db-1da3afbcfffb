'use client'

import React, { useEffect, useState } from 'react'
import {
  validatePasswordSync,
  getPasswordStrengthIndicators,
  type PasswordValidationResult,
} from '@/lib/password-policy'

interface PasswordStrengthProps {
  password: string
  userInputs?: string[]
  showDetails?: boolean
  className?: string
}

export function PasswordStrength({
  password,
  userInputs = [],
  showDetails = true,
  className = '',
}: PasswordStrengthProps) {
  const [validation, setValidation] = useState<Omit<
    PasswordValidationResult,
    'isCompromised'
  > | null>(null)

  useEffect(() => {
    if (password) {
      const result = validatePasswordSync(password)
      setValidation(result)
    } else {
      setValidation(null)
    }
  }, [password, userInputs])

  if (!validation || !password) {
    return null
  }

  const indicators = getPasswordStrengthIndicators(password)

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Strength Bar */}
      <div className="space-y-1">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">Password strength</span>
          <span
            className="font-medium"
            style={{ color: indicators.strength.color }}
          >
            {indicators.strength.label}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="h-2 rounded-full transition-all duration-300 ease-out"
            style={{
              width: `${indicators.strength.score}%`,
              backgroundColor: indicators.strength.color,
            }}
          />
        </div>
      </div>

      {/* Estimated crack time */}
      {showDetails && (
        <div className="text-sm text-gray-600">
          <span className="font-medium">Password strength:</span>{' '}
          {indicators.strength.label}
        </div>
      )}

      {/* Errors */}
      {showDetails && !validation.isValid && (
        <div className="space-y-1">
          <div className="text-sm font-medium text-red-600">
            Requirements not met:
          </div>
          <ul className="text-sm text-red-600 space-y-1">
            {validation.failedRequirements.map((requirement, index) => (
              <li key={index} className="flex items-start gap-1">
                <span className="text-red-500 mt-1">•</span>
                <span>{requirement.label}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

/**
 * Compact version showing just the strength bar
 */
export function PasswordStrengthCompact({
  password,
  userInputs = [],
  className = '',
}: PasswordStrengthProps) {
  return (
    <PasswordStrength
      password={password}
      userInputs={userInputs}
      showDetails={false}
      className={className}
    />
  )
}

/**
 * Hook for password validation in forms
 */
export function usePasswordValidation(
  password: string,
  userInputs: string[] = []
) {
  const [validation, setValidation] = useState<Omit<
    PasswordValidationResult,
    'isCompromised'
  > | null>(null)
  const [isValidating, setIsValidating] = useState(false)

  useEffect(() => {
    if (!password) {
      setValidation(null)
      return
    }

    setIsValidating(true)

    // Debounce validation to avoid excessive computation
    const timer = setTimeout(() => {
      const result = validatePasswordSync(password)
      setValidation(result)
      setIsValidating(false)
    }, 300)

    return () => {
      clearTimeout(timer)
      setIsValidating(false)
    }
  }, [password, userInputs])

  return {
    validation,
    isValidating,
    isValid: validation?.isValid ?? false,
    indicators: password ? getPasswordStrengthIndicators(password) : null,
  }
}
