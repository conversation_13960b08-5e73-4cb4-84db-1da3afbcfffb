// Lightweight env loader to support monorepo root .env.local during local dev.
// Next.js only auto-loads env files from the app directory (apps/web).
// If developers keep a single .env.local at the repo root, try to read it here
// and populate the public Supabase vars when missing.
import fs from 'node:fs'
import path from 'node:path'

const ensurePublicSupabaseEnv = () => {
  const hasUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL
  const hasAnon = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  if (hasUrl && hasAnon) return

  try {
    const rootEnv = path.resolve(__dirname, '../../.env.local')
    if (!fs.existsSync(rootEnv)) return
    const content = fs.readFileSync(rootEnv, 'utf8')
    for (const raw of content.split(/\r?\n/)) {
      const line = raw.trim()
      if (!line || line.startsWith('#')) continue
      const eq = line.indexOf('=')
      if (eq <= 0) continue
      const key = line.slice(0, eq).trim()
      let value = line.slice(eq + 1).trim()
      if ((value.startsWith('"') && value.endsWith('"')) || (value.startsWith("'") && value.endsWith("'"))) {
        value = value.slice(1, -1)
      }
      if (key === 'NEXT_PUBLIC_SUPABASE_URL' && !process.env.NEXT_PUBLIC_SUPABASE_URL) {
        process.env.NEXT_PUBLIC_SUPABASE_URL = value
      }
      if (key === 'NEXT_PUBLIC_SUPABASE_ANON_KEY' && !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = value
      }
    }
  } catch {
    // best-effort only; do not block build
  }
}

ensurePublicSupabaseEnv()

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['@belbooks/types', '@belbooks/dal'],
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  compiler: {
    styledComponents: true,
  },
  
  // Security Headers Configuration
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'off'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), interest-cohort=()'
          },
          // HSTS - only enable in production with HTTPS
          process.env.NODE_ENV === 'production' && {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          // Content Security Policy - Conditional based on environment
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production' 
              ? [
                  "default-src 'self'",
                  "script-src 'self' 'nonce-NONCE_PLACEHOLDER'", // Production: nonce-based, no unsafe directives
                  "style-src 'self' 'nonce-NONCE_PLACEHOLDER'", // Production: nonce-based styles
                  "img-src 'self' data: https:",
                  "font-src 'self'",
                  "connect-src 'self' https://*.supabase.co https://*.supabase.in",
                  "frame-ancestors 'none'",
                  "base-uri 'self'",
                  "form-action 'self'",
                  "manifest-src 'self'",
                  "object-src 'none'", // Block plugins
                  "media-src 'self'",
                  "worker-src 'self' blob:", // Service workers
                ].join('; ')
              : [
                  "default-src 'self'",
                  "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Dev: allow unsafe for hot reload
                  "style-src 'self' 'unsafe-inline'", // Dev: allow inline styles
                  "img-src 'self' data: https:",
                  "font-src 'self'",
                  "connect-src 'self' https://*.supabase.co https://*.supabase.in ws: wss:", // Dev: websockets for HMR
                  "frame-ancestors 'none'",
                  "base-uri 'self'",
                  "form-action 'self'",
                  "manifest-src 'self'",
                ].join('; ')
          }
        ].filter(Boolean)
      },
      {
        // More restrictive CSP for production API routes
        source: '/api/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'none'; frame-ancestors 'none';"
          }
        ]
      }
    ]
  },

  // Experimental security features
  experimental: {
    // Enable strict CSP in production
    strictNextHead: true,
  }
}

export default nextConfig
