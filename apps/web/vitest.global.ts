import { vi } from 'vitest'

// Define a global Jest-compatible API before any tests are loaded
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const jestLike: any = vi
jestLike.fn = vi.fn
jestLike.spyOn = vi.spyOn
jestLike.mock = vi.mock
jestLike.clearAllMocks = vi.clearAllMocks
jestLike.resetAllMocks = vi.resetAllMocks
jestLike.restoreAllMocks = vi.restoreAllMocks
jestLike.mocked = <T>(item: T) => item as unknown as T
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).jest = jestLike

