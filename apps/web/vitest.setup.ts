import '@testing-library/jest-dom'
import { vi, beforeEach, afterEach } from 'vitest'
import type * as AuthContextActual from '@/contexts/AuthContext'

// Jest compatibility shim for existing tests
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const jestLike: any = vi
jestLike.fn = vi.fn
jestLike.spyOn = vi.spyOn
jestLike.mock = vi.mock
jestLike.clearAllMocks = vi.clearAllMocks
jestLike.resetAllMocks = vi.resetAllMocks
jestLike.restoreAllMocks = vi.restoreAllMocks
jestLike.mocked = <T>(item: T) => item as unknown as T
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).jest = jestLike
// Minimal shim for jest.requireActual used by some tests
// Only supports modules we explicitly handle
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).jest.requireActual = (id: string) => {
  if (id === '@/contexts/AuthContext') {
    const React = require('react')
    const AuthProvider = ({ children }: { children: any }) => React.createElement(React.Fragment, null, children)
    return { AuthProvider }
  }
  return {}
}


// Make NODE_ENV writable in tests that redefine it
try {
  Object.defineProperty(process.env, 'NODE_ENV', {
    value: process.env.NODE_ENV ?? 'test',
    writable: true,
    configurable: true,
    enumerable: true,
  })
} catch {}

// Mock environment variables for tests
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key'
process.env.CSRF_SECRET = 'test-csrf-secret'
// Mock session security hooks to avoid circular dependency with AuthProvider using useAuth
vi.mock('@/hooks/useSessionSecurity', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@/hooks/useSessionSecurity')>()
  return {
    ...actual,
    useSessionSecurity: () => ({
      isActive: true,
      timeoutInfo: null,
      showWarning: false,
      lastActivity: new Date(),
      isOnline: true,
      extendSession: vi.fn(async () => {}),
      forceLogout: vi.fn(() => {}),
      updateActivity: vi.fn(() => {}),
    }),
    useSessionTimeoutDialog: () => ({
      showDialog: false,
      timeoutInfo: null,
      onTimeoutWarning: vi.fn(),
      extendSession: vi.fn(),
      logoutNow: vi.fn(),
    }),
  }
})
// Global mock for @ledgerly/dal so tests importing components before per-file mocks still see a mock
vi.mock('@ledgerly/dal', () => {
  return {
    signInWithEmail: vi.fn(),
    grantTenantRole: vi.fn(),
    grantEntityRole: vi.fn(),
    createInvite: vi.fn(),
    acceptInvite: vi.fn(),
    getUserRoles: vi.fn(),
  }
})

// Mock @/lib/csrf module
vi.mock('@/lib/csrf', () => ({
  validateCSRFMiddleware: vi.fn().mockResolvedValue(true), // Returns boolean
  validateCSRFFromRequest: vi.fn().mockResolvedValue(true), // Returns boolean
  validateCSRFToken: vi.fn().mockResolvedValue(true),
  getCSRFTokenFromRequest: vi.fn().mockReturnValue('test-csrf-token'),
  generateCSRFToken: vi.fn().mockReturnValue('test-csrf-token'),
  addCSRFToResponse: vi.fn(),
  createCSRFErrorResponse: vi.fn(),
}))

// Mock @/lib/security-monitoring module
vi.mock('@/lib/security-monitoring', () => ({
  logSecurityEvent: vi.fn().mockResolvedValue(undefined),
  SecurityEvents: {
    SESSION_TIMEOUT: 'session_timeout',
    FAILED_AUTH: 'failed_auth',
    SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_REQUEST',
    SESSION_EXTENDED: 'session_extended',
    ADMIN_ACTION: 'ADMIN_ACTION',
    CSRF_VIOLATION: 'CSRF_VIOLATION',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    failedLogin: vi.fn(),
    successfulLogin: vi.fn(),
    csrfViolation: vi.fn(),
    rateLimitExceeded: vi.fn(),
    adminAction: vi.fn(),
    dataExport: vi.fn(),
    suspiciousRequest: vi.fn(),
  },
  extractRequestInfo: vi.fn().mockReturnValue({
    ip_address: '127.0.0.1',
    user_agent: 'test-agent'
  }),
  SecurityAnalyzer: {
    getFailedLoginsByIP: vi.fn(),
    getRateLimitViolationsByIP: vi.fn(),
    shouldBlockIP: vi.fn(),
    getHighSeverityEvents: vi.fn(),
    getSuspiciousIPs: vi.fn(),
    getEventStats: vi.fn(),
  },
}))

// Mock @/lib/rate-limit module
vi.mock('@/lib/rate-limit', () => ({
  getClientIP: vi.fn().mockReturnValue('127.0.0.1'),
  checkRateLimit: vi.fn().mockResolvedValue({ success: true }),
  authRateLimiter: {
    limit: vi.fn().mockResolvedValue({ success: true }),
  },
  logRateLimitViolation: vi.fn(),
}))


// Global mock for useCSRF so pages render CSRF input in tests
vi.mock('@/hooks/useCSRF', () => ({
  useCSRF: () => ({
    createCSRFInput: () => {
      const input = globalThis.document?.createElement?.('input')
      if (input) {
        input.type = 'hidden'
        input.name = '_csrf_token'
        input.value = 'test-token'
      }
      return input
    },
    csrfToken: 'test-token',
  }),
}))


// Base mock for AuthContext to avoid provider errors in tests that import hooks without wrapping
vi.mock('@/contexts/AuthContext', async (importOriginal) => {
  const React = await import('react')
  // Re-export the real module to preserve actual exports like AuthProvider
  const actual = (await importOriginal()) as typeof AuthContextActual
  return {
    ...actual,
    // provide a default lightweight AuthProvider if tests render without real tree
    AuthProvider: ({ children }: { children: any }) => React.createElement(React.Fragment, null, children),
    useAuth: () => ({
      user: null,
      session: null,
      loading: false,
      signOut: vi.fn(async () => {}),
      sessionSecurity: {
        isActive: false,
        timeoutInfo: null,
        showWarning: false,
        lastActivity: null,
        isOnline: true,
        extendSession: vi.fn(async () => {}),
        forceLogout: vi.fn(() => {}),
        updateActivity: vi.fn(() => {}),
      },
    }),
  }
})

// Minimal Next.js server/runtime mocks as defaults (tests can override per-suite)
vi.mock('next/server', () => {
  class MockNextRequest {
    url: string
    method: string
    headers: Headers
    nextUrl: { pathname: string }
    cookies: {
      get: (name: string) => { value?: string }
      set: (name: string, value: string) => void
      delete: (name: string) => void
    }
    ip?: string

    constructor(url: string, init?: { method?: string; headers?: Record<string, string> | Headers }) {
      this.url = url
      this.method = init?.method ?? 'GET'
      this.headers = init?.headers instanceof Headers ? init.headers : new Headers(init?.headers)
      this.nextUrl = { pathname: new URL(url).pathname }

      const cookieMap = new Map<string, string>()
      const cookieHeader = this.headers.get('cookie')
      if (cookieHeader) {
        cookieHeader.split(';').forEach((pair) => {
          const [k, v] = pair.split('=')
          if (k && v !== undefined) cookieMap.set(k.trim(), v.trim())
        })
      }
      this.cookies = {
        get: (name: string) => (cookieMap.has(name) ? { value: cookieMap.get(name)! } : undefined as any),
        set: (name: string, value: string) => {
          cookieMap.set(name, value)
          this.headers.set('cookie', Array.from(cookieMap.entries()).map(([k, v]) => `${k}=${v}`).join('; '))
        },
        delete: (name: string) => {
          cookieMap.delete(name)
          this.headers.set('cookie', Array.from(cookieMap.entries()).map(([k, v]) => `${k}=${v}`).join('; '))
        },
      }
    }
  }

  class MockNextResponse {
    headers: Headers
    status: number
    body?: any

    constructor(body?: any, init?: { status?: number; headers?: Record<string, string> | Headers }) {
      this.body = body
      this.status = init?.status ?? 200
      this.headers = init?.headers instanceof Headers ? init.headers : new Headers(init?.headers)
    }

    static next(init?: { request?: { headers?: Headers } }) {
      return new MockNextResponse(undefined, { status: 200, headers: init?.request?.headers })
    }

    static json(body: any, init?: { status?: number; headers?: Record<string, string> }) {
      return new MockNextResponse(JSON.stringify(body), {
        status: init?.status ?? 200,
        headers: { 'content-type': 'application/json', ...(init?.headers || {}) },
      })
    }

    static redirect(url: string | URL, init?: { status?: number }) {
      const res = new MockNextResponse(undefined, { status: init?.status ?? 302 })
      res.headers.set('location', String(url))
      return res
    }
  }

  return { NextRequest: MockNextRequest, NextResponse: MockNextResponse }
})

vi.mock('next/headers', () => {
  const headers = () => new Map<string, string>()
  const cookies = vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
  }))
  return { headers, cookies }
})

// Mock Upstash Redis for rate limiting tests
vi.mock('@upstash/redis', () => ({
  Redis: vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
    incr: vi.fn(),
    expire: vi.fn(),
  })),
}))

vi.mock('@upstash/ratelimit', () => ({
  Ratelimit: vi.fn().mockImplementation(() => ({
    limit: vi.fn().mockResolvedValue({
      success: true,
      limit: 10,
      remaining: 9,
      reset: new Date(Date.now() + 60000),
    }),
  })),
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
  usePathname: () => '/',
}))

// Global test utilities
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).fetch = vi.fn()

// Polyfill Web APIs for Next.js middleware tests
const { TextEncoder, TextDecoder } = require('util')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).TextEncoder = TextEncoder
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(globalThis as any).TextDecoder = TextDecoder

beforeEach(() => {
  vi.clearAllMocks()
})

afterEach(() => {
  vi.restoreAllMocks()
})

