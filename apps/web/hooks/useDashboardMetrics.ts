import { useState, useEffect, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { useAuth } from '@/contexts/AuthContext'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import type { Database } from '@ledgerly/types'

export interface DashboardMetrics {
  vatPosition: {
    amount: number
    currency: string
    dueDate: string | null
    type: 'due' | 'refund'
  } | null
  bankBalance: {
    total: number
    currency: string
    lastUpdated: string
  } | null
  openInvoices: {
    count: number
    totalAmount: number
    currency: string
  } | null
  inboxToReview: {
    count: number
  } | null
  reconciliationTasks: {
    count: number
  } | null
}

export interface DashboardMetricsHook {
  metrics: DashboardMetrics
  loading: boolean
  error: string | null
  refreshMetrics: () => Promise<void>
}

export const useDashboardMetrics = (): DashboardMetricsHook => {
  const { user } = useAuth()
  const { currentEntity, isValid } = useOrgEntitySelection()
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    vatPosition: null,
    bankBalance: null,
    openInvoices: null,
    inboxToReview: null,
    reconciliationTasks: null,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchMetrics = useCallback(async () => {
    if (!user || !isValid || !currentEntity) {
      setMetrics({
        vatPosition: null,
        bankBalance: null,
        openInvoices: null,
        inboxToReview: null,
        reconciliationTasks: null,
      })
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const supabase = createClientSupabaseClient()
      const entityId = currentEntity.entity_id

      // Ensure entityId is valid
      if (!entityId) {
        setMetrics({
          vatPosition: null,
          bankBalance: null,
          openInvoices: null,
          inboxToReview: null,
          reconciliationTasks: null,
        })
        setLoading(false)
        return
      }

      // Fetch all metrics in parallel
      const [
        bankBalanceResult,
        openInvoicesResult,
        inboxResult,
        reconciliationResult,
      ] = await Promise.allSettled([
        // Bank Balance - sum of all bank transactions for active accounts
        supabase
          .from('bank_transactions')
          .select(
            `
            amount,
            bank_accounts!inner(entity_id, is_active)
          `
          )
          .eq('bank_accounts.entity_id', entityId)
          .eq('bank_accounts.is_active', true),

        // Open Invoices - count and sum of unpaid invoices
        supabase
          .from('invoices')
          .select('total_amount, status')
          .eq('entity_id', entityId)
          .in('status', ['draft', 'sent', 'overdue']),

        // Inbox Documents - count of unprocessed documents
        supabase
          .from('inbox_documents')
          .select('id', { count: 'exact' })
          .eq('entity_id', entityId)
          .eq('status', 'uploaded'),

        // Reconciliation Tasks - count of unreconciled bank transactions
        supabase
          .from('bank_transactions')
          .select('id', { count: 'exact' })
          .eq('bank_account_id', entityId) // This needs proper join
          .eq('is_reconciled', false),
      ])

      // Process Bank Balance
      let bankBalance = null
      if (
        bankBalanceResult.status === 'fulfilled' &&
        bankBalanceResult.value.data
      ) {
        const transactions = bankBalanceResult.value.data
        const total = transactions.reduce(
          (sum, transaction) => sum + (transaction.amount || 0),
          0
        )
        bankBalance = {
          total,
          currency: 'EUR', // Default currency, could be enhanced to get from entity
          lastUpdated: new Date().toISOString(),
        }
      }

      // Process Open Invoices
      let openInvoices = null
      if (
        openInvoicesResult.status === 'fulfilled' &&
        openInvoicesResult.value.data
      ) {
        const invoices = openInvoicesResult.value.data
        openInvoices = {
          count: invoices.length,
          totalAmount: invoices.reduce((sum, inv) => sum + inv.total_amount, 0),
          currency: 'EUR',
        }
      }

      // Process Inbox Count
      let inboxToReview = null
      if (inboxResult.status === 'fulfilled') {
        inboxToReview = {
          count: inboxResult.value.count || 0,
        }
      }

      // Process Reconciliation Count
      let reconciliationTasks = null
      if (reconciliationResult.status === 'fulfilled') {
        reconciliationTasks = {
          count: reconciliationResult.value.count || 0,
        }
      }

      // VAT Position - would need custom RPC or complex calculation
      // For now, set to null and implement separately
      const vatPosition = null

      setMetrics({
        vatPosition,
        bankBalance,
        openInvoices,
        inboxToReview,
        reconciliationTasks,
      })
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to load dashboard metrics'
      )
      console.error('Dashboard metrics error:', err)
    } finally {
      setLoading(false)
    }
  }, [user, isValid, currentEntity])

  useEffect(() => {
    void fetchMetrics()
  }, [fetchMetrics])

  const refreshMetrics = async () => {
    await fetchMetrics()
  }

  return {
    metrics,
    loading,
    error,
    refreshMetrics,
  }
}
