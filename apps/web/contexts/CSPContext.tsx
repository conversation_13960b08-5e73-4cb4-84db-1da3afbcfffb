'use client'

import React, { createContext, useContext, ReactNode } from 'react'

interface CSPContextType {
  nonce?: string | null
  createNonceProps: () => Record<string, string>
}

const CSPContext = createContext<CSPContextType>({
  nonce: null,
  createNonceProps: () => ({}),
})

interface CSPProviderProps {
  children: ReactNode
  nonce?: string | null
}

export function CSPProvider({ children, nonce }: CSPProviderProps) {
  const createNonceProps = (): Record<string, string> => {
    if (!nonce || process.env.NODE_ENV !== 'production') {
      return {}
    }

    return { nonce }
  }

  const value: CSPContextType = {
    nonce,
    createNonceProps,
  }

  return <CSPContext.Provider value={value}>{children}</CSPContext.Provider>
}

/**
 * Hook to get CSP nonce for use in components
 */
export function useCSPNonce() {
  const context = useContext(CSPContext)
  if (!context) {
    throw new Error('useCSPNonce must be used within a CSPProvider')
  }
  return context
}

/**
 * Higher-order component that adds nonce props to script/style elements
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function withCSPNonce<T extends Record<string, any>>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function CSPNonceWrapper(props: T) {
    const { createNonceProps } = useCSPNonce()

    return <Component {...props} {...createNonceProps()} />
  }
}
