/**
 * Integration Tests for User Context Propagation
 * Tests that user JWTs are properly propagated from web app to BFF and enforced by RLS
 */

/**
 * @vitest-environment node
 */

import { createClient } from '@supabase/supabase-js'

describe('User Context Propagation Integration', () => {
  let supabaseClient: any
  let mockFetch: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock Supabase client
    supabaseClient = {
      auth: {
        getSession: vi.fn(),
        getUser: vi.fn(),
      },
      from: vi.fn(),
      rpc: vi.fn()
    }

    // Mock fetch for BFF requests
    mockFetch = vi.fn()
    global.fetch = mockFetch as unknown as typeof fetch
  })

  describe('BFF User Context Creation', () => {
    it('should create user-scoped Supabase client when JWT token is provided', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test'
      
      // Mock BFF client utility
      const bffClient = {
        getUserToken: vi.fn().mockResolvedValue(mockToken),
        makeRequest: async function(endpoint: string, options: any, userToken?: string) {
          // Simulate BFF request with proper headers
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            'X-Internal-Key': 'test-key'
          }
          
          if (userToken) {
            headers['Authorization'] = `Bearer ${userToken}`
          }
          
          expect(userToken).toBe(mockToken)
          expect(headers['Authorization']).toBe(`Bearer ${mockToken}`)
          
          return { success: true, data: [] }
        }
      }

      const result = await bffClient.makeRequest('/entities/1/accounts', {}, mockToken)
      
      expect(result.success).toBe(true)
    })
  })

  describe('BFF Authentication Middleware', () => {
    it('should extract and validate user JWT from Authorization header', () => {
      const mockRequest = {
        headers: {
          'x-internal-key': 'test-internal-key',
          'authorization': 'Bearer mock-jwt-token'
        }
      }

      // Simulate BFF auth plugin logic
      const internalKey = mockRequest.headers['x-internal-key']
      const authHeader = mockRequest.headers['authorization']
      
      expect(internalKey).toBe('test-internal-key')
      expect(authHeader?.startsWith('Bearer ')).toBe(true)
      
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        expect(token).toBe('mock-jwt-token')
      }
    })

    it('should reject requests without internal key', () => {
      const mockRequest = {
        headers: {
          'authorization': 'Bearer mock-jwt-token'
          // Missing x-internal-key
        } as Record<string, string>
      }

      const internalKey = mockRequest.headers['x-internal-key']
      expect(internalKey).toBeUndefined()
      
      // Should be rejected by auth middleware
    })

    it('should handle requests without user JWT (service-to-service)', () => {
      const mockRequest = {
        headers: {
          'x-internal-key': 'test-internal-key'
          // No Authorization header - service-to-service request
        } as Record<string, string>
      }

      const internalKey = mockRequest.headers['x-internal-key']
      const authHeader = mockRequest.headers['authorization']
      
      expect(internalKey).toBe('test-internal-key')
      expect(authHeader).toBeUndefined()
      
      // Should use service role client
    })
  })

  describe('BFF Route Handler Client Selection', () => {
    it('should use user client when userSupabase is available', () => {
      const mockUserSupabase = { from: vi.fn(), rpc: vi.fn() }
      const mockServiceSupabase = { from: vi.fn(), rpc: vi.fn() }

      // Simulate route handler logic
      const mockRequest = { userSupabase: mockUserSupabase }
      const mockFastify = { supabase: mockServiceSupabase }
      
      const supabaseClient = mockRequest.userSupabase || mockFastify.supabase
      
      expect(supabaseClient).toBe(mockUserSupabase)
      expect(supabaseClient).not.toBe(mockServiceSupabase)
    })

    it('should fallback to service client when userSupabase is not available', () => {
      const mockServiceSupabase = { from: vi.fn(), rpc: vi.fn() }

      // Simulate route handler logic
      const mockRequest = { userSupabase: undefined }
      const mockFastify = { supabase: mockServiceSupabase }
      
      const supabaseClient = mockRequest.userSupabase || mockFastify.supabase
      
      expect(supabaseClient).toBe(mockServiceSupabase)
    })
  })

  describe('RLS Enforcement with User Context', () => {
    it('should enforce RLS when using user-scoped client', async () => {
      // Mock user client that enforces RLS
      const mockUserClient = {
        from: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [{ id: 1, entity_id: 1, name: 'User Account' }],
              error: null
            })
          })
        })
      }

      // Simulate query through user client (with RLS)
      const { data, error } = await mockUserClient
        .from('accounts')
        .select('*')
        .eq('entity_id', 1)

      expect(data).toHaveLength(1)
      expect(data[0].name).toBe('User Account')
      expect(error).toBeNull()
    })

    it('should bypass RLS when using service client', async () => {
      // Mock service client that bypasses RLS
      const mockServiceClient = {
        from: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [
                { id: 1, entity_id: 1, name: 'User Account' },
                { id: 2, entity_id: 2, name: 'Other Account' }
              ],
              error: null
            })
          })
        })
      }

      // Simulate query through service client (bypasses RLS)
      const { data, error } = await mockServiceClient
        .from('accounts')
        .select('*')
        .eq('entity_id', 1)

      expect(data).toHaveLength(2) // Service client sees all data
      expect(error).toBeNull()
    })
  })

  describe('SECURITY DEFINER Functions with User Context', () => {
    it('should validate user permissions in RPC functions', async () => {
      const mockUserClient = {
        rpc: vi.fn().mockResolvedValue({
          data: 123,
          error: null
        })
      }

      // Simulate RPC call through user client
      const { data, error } = await mockUserClient.rpc('rpc_post_journal', {
        p_entity: 1,
        p_type: 'manual',
        p_description: 'Test journal',
        p_date: '2024-01-01',
        p_lines: [
          { account_id: 1, debit_amount: 100, credit_amount: 0 },
          { account_id: 2, debit_amount: 0, credit_amount: 100 }
        ]
      })

      expect(mockUserClient.rpc).toHaveBeenCalledWith('rpc_post_journal', expect.any(Object))
      expect(data).toBe(123) // Journal ID returned
      expect(error).toBeNull()
    })

    it('should reject RPC calls for unauthorized entities', async () => {
      const mockUserClient = {
        rpc: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Access denied to entity 999' }
        })
      }

      // Simulate RPC call to entity user doesn't have access to
      const { data, error } = await mockUserClient.rpc('rpc_post_journal', {
        p_entity: 999, // User doesn't have access
        p_type: 'manual',
        p_description: 'Test journal',
        p_date: '2024-01-01',
        p_lines: []
      })

      expect(data).toBeNull()
      expect(error?.message).toBe('Access denied to entity 999')
    })
  })

  describe('End-to-End User Context Flow', () => {
    it('should complete full authentication flow from web app to database', async () => {
      // 1. Web app gets user session
      const mockSession = {
        access_token: 'mock-jwt-token',
        user: { id: 'user-123' }
      }
      
      ;(supabaseClient.auth.getSession as ReturnType<typeof vi.fn>).mockResolvedValue({
        data: { session: mockSession },
        error: null
      })

      // 2. BFF receives request with JWT
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: [{ id: 1, name: 'Test Account' }]
        })
      })

      // 3. Simulate full flow
      const sessionResult = await supabaseClient.auth.getSession()
      const userToken = sessionResult.data.session?.access_token

      const response = await fetch('http://localhost:4000/entities/1/accounts', {
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': 'test-key',
          'Authorization': `Bearer ${userToken}`
        }
      })

      const result = await response.json()

      expect(userToken).toBe('mock-jwt-token')
      expect(response.ok).toBe(true)
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(1)
    })
  })
})

// Mock environment variables for tests
process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'