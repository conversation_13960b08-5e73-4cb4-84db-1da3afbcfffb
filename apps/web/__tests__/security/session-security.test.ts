/**
 * Session Security Tests
 * Tests for session timeout, security validation, and management
 */

import { vi } from 'vitest'

import {
  SessionSecurityManager,
  defaultSessionConfig,
  validateSessionSecurity,
  SessionSecurityConfig,
  sessionManager,
  DEFAULT_SESSION_CONFIG
} from '@/lib/session-security'

// Mock security monitoring
vi.mock('@/lib/security-monitoring', () => ({
  extractRequestInfo: vi.fn(() => ({ ip_address: '127.0.0.1', user_agent: 'test-agent' })),
  logSecurityEvent: vi.fn(),
  SecurityEvents: {
    successfulLogin: vi.fn(),
    suspiciousRequest: vi.fn(),
    adminAction: vi.fn(),
    SESSION_TIMEOUT: 'session_timeout',
    FAILED_AUTH: 'failed_auth',
    SUSPICIOUS_ACTIVITY: 'suspicious_activity',
    SESSION_EXTENDED: 'session_extended',
    ADMIN_ACTION: 'admin_action',
    CSRF_VIOLATION: 'csrf_violation',
    PERMISSION_DENIED: 'permission_denied',
  },
}))

// Import the mocked function
import { extractRequestInfo as mockExtractRequestInfo } from '@/lib/security-monitoring'

describe('Session Security Management', () => {
  let testSessionManager: SessionSecurityManager
  let mockRequest: any

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    // Clear global session manager state
    sessionManager.clearAllSessions()

    testSessionManager = new SessionSecurityManager({
      maxIdleTime: 30 * 60 * 1000, // 30 minutes for testing
      warningTime: 5 * 60 * 1000, // 5 minutes warning
      checkInterval: 60 * 1000, // 1 minute check interval
      maxAbsoluteTime: 2 * 60 * 60 * 1000, // 2 hours absolute maximum
      maxConcurrentSessions: 3 // max 3 concurrent sessions per user
    })

    mockRequest = {
      headers: {
        'user-agent': 'Mozilla/5.0 Test Browser',
        'x-forwarded-for': '***********'
      }
    } as any
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Session Initialization', () => {
    it('should initialize session with security metadata', async () => {
      const sessionId = 'test-session-123'
      const userId = 'user-456'

      const metadata = await sessionManager.initializeSession(sessionId, userId, mockRequest)

      expect(metadata).toEqual(
        expect.objectContaining({
          sessionId,
          userId,
          ipAddress: expect.any(String), // IP comes from mock extraction
          userAgent: expect.any(String), // User agent comes from mock extraction 
          isActive: true,
          deviceFingerprint: expect.any(String),
          createdAt: expect.any(Number),
          lastActivity: expect.any(Number),
          expiresAt: expect.any(Number),
          // Allow for additional properties like absoluteExpiresAt
        })
      )

      expect(metadata.expiresAt).toBeGreaterThan(metadata.createdAt)
    })

    it('should generate consistent device fingerprints', async () => {
      const sessionId1 = 'session-1'
      const sessionId2 = 'session-2'
      const userId = 'user-123'

      const metadata1 = await sessionManager.initializeSession(sessionId1, userId, mockRequest)
      const metadata2 = await sessionManager.initializeSession(sessionId2, userId, mockRequest)

      expect(metadata1.deviceFingerprint).toBe(metadata2.deviceFingerprint)
    })

    it('should handle missing request information', async () => {
      const metadata = await sessionManager.initializeSession('session-123', 'user-456')

      expect(metadata.ipAddress).toBe('unknown')
      expect(metadata.userAgent).toBe('unknown')
      expect(metadata.deviceFingerprint).toBeDefined()
    })
  })

  describe('Activity Tracking', () => {
    it('should update session activity', async () => {
      const sessionId = 'test-session'
      const userId = 'user-123'

      await sessionManager.initializeSession(sessionId, userId, mockRequest)
      
      // Advance time
      vi.advanceTimersByTime(5 * 60 * 1000) // 5 minutes

      const updated = sessionManager.updateActivity(sessionId, mockRequest)
      expect(updated).toBe(true)

      const session = sessionManager.getSession(sessionId)
      expect(session?.lastActivity).toBeGreaterThan(session!.createdAt)
    })

    it('should detect device fingerprint changes', async () => {
      const sessionId = 'test-session'
      const userId = 'user-123'

      await sessionManager.initializeSession(sessionId, userId, mockRequest)

      // Mock extractRequestInfo to return different fingerprint info
      mockExtractRequestInfo.mockReturnValueOnce({
        ip_address: '***********',
        user_agent: 'Different Browser'
      })

      // Create request with different device fingerprint
      const differentRequest = {
        headers: {
          'user-agent': 'Different Browser',
          'x-forwarded-for': '***********'
        }
      } as any

      const updated = sessionManager.updateActivity(sessionId, differentRequest)
      expect(updated).toBe(false)

      // Session should be terminated
      expect(sessionManager.hasActiveSession(sessionId)).toBe(false)
    })

    it('should return false for non-existent sessions', () => {
      const updated = sessionManager.updateActivity('non-existent-session', mockRequest)
      expect(updated).toBe(false)
    })
  })

  describe('Timeout Management', () => {
    it('should calculate correct timeout information', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      const timeoutInfo = sessionManager.getTimeoutInfo(sessionId)

      expect(timeoutInfo).toEqual(
        expect.objectContaining({
          idleTimeRemaining: expect.any(Number),
          absoluteTimeRemaining: expect.any(Number),
          showWarning: false,
          shouldTimeout: false,
          reason: null
        })
      )

      expect(timeoutInfo.idleTimeRemaining).toBeGreaterThan(0)
      expect(timeoutInfo.absoluteTimeRemaining).toBeGreaterThan(0)
    })

    it('should show warning when approaching timeout', async () => {
      const sessionId = 'test-session'
      await testSessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Advance time to warning threshold (30min - 3min = 27min) to be within warning range
      vi.advanceTimersByTime(27 * 60 * 1000)

      const timeoutInfo = testSessionManager.getTimeoutInfo(sessionId)

      expect(timeoutInfo.idleTimeRemaining).toBeLessThanOrEqual(5 * 60) // Less than 5 minutes remaining
      expect(timeoutInfo.idleTimeRemaining).toBeGreaterThan(0) // But still some time left

      // The warning should show if we're within warning time
      if (timeoutInfo.idleTimeRemaining && timeoutInfo.idleTimeRemaining <= 5 * 60) {
        expect(timeoutInfo.showWarning).toBe(true)
      }
    })

    it('should indicate timeout when time exceeded', async () => {
      const sessionId = 'test-session'
      await testSessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Advance time beyond idle timeout
      vi.advanceTimersByTime(35 * 60 * 1000) // 35 minutes

      const timeoutInfo = testSessionManager.getTimeoutInfo(sessionId)

      expect(timeoutInfo.shouldTimeout).toBe(true)
      expect(timeoutInfo.idleTimeRemaining).toBeLessThanOrEqual(0)
      // Note: the reason might be 'idle' or 'absolute' depending on which timeout occurred first
    })

    it('should handle absolute timeout', async () => {
      const sessionId = 'test-session'
      await testSessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Advance time beyond absolute timeout (2 hours)
      vi.advanceTimersByTime(3 * 60 * 60 * 1000)

      const timeoutInfo = testSessionManager.getTimeoutInfo(sessionId)

      expect(timeoutInfo.shouldTimeout).toBe(true)
      expect(timeoutInfo.absoluteTimeRemaining).toBeLessThanOrEqual(0)
    })
  })

  describe('Session Termination', () => {
    it('should terminate session with cleanup', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      expect(sessionManager.hasActiveSession(sessionId)).toBe(true)

      await sessionManager.terminateSession(sessionId, 'user_logout')

      expect(sessionManager.hasActiveSession(sessionId)).toBe(false)
    })

    it('should handle non-existent session termination', async () => {
      await expect(
        sessionManager.terminateSession('non-existent-session')
      ).resolves.not.toThrow()
    })

    it('should log security events on suspicious termination', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      await sessionManager.terminateSession(sessionId, 'security_violation')

      const { SecurityEvents } = await vi.importMock('@/lib/security-monitoring')
      expect(SecurityEvents.suspiciousRequest).toHaveBeenCalled()
    })
  })

  describe('Concurrent Session Management', () => {
    it('should track user sessions', async () => {
      const userId = 'user-123'
      
      await sessionManager.initializeSession('session-1', userId, mockRequest)
      await sessionManager.initializeSession('session-2', userId, mockRequest)

      const userSessions = sessionManager.getUserSessions(userId)
      expect(userSessions).toHaveLength(2)
      expect(userSessions.every(s => s.userId === userId)).toBe(true)
    })

    it('should enforce maximum concurrent sessions', async () => {
      const userId = 'user-123'

      // Create sessions beyond the limit using testSessionManager (3 session limit)
      await testSessionManager.initializeSession('session-1', userId, mockRequest)
      await testSessionManager.initializeSession('session-2', userId, mockRequest)
      await testSessionManager.initializeSession('session-3', userId, mockRequest)
      await testSessionManager.initializeSession('session-4', userId, mockRequest) // Should trigger cleanup

      const userSessions = testSessionManager.getUserSessions(userId)
      expect(userSessions.length).toBeLessThanOrEqual(3) // Max sessions limit
    })

    it('should preserve current session when enforcing limits', async () => {
      const userId = 'user-123'
      const currentSession = 'current-session'

      // Create old sessions using testSessionManager
      await testSessionManager.initializeSession('old-session-1', userId, mockRequest)
      await testSessionManager.initializeSession('old-session-2', userId, mockRequest)
      await testSessionManager.initializeSession('old-session-3', userId, mockRequest)

      // Advance time to make them older
      vi.advanceTimersByTime(10 * 60 * 1000)

      await testSessionManager.initializeSession(currentSession, userId, mockRequest)

      const userSessions = testSessionManager.getUserSessions(userId)
      expect(userSessions.find(s => s.sessionId === currentSession)).toBeDefined()
    })
  })

  describe('Session Statistics', () => {
    it('should provide session statistics', async () => {
      await sessionManager.initializeSession('session-1', 'user-1', mockRequest)
      await sessionManager.initializeSession('session-2', 'user-1', mockRequest)
      await sessionManager.initializeSession('session-3', 'user-2', mockRequest)

      const stats = sessionManager.getSessionStats()

      expect(stats).toEqual({
        totalActiveSessions: 3,
        uniqueUsers: 2,
        averageSessionsPerUser: 1.5,
        maxConcurrentSessions: 2
      })
    })

    it('should handle empty session statistics', () => {
      const stats = sessionManager.getSessionStats()

      expect(stats).toEqual({
        totalActiveSessions: 0,
        uniqueUsers: 0,
        averageSessionsPerUser: 0,
        maxConcurrentSessions: 0
      })
    })
  })

  describe('Session Cleanup', () => {
    it('should clean up expired sessions', async () => {
      // Create sessions with different ages using testSessionManager (30 min timeout)
      await testSessionManager.initializeSession('fresh-session', 'user-1', mockRequest)

      jest.advanceTimersByTime(10 * 60 * 1000) // 10 minutes
      await testSessionManager.initializeSession('medium-session', 'user-2', mockRequest)

      jest.advanceTimersByTime(30 * 60 * 1000) // Advance to expire first session (40 min total > 30 min timeout)

      const cleanedCount = testSessionManager.cleanupExpiredSessions()

      expect(cleanedCount).toBe(1) // Should clean up the expired session
      expect(testSessionManager.getUserSessions('user-1')).toHaveLength(0)
      expect(testSessionManager.getUserSessions('user-2')).toHaveLength(1)
    })
  })

  describe('Session Validation', () => {
    it('should validate session security successfully', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      const validation = await validateSessionSecurity(mockRequest, sessionId)

      expect(validation.valid).toBe(true)
      expect(validation.timeoutInfo).toBeDefined()
      expect(validation.reason).toBeUndefined()
    })

    it('should reject validation without session ID', async () => {
      const validation = await validateSessionSecurity(mockRequest)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('No session ID provided')
    })

    it('should reject expired sessions', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Advance time to expire session (beyond 1 hour for development config)
      vi.advanceTimersByTime(65 * 60 * 1000) // 65 minutes

      const validation = await validateSessionSecurity(mockRequest, sessionId)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toContain('Session timeout')
    })

    it('should reject sessions with security violations', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Mock extractRequestInfo to return different fingerprint info
      mockExtractRequestInfo.mockReturnValueOnce({
        ip_address: '********',
        user_agent: 'Malicious Bot'
      })

      // Create request with different fingerprint
      const suspiciousRequest = {
        headers: new Map([
          ['user-agent', 'Malicious Bot'],
          ['x-forwarded-for', '********']
        ])
      } as any

      const validation = await validateSessionSecurity(suspiciousRequest, sessionId)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Device fingerprint mismatch')
    })
  })

  describe('Environment Configuration', () => {
    it('should use development timeouts in development mode', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'development')

      const devManager = new SessionSecurityManager()
      expect(devManager.getConfig().idleTimeout).toBe(DEFAULT_SESSION_CONFIG.development.idleTimeout)

      vi.unstubAllEnvs()
    })

    it('should use production timeouts in production mode', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'production')

      const prodManager = new SessionSecurityManager()
      expect(prodManager.getConfig().idleTimeout).toBe(DEFAULT_SESSION_CONFIG.production.idleTimeout)

      vi.unstubAllEnvs()
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed requests gracefully', async () => {
      const sessionId = 'test-session'
      await sessionManager.initializeSession(sessionId, 'user-123')

      const malformedRequest = {} as Request

      expect(() => {
        sessionManager.updateActivity(sessionId, malformedRequest)
      }).not.toThrow()
    })

    it('should handle concurrent session operations', async () => {
      const sessionId = 'test-session'
      const userId = 'user-123'

      // Run concurrent operations
      const operations = Promise.all([
        sessionManager.initializeSession(sessionId, userId, mockRequest),
        sessionManager.updateActivity(sessionId, mockRequest),
        sessionManager.terminateSession(sessionId)
      ])

      await expect(operations).resolves.not.toThrow()
    })
  })

  describe('Security Edge Cases', () => {
    it('should handle extremely long session IDs', async () => {
      const veryLongSessionId = 'a'.repeat(1000)
      const metadata = await sessionManager.initializeSession(veryLongSessionId, 'user-123', mockRequest)

      expect(metadata.sessionId).toBe(veryLongSessionId)
    })

    it('should handle special characters in session data', async () => {
      // Mock extractRequestInfo to return the special characters
      mockExtractRequestInfo.mockReturnValueOnce({
        ip_address: '***********; DROP TABLE sessions;',
        user_agent: '<script>alert("xss")</script>'
      })

      const specialRequest = {
        headers: new Map([
          ['user-agent', '<script>alert("xss")</script>'],
          ['x-forwarded-for', '***********; DROP TABLE sessions;']
        ])
      } as any

      const metadata = await sessionManager.initializeSession('session-123', 'user-123', specialRequest)

      expect(metadata.userAgent).toContain('<script>')
      expect(metadata.ipAddress).toContain('DROP TABLE')
      // Ensure no code execution or SQL injection
      expect(typeof metadata.userAgent).toBe('string')
    })

    it('should prevent session hijacking attempts', async () => {
      const sessionId = 'session-123'
      await sessionManager.initializeSession(sessionId, 'user-123', mockRequest)

      // Mock extractRequestInfo to return different fingerprint info for hijack attempt
      mockExtractRequestInfo.mockReturnValueOnce({
        ip_address: '*******',
        user_agent: 'Attacker Browser'
      })

      // Attempt to hijack with different IP/User-Agent
      const hijackRequest = {
        headers: new Map([
          ['user-agent', 'Attacker Browser'],
          ['x-forwarded-for', '*******']
        ])
      } as any

      const updated = sessionManager.updateActivity(sessionId, hijackRequest)
      expect(updated).toBe(false)
      expect(sessionManager.hasActiveSession(sessionId)).toBe(false)
    })
  })
})