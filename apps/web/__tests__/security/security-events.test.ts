/**
 * Security Events Persistence Tests
 * Tests for storing and querying security events in database
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any, security/detect-object-injection */

import { vi } from 'vitest'

// Unmock security monitoring to test actual implementation
vi.unmock('@/lib/security-monitoring')

import { logSecurityEvent, SecurityAnalyzer, SecurityEvents } from '@/lib/security-monitoring'
import type { SecurityEvent } from '@ledgerly/dal'


// Mock the DAL module
vi.mock('@ledgerly/dal', () => ({
  // Auth functions
  signInWithEmail: vi.fn(),
  signOut: vi.fn(),
  getSession: vi.fn(),
  getUser: vi.fn(),
  onAuthStateChange: vi.fn(),
  refreshSession: vi.fn(),

  // Tenant management
  listUserTenants: vi.fn(),
  getTenant: vi.fn(),
  createTenant: vi.fn(),
  createTenantRPC: vi.fn(),
  hasTenanRole: vi.fn(),
  grantTenantRole: vi.fn(),

  // Entity management
  listUserEntities: vi.fn(),
  getEntity: vi.fn(),
  createEntity: vi.fn(),
  hasEntityRole: vi.fn(),
  grantEntityRole: vi.fn(),
  listEntityMemberships: vi.fn(),

  // Invitation management
  createInvite: vi.fn(),
  acceptInvite: vi.fn(),
  listSentInvites: vi.fn(),
  getPendingInvite: vi.fn(),
  getPendingInvitesForUser: vi.fn(),
  cancelInvite: vi.fn(),

  // Security events management
  storeSecurityEvent: vi.fn(),
  getSecurityEvents: vi.fn(),
  getSecurityEventStats: vi.fn(),
  detectSuspiciousIPs: vi.fn(),
  cleanupOldSecurityEvents: vi.fn(),
  getFailedLoginsByIP: vi.fn(),
  getRateLimitViolationsByIP: vi.fn(),
  shouldBlockIP: vi.fn(),

  // Client utilities
  createSupabaseClient: vi.fn(),
  createBrowserClient: vi.fn(),
  createServerClient: vi.fn(),
  supabase: {},
}))

// Import the mocked DAL
import * as DAL from '@ledgerly/dal'
const mockDAL = vi.mocked(DAL)

describe('Security Events Persistence', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset console mocks
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('logSecurityEvent', () => {
    it('should store security events in database', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'AUTH_FAILED_LOGIN', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: 'auth/login', 
        action: 'login_attempt', 
        details: { email: '<EMAIL>', reason: 'invalid_credentials' }, 
        created_at: new Date().toISOString() 
      })

      const mockEvent = {
        type: 'AUTH_FAILED_LOGIN' as const,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        severity: 'medium' as const,
        resource: 'auth/login',
        action: 'login_attempt',
        details: { email: '<EMAIL>', reason: 'invalid_credentials' }
      }

      void logSecurityEvent(mockEvent)

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith({
        event_type: 'AUTH_FAILED_LOGIN',
        user_id: undefined,
        session_id: undefined,
        ip_address: '***********',
        user_agent: 'Mozilla/5.0',
        severity: 'medium',
        resource: 'auth/login',
        action: 'login_attempt',
        details: { email: '<EMAIL>', reason: 'invalid_credentials' }
      })
    })

    it('should handle database storage errors gracefully', async () => {
      mockDAL.storeSecurityEvent.mockRejectedValueOnce(new Error('Database error'))

      const mockEvent = {
        type: 'CSRF_VIOLATION' as const,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        severity: 'high' as const,
        details: { test: 'data' },
      }

      void logSecurityEvent(mockEvent)

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(console.error).toHaveBeenCalledWith(
        'Failed to store security event in database:',
        expect.any(Error)
      )
    })

    it('should skip storing unknown IP addresses', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'RATE_LIMIT_EXCEEDED', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: undefined, 
        user_agent: undefined, 
        severity: 'medium', 
        resource: undefined, 
        action: undefined, 
        details: {}, 
        created_at: new Date().toISOString() 
      })

      const mockEvent = {
        type: 'RATE_LIMIT_EXCEEDED' as const,
        ipAddress: 'unknown',
        userAgent: 'unknown',
        severity: 'medium' as const,
        details: { endpoint: '/api/test' },
      }

      void logSecurityEvent(mockEvent)

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith({
        event_type: 'RATE_LIMIT_EXCEEDED',
        user_id: undefined,
        session_id: undefined,
        ip_address: undefined,
        user_agent: undefined,
        severity: 'medium',
        resource: undefined,
        action: undefined,
        details: {}
      })
    })
  })

  describe('SecurityEvents helpers', () => {
    it('should log failed login events with proper structure', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'AUTH_FAILED_LOGIN', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: 'auth/login', 
        action: 'login_attempt', 
        details: { email: '<EMAIL>', reason: 'invalid_password', timestamp: expect.any(String) }, 
        created_at: new Date().toISOString() 
      })

      void SecurityEvents.failedLogin('<EMAIL>', '***********', 'Mozilla/5.0', 'invalid_password')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'AUTH_FAILED_LOGIN',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0',
          severity: 'medium',
          resource: 'auth/login',
          action: 'login_attempt',
          details: expect.objectContaining({
            email: '<EMAIL>',
            reason: 'invalid_password',
            timestamp: expect.any(String)
          })
        })
      )
    })

    it('should log successful login events', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'AUTH_SUCCESSFUL_LOGIN', 
        user_id: 'user123', 
        session_id: 'session456', 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'low', 
        resource: 'auth/login', 
        action: 'login_success', 
        details: {}, 
        created_at: new Date().toISOString() 
      })

      void SecurityEvents.successfulLogin('user123', 'session456', '***********', 'Mozilla/5.0')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'AUTH_SUCCESSFUL_LOGIN',
          user_id: 'user123',
          session_id: 'session456',
          severity: 'low',
          resource: 'auth/login',
          action: 'login_success'
        })
      )
    })

    it('should log CSRF violations', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'CSRF_VIOLATION', 
        user_id: 'user123', 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'high', 
        resource: '/api/sensitive', 
        action: 'csrf_violation', 
        details: {}, 
        created_at: new Date().toISOString() 
      })

      void SecurityEvents.csrfViolation('user123', '***********', 'Mozilla/5.0', '/api/sensitive')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'CSRF_VIOLATION',
          user_id: 'user123',
          severity: 'high',
          resource: '/api/sensitive',
          action: 'csrf_violation'
        })
      )
    })

    it('should log rate limit exceeded events', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'RATE_LIMIT_EXCEEDED', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: '/api/data', 
        action: 'rate_limit_exceeded', 
        details: { identifier: 'client123', endpoint: '/api/data' }, 
        created_at: new Date().toISOString() 
      })

      void SecurityEvents.rateLimitExceeded('client123', '/api/data', '***********', 'Mozilla/5.0')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'RATE_LIMIT_EXCEEDED',
          severity: 'medium',
          resource: '/api/data',
          action: 'rate_limit_exceeded',
          details: expect.objectContaining({
            identifier: 'client123',
            endpoint: '/api/data'
          })
        })
      )
    })

    it('should log admin actions with high severity', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'ADMIN_ACTION', 
        user_id: 'admin123', 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'high', 
        resource: 'admin', 
        action: 'admin_action', 
        details: { action: 'user_delete', target: 'user456' }, 
        created_at: new Date().toISOString() 
      })

      void SecurityEvents.adminAction('admin123', 'user_delete', 'user456', '***********', 'Mozilla/5.0')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'ADMIN_ACTION',
          user_id: 'admin123',
          severity: 'high',
          resource: 'admin',
          details: expect.objectContaining({
            action: 'user_delete',
            target: 'user456'
          })
        })
      )
    })

    it('should log data exports with appropriate severity based on size', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'DATA_EXPORT', 
        user_id: 'user123', 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'high', 
        resource: 'data/export', 
        action: 'export_data', 
        details: { dataType: 'customer_data', recordCount: 5000 }, 
        created_at: new Date().toISOString() 
      })

      // Large export should be high severity
      void SecurityEvents.dataExport('user123', 'customer_data', 5000, '***********', 'Mozilla/5.0')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'DATA_EXPORT',
          severity: 'high',
          details: expect.objectContaining({
            dataType: 'customer_data',
            recordCount: 5000
          })
        })
      )

      vi.clearAllMocks()
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 2, 
        event_type: 'DATA_EXPORT', 
        user_id: 'user123', 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: 'data/export', 
        action: 'export_data', 
        details: { dataType: 'reports', recordCount: 100 }, 
        created_at: new Date().toISOString() 
      })

      // Small export should be medium severity
      void SecurityEvents.dataExport('user123', 'reports', 100, '***********', 'Mozilla/5.0')

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          severity: 'medium'
        })
      )
    })
  })

  describe('SecurityAnalyzer', () => {
    it('should get failed logins by IP from database', async () => {
      mockDAL.getFailedLoginsByIP.mockResolvedValueOnce(5)

      const result = await SecurityAnalyzer.getFailedLoginsByIP('***********', 10)

      expect(result).toBe(5)
      expect(mockDAL.getFailedLoginsByIP).toHaveBeenCalledWith('***********', 10)
    })

    it('should handle database errors when getting failed logins', async () => {
      mockDAL.getFailedLoginsByIP.mockRejectedValueOnce(new Error('DB error'))

      const result = await SecurityAnalyzer.getFailedLoginsByIP('***********', 5)

      expect(result).toBe(0)
      expect(console.error).toHaveBeenCalledWith('Failed to get failed logins by IP:', expect.any(Error))
    })

    it('should get rate limit violations by IP from database', async () => {
      mockDAL.getRateLimitViolationsByIP.mockResolvedValueOnce(3)

      const result = await SecurityAnalyzer.getRateLimitViolationsByIP('***********', 5)

      expect(result).toBe(3)
      expect(mockDAL.getRateLimitViolationsByIP).toHaveBeenCalledWith('***********', 5)
    })

    it('should check if IP should be blocked', async () => {
      mockDAL.shouldBlockIP.mockResolvedValueOnce(true)

      const result = await SecurityAnalyzer.shouldBlockIP('***********')

      expect(result).toBe(true)
      expect(mockDAL.shouldBlockIP).toHaveBeenCalledWith('***********')
    })

    it('should get high severity events from database', async () => {
      const mockEvents: SecurityEvent[] = [
        {
          id: 1,
          event_type: 'CSRF_VIOLATION',
          user_id: 'user123',
          session_id: undefined,
          ip_address: '***********',
          user_agent: 'Mozilla/5.0',
          severity: 'high',
          resource: '/api/data',
          action: 'csrf_violation',
          details: {},
          created_at: '2023-01-01T10:00:00Z'
        }
      ]

      mockDAL.getSecurityEvents.mockResolvedValueOnce(mockEvents)

      const result = await SecurityAnalyzer.getHighSeverityEvents(24)

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual(
        expect.objectContaining({
          type: 'CSRF_VIOLATION',
          userId: 'user123',
          ipAddress: '***********',
          severity: 'high',
          timestamp: expect.any(Date)
        })
      )
    })

    it('should get suspicious IPs from database', async () => {
      const mockSuspiciousIPs = [
        {
          ip_address: '***********',
          failed_logins: 15,
          rate_limit_violations: 5,
          total_events: 20,
          risk_score: 100
        }
      ]

      mockDAL.detectSuspiciousIPs.mockResolvedValueOnce(mockSuspiciousIPs)

      const result = await SecurityAnalyzer.getSuspiciousIPs(5, 10, 20)

      expect(result).toEqual(mockSuspiciousIPs)
      expect(mockDAL.detectSuspiciousIPs).toHaveBeenCalledWith(5, 10, 20)
    })

    it('should get event statistics from database', async () => {
      const mockStats = [
        {
          event_type: 'AUTH_FAILED_LOGIN',
          event_count: 25,
          unique_ips: 5,
          unique_users: 8,
          severity_breakdown: { medium: 20, high: 5 }
        }
      ]

      mockDAL.getSecurityEventStats.mockResolvedValueOnce(mockStats)

      const result = await SecurityAnalyzer.getEventStats(24, ['AUTH_FAILED_LOGIN'])

      expect(result).toEqual(mockStats)
      expect(mockDAL.getSecurityEventStats).toHaveBeenCalledWith(24, ['AUTH_FAILED_LOGIN'])
    })

    it('should handle errors gracefully for all methods', async () => {
      const methods = [
        ['getRateLimitViolationsByIP', ['***********', 5]],
        ['shouldBlockIP', ['***********']],
        ['getHighSeverityEvents', [24]],
        ['getSuspiciousIPs', [5, 10, 20]],
        ['getEventStats', [24]]
      ] as const

      for (const [method, args] of methods) {
        vi.clearAllMocks()

        // Mock the corresponding DAL function to throw an error
        const dalMethod = method.replace('get', 'get').replace('should', 'should')
        const targetMethod = dalMethod === 'getHighSeverityEvents' ? 'getSecurityEvents' :
                           dalMethod === 'getSuspiciousIPs' ? 'detectSuspiciousIPs' :
                           dalMethod === 'getEventStats' ? 'getSecurityEventStats' :
                           dalMethod;
        (mockDAL as any)[targetMethod].mockRejectedValueOnce(new Error('DB error'))

        const result = await (SecurityAnalyzer as unknown as Record<string, (...args: unknown[]) => Promise<unknown>>)[method](...args)

        expect(console.error).toHaveBeenCalled()

        // Check appropriate fallback values
        if (method.includes('get') && !method.includes('should')) {
          if (method.includes('ByIP')) {
            expect(typeof result).toBe('number')
            expect(result).toBe(0)
          } else {
            expect(Array.isArray(result)).toBe(true)
            expect(result).toEqual([])
          }
        } else if (method.includes('should')) {
          expect(result).toBe(false)
        }
      }
    })
  })

  describe('Integration with monitoring', () => {
    it('should work in development mode', () => {
      vi.stubEnv('NODE_ENV', 'development')

      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'SUSPICIOUS_REQUEST', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Bot/1.0', 
        severity: 'high', 
        resource: 'request', 
        action: 'suspicious_activity', 
        details: { reason: 'Bot detection' }, 
        created_at: new Date().toISOString() 
      })

      const mockEvent = {
        type: 'SUSPICIOUS_REQUEST' as const,
        ipAddress: '***********',
        userAgent: 'Bot/1.0',
        severity: 'high' as const,
        details: { reason: 'Bot detection' },
      }

      void logSecurityEvent(mockEvent)

      expect(console.warn).toHaveBeenCalledWith('🔒 Security Event:', expect.objectContaining({
        type: 'SUSPICIOUS_REQUEST',
        severity: 'high'
      }))

      vi.unstubAllEnvs()
    })

    it('should work in production mode', () => {
      vi.stubEnv('NODE_ENV', 'production')

      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'PERMISSION_DENIED', 
        user_id: 'user123', 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: '/admin', 
        action: 'access_denied', 
        details: { resource: '/admin', action: 'access_denied' }, 
        created_at: new Date().toISOString() 
      })

      const mockEvent = {
        type: 'PERMISSION_DENIED' as const,
        userId: 'user123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        severity: 'medium' as const,
        resource: '/admin',
        action: 'access_denied',
        details: { resource: '/admin', action: 'access_denied' }
      }

      void logSecurityEvent(mockEvent)

      // Should not log to console in production
      expect(console.warn).not.toHaveBeenCalledWith(expect.stringContaining('🔒 Security Event'))

      vi.unstubAllEnvs()
    })
  })

  describe('Performance and Edge Cases', () => {
    it('should handle concurrent event logging', async () => {
      mockDAL.storeSecurityEvent.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ 
          id: Math.floor(Math.random() * 1000), 
          event_type: 'AUTH_FAILED_LOGIN', 
          user_id: undefined, 
          session_id: undefined, 
          ip_address: '***********', 
          user_agent: 'Mozilla/5.0', 
          severity: 'medium', 
          resource: 'auth/login', 
          action: 'login_attempt', 
          details: {}, 
          created_at: new Date().toISOString() 
        }), 10))
      )

      const promises = Array.from({ length: 10 }, (_, i) =>
        void logSecurityEvent({
          type: 'AUTH_FAILED_LOGIN',
          ipAddress: `192.168.1.${i}`,
          userAgent: 'Mozilla/5.0',
          severity: 'medium',
          details: { attempt: i + 1 }
        })
      )

      // All should complete without errors
      await Promise.all(promises.map(_p => new Promise(resolve => setTimeout(resolve, 50))))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledTimes(10)
    })

    it('should handle very large event details', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'SUSPICIOUS_REQUEST', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'high', 
        resource: 'request', 
        action: 'suspicious_activity', 
        details: { request: 'x'.repeat(10000), headers: Array.from({ length: 100 }, (_, i) => `header${i}=value${i}`), stackTrace: 'Error\n'.repeat(1000) }, 
        created_at: new Date().toISOString() 
      })

      const largeDetails = {
        request: 'x'.repeat(10000),
        headers: Array.from({ length: 100 }, (_, i) => `header${i}=value${i}`),
        stackTrace: 'Error\n'.repeat(1000)
      }

      const mockEvent = {
        type: 'SUSPICIOUS_REQUEST' as const,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        severity: 'high' as const,
        details: largeDetails
      }

      void logSecurityEvent(mockEvent)

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          details: largeDetails
        })
      )
    })

    it('should handle null and undefined values gracefully', async () => {
      mockDAL.storeSecurityEvent.mockResolvedValueOnce({ 
        id: 1, 
        event_type: 'AUTH_FAILED_LOGIN', 
        user_id: undefined, 
        session_id: undefined, 
        ip_address: '***********', 
        user_agent: 'Mozilla/5.0', 
        severity: 'medium', 
        resource: undefined, 
        action: undefined, 
        details: {}, 
        created_at: new Date().toISOString() 
      })

      const mockEvent = {
        type: 'AUTH_FAILED_LOGIN' as const,
        userId: undefined,
        sessionId: undefined,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        severity: 'medium' as const,
        resource: undefined,
        action: undefined,
        details: {}
      }

      void logSecurityEvent(mockEvent)

      await new Promise(resolve => setTimeout(resolve, 10))

      expect(mockDAL.storeSecurityEvent).toHaveBeenCalledWith({
        event_type: 'AUTH_FAILED_LOGIN',
        user_id: undefined,
        session_id: undefined,
        ip_address: '***********',
        user_agent: 'Mozilla/5.0',
        severity: 'medium',
        resource: undefined,
        action: undefined,
        details: {}
      })
    })
  })
})