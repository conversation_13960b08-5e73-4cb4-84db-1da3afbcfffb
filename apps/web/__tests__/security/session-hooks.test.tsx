/**
 * Session Security Hooks Tests
 * Tests for React hooks managing session timeouts and security
 */

import { vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'

// Unmock session security hooks to test actual implementation
vi.unmock('@/hooks/useSessionSecurity')

import {
  useSessionSecurity,
  useSessionTimeoutDialog,
  formatTimeRemaining,
} from '@/hooks/useSessionSecurity'

// Mock auth data for tests
const mockAuthData = {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    email_confirmed_at: '2023-01-01T00:00:00.000Z',
    last_sign_in_at: '2023-01-01T00:00:00.000Z',
    role: 'authenticated',
    confirmation_sent_at: '2023-01-01T00:00:00.000Z',
  },
  session: {
    access_token: 'token-123',
    expires_at: Date.now() + 8 * 60 * 60 * 1000,
    expires_in: 8 * 60 * 60,
    refresh_token: 'refresh-123',
    token_type: 'bearer',
    user: {
      id: 'user-123',
      email: '<EMAIL>',
      app_metadata: {},
      user_metadata: {},
      aud: 'authenticated',
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      email_confirmed_at: '2023-01-01T00:00:00.000Z',
      last_sign_in_at: '2023-01-01T00:00:00.000Z',
      role: 'authenticated',
      confirmation_sent_at: '2023-01-01T00:00:00.000Z',
    },
  },
  signOut: vi.fn(),
}

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    replace: vi.fn(),
  }),
}))

// Mock DOM APIs
Object.defineProperty(window, 'location', {
  value: { origin: 'http://localhost:3000', pathname: '/dashboard' },
  writable: true,
})

global.fetch = vi.fn() as unknown as typeof fetch

describe('Session Security Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    // Mock DOM event listeners
    const mockEventListeners: { [key: string]: EventListener[] } = {}

    vi.spyOn(document, 'addEventListener').mockImplementation(
      (event, listener) => {
        if (!mockEventListeners[event]) {
          mockEventListeners[event] = []
        }
        mockEventListeners[event].push(listener as EventListener)
      }
    )

    vi.spyOn(document, 'removeEventListener').mockImplementation(
      (event, listener) => {
        if (mockEventListeners[event]) {
          const index = mockEventListeners[event].indexOf(
            listener as EventListener
          )
          if (index > -1) {
            mockEventListeners[event].splice(index, 1)
          }
        }
      }
    )

    // Mock window event listeners
    const mockWindowListeners: { [key: string]: EventListener[] } = {}

    vi.spyOn(window, 'addEventListener').mockImplementation(
      (event, listener) => {
        if (!mockWindowListeners[event]) {
          mockWindowListeners[event] = []
        }
        mockWindowListeners[event].push(listener as EventListener)
      }
    )

    vi.spyOn(window, 'removeEventListener').mockImplementation(
      (event, listener) => {
        if (mockWindowListeners[event]) {
          const index = mockWindowListeners[event].indexOf(
            listener as EventListener
          )
          if (index > -1) {
            mockWindowListeners[event].splice(index, 1)
          }
        }
      }
    )
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe('useSessionSecurity', () => {
    it('should initialize with active session', () => {
      const { result } = renderHook(() => useSessionSecurity(mockAuthData))

      expect(result.current.isActive).toBe(true)
      expect(result.current.lastActivity).toBeInstanceOf(Date)
      expect(result.current.isOnline).toBe(true)
    })

    it('should set up activity tracking listeners', () => {
      renderHook(() =>
        useSessionSecurity(mockAuthData, { enableActivityTracking: true })
      )

      const expectedEvents = [
        'mousedown',
        'mousemove',
        'keypress',
        'scroll',
        'touchstart',
        'click',
      ]
      expectedEvents.forEach(event => {
        expect(document.addEventListener).toHaveBeenCalledWith(
          event,
          expect.any(Function),
          true
        )
      })
    })

    it('should set up online/offline listeners', () => {
      renderHook(() =>
        useSessionSecurity(mockAuthData, { enableOnlineStatus: true })
      )

      expect(window.addEventListener).toHaveBeenCalledWith(
        'online',
        expect.any(Function)
      )
      expect(window.addEventListener).toHaveBeenCalledWith(
        'offline',
        expect.any(Function)
      )
    })

    it('should update activity when called', () => {
      const onActivityDetected = vi.fn()
      const { result } = renderHook(() =>
        useSessionSecurity(mockAuthData, { onActivityDetected })
      )

      const initialActivity = result.current.lastActivity

      act(() => {
        result.current.updateActivity()
      })

      expect(result.current.lastActivity).not.toEqual(initialActivity)
      expect(onActivityDetected).toHaveBeenCalled()
    })

    it('should extend session with keep-alive request', async () => {
      ;(fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValueOnce({
        ok: true,
      })

      const { result } = renderHook(() => useSessionSecurity(mockAuthData))

      await act(async () => {
        result.current.extendSession()
      })

      expect(fetch).toHaveBeenCalledWith('/api/auth/keep-alive', {
        method: 'POST',
      })
    })

    it('should handle timeout warnings', () => {
      const onTimeoutWarning = vi.fn()
      const { result } = renderHook(() =>
        useSessionSecurity(mockAuthData, { onTimeoutWarning })
      )

      // Simulate approaching timeout
      act(() => {
        // This would typically be triggered by the session manager
        const mockTimeoutInfo = {
          idleTimeRemaining: 240, // 4 minutes
          absoluteTimeRemaining: 300, // 5 minutes
          showWarning: true,
          shouldTimeout: false,
          reason: null,
        }
        onTimeoutWarning(mockTimeoutInfo)
      })

      expect(onTimeoutWarning).toHaveBeenCalled()
    })

    it('should handle session timeout', async () => {
      const mockSignOut = vi.fn()
      const mockReplace = vi.fn()

      // Mock auth context
      vi.doMock('@/contexts/AuthContext', () => ({
        useAuth: () => ({
          user: { id: 'user-123' },
          session: { access_token: 'token-123' },
          signOut: mockSignOut,
        }),
      }))

      vi.doMock('next/navigation', () => ({
        useRouter: () => ({
          replace: mockReplace,
        }),
      }))

      const onTimeout = jest.fn()
      const { result } = renderHook(() =>
        useSessionSecurity(mockAuthData, { onTimeout })
      )

      await act(async () => {
        result.current.forceLogout()
      })

      expect(onTimeout).toHaveBeenCalledWith('user_requested')
    })

    it('should clean up listeners on unmount', () => {
      const { unmount } = renderHook(() =>
        useSessionSecurity(mockAuthData, {
          enableActivityTracking: true,
          enableOnlineStatus: true,
        })
      )

      act(() => {
        unmount()
      })

      const expectedEvents = [
        'mousedown',
        'mousemove',
        'keypress',
        'scroll',
        'touchstart',
        'click',
      ]
      expectedEvents.forEach(event => {
        expect(document.removeEventListener).toHaveBeenCalledWith(
          event,
          expect.any(Function),
          true
        )
      })

      expect(window.removeEventListener).toHaveBeenCalledWith(
        'online',
        expect.any(Function)
      )
      expect(window.removeEventListener).toHaveBeenCalledWith(
        'offline',
        expect.any(Function)
      )
    })

    it('should handle offline status', () => {
      const { result } = renderHook(() =>
        useSessionSecurity(mockAuthData, { enableOnlineStatus: true })
      )

      // Simulate going offline
      act(() => {
        const offlineEvent = new Event('offline')
        window.dispatchEvent(offlineEvent)
      })

      // Note: The actual offline handler would be called by the real event system
      // In the test, we just verify the handler was registered
      expect(window.addEventListener).toHaveBeenCalledWith(
        'offline',
        expect.any(Function)
      )
    })
  })

  describe('useSessionTimeoutDialog', () => {
    it('should initialize with dialog closed', () => {
      const { result } = renderHook(() => useSessionTimeoutDialog())

      expect(result.current.showDialog).toBe(false)
      expect(result.current.timeoutInfo).toBeNull()
    })

    it('should show dialog on timeout warning', () => {
      const { result } = renderHook(() => useSessionTimeoutDialog())

      const mockTimeoutInfo = {
        timeUntilTimeout: 180,
        isWarning: true,
        warningTime: 5 * 60 * 1000,
        maxIdleTime: 30 * 60 * 1000,
        absoluteTimeRemaining: 300,
        idleTimeRemaining: 180,
        showWarning: true,
        shouldTimeout: false,
        reason: null,
      }

      act(() => {
        result.current.onTimeoutWarning(mockTimeoutInfo)
      })

      expect(result.current.showDialog).toBe(true)
      expect(result.current.timeoutInfo).toEqual(mockTimeoutInfo)
    })

    it('should extend session and close dialog', () => {
      const { result } = renderHook(() => useSessionTimeoutDialog())

      // First show the dialog
      act(() => {
        result.current.onTimeoutWarning({
          timeUntilTimeout: 180,
          isWarning: true,
          warningTime: 5 * 60 * 1000,
          maxIdleTime: 30 * 60 * 1000,
          absoluteTimeRemaining: 300,
          idleTimeRemaining: 180,
          showWarning: true,
          shouldTimeout: false,
          reason: null,
        })
      })

      expect(result.current.showDialog).toBe(true)

      // Then extend session
      act(() => {
        result.current.extendSession()
      })

      expect(result.current.showDialog).toBe(false)
    })

    it('should logout now and close dialog', () => {
      const { result } = renderHook(() => useSessionTimeoutDialog())

      // First show the dialog
      act(() => {
        result.current.onTimeoutWarning({
          timeUntilTimeout: 180,
          isWarning: true,
          warningTime: 5 * 60 * 1000,
          maxIdleTime: 30 * 60 * 1000,
          absoluteTimeRemaining: 300,
          idleTimeRemaining: 180,
          showWarning: true,
          shouldTimeout: false,
          reason: null,
        })
      })

      expect(result.current.showDialog).toBe(true)

      // Then logout now
      act(() => {
        result.current.logoutNow()
      })

      expect(result.current.showDialog).toBe(false)
    })

    it('should auto-close dialog when time runs out', () => {
      const { result } = renderHook(() => useSessionTimeoutDialog())

      act(() => {
        result.current.onTimeoutWarning({
          timeUntilTimeout: 2, // Very short time
          isWarning: true,
          warningTime: 5 * 60 * 1000,
          maxIdleTime: 30 * 60 * 1000,
          absoluteTimeRemaining: 300,
          idleTimeRemaining: 2, // Very short time
          showWarning: true,
          shouldTimeout: false,
          reason: null,
        })
      })

      expect(result.current.showDialog).toBe(true)

      // Advance timers to simulate timeout
      act(() => {
        vi.advanceTimersByTime(3000) // 3 seconds
      })

      expect(result.current.showDialog).toBe(false)
    })
  })

  describe('formatTimeRemaining', () => {
    it('should format seconds only', () => {
      expect(formatTimeRemaining(45)).toBe('45s')
      expect(formatTimeRemaining(0)).toBe('0s')
    })

    it('should format minutes and seconds', () => {
      expect(formatTimeRemaining(65)).toBe('1m 5s')
      expect(formatTimeRemaining(120)).toBe('2m 0s')
      expect(formatTimeRemaining(305)).toBe('5m 5s')
    })

    it('should handle large values', () => {
      expect(formatTimeRemaining(3661)).toBe('61m 1s')
    })

    it('should handle negative values', () => {
      expect(formatTimeRemaining(-10)).toBe('0s')
    })

    it('should handle edge cases', () => {
      expect(formatTimeRemaining(60)).toBe('1m 0s')
      expect(formatTimeRemaining(59)).toBe('59s')
    })
  })

  describe('Integration Tests', () => {
    it('should coordinate between hooks', () => {
      const { result: sessionResult } = renderHook(() =>
        useSessionSecurity(mockAuthData)
      )
      const { result: dialogResult } = renderHook(() =>
        useSessionTimeoutDialog()
      )

      // Simulate the session hook triggering the dialog
      const mockTimeoutInfo = {
        timeUntilTimeout: 240,
        isWarning: true,
        warningTime: 5 * 60 * 1000,
        maxIdleTime: 30 * 60 * 1000,
        absoluteTimeRemaining: 300,
        idleTimeRemaining: 240,
        showWarning: true,
        shouldTimeout: false,
        reason: null,
      }

      act(() => {
        dialogResult.current.onTimeoutWarning(mockTimeoutInfo)
      })

      expect(dialogResult.current.showDialog).toBe(true)

      // Extend session from dialog should also update main hook
      act(() => {
        sessionResult.current.extendSession()
        dialogResult.current.extendSession()
      })

      expect(dialogResult.current.showDialog).toBe(false)
    })

    it('should handle rapid state changes', () => {
      const { result } = renderHook(() =>
        useSessionSecurity(mockAuthData, { checkInterval: 1 })
      )

      // Rapidly update activity
      act(() => {
        for (let i = 0; i < 10; i++) {
          result.current.updateActivity()
        }
      })

      expect(result.current.isActive).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle fetch errors gracefully', async () => {
      ;(fetch as unknown as ReturnType<typeof vi.fn>).mockRejectedValueOnce(
        new Error('Network error')
      )

      const { result } = renderHook(() => useSessionSecurity(mockAuthData))

      // Should not throw
      await act(async () => {
        result.current.extendSession()
      })

      expect(result.current.isActive).toBe(true)
    })

    it('should handle missing auth context', () => {
      const noSessionAuthData = {
        user: null,
        session: null,
        signOut: vi.fn(),
      }

      const { result } = renderHook(() => useSessionSecurity(noSessionAuthData))

      expect(result.current.isActive).toBe(false)
      expect(result.current.timeoutInfo).toBeNull()
    })
  })
})
