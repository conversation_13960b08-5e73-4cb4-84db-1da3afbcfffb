import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { signInWithEmail } from '@ledgerly/dal'
import { useAuth } from '@/contexts/AuthContext'
import { AuthProvider } from '@/contexts/AuthContext'
import LoginPage from '@/app/login/page'

// Mock the auth DAL functions
vi.mock('@ledgerly/dal', () => ({
  signInWithEmail: vi.fn(),
}))

// Mock useCSRF hook
vi.mock('@/hooks/useCSRF', () => ({
  useCSRF: () => ({
    createCSRFInput: () => <input type="hidden" name="_csrf_token" value="test-token" />,
    csrfToken: 'test-token',
  }),
}))

// Mock useAuth hook
const mockUseAuth = vi.fn()
vi.mock('@/contexts/AuthContext', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@/contexts/AuthContext')>()
  return {
    ...actual,
    useAuth: () => mockUseAuth(),
  }
})

const mockPush = vi.fn()
const mockReplace = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}))

describe('Authentication Security', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Default mock for useAuth
    mockUseAuth.mockReturnValue({
      user: null,
      session: null,
      loading: false,
      signOut: vi.fn(),
    })
  })

  describe('Login Form Security', () => {
    it('should include CSRF token in login form', () => {
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const csrfInput = screen.getByRole('textbox', { name: /email/i })
        .closest('form')
        ?.querySelector('input[name="_csrf_token"]')
      
      expect(csrfInput).toBeInTheDocument()
      expect(csrfInput).toHaveValue('test-token')
    })

    it('should validate email format before submission', async () => {
      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      const submitButton = screen.getByRole('button', { name: /send login link/i })

      await user.type(emailInput, 'invalid-email')
      await user.click(submitButton)

      // HTML5 validation should prevent submission
      expect(emailInput).toBeInvalid()
    })

    it('should disable submit button during loading', async () => {
      ;(signInWithEmail as unknown as ReturnType<typeof vi.fn>).mockImplementation(() => new Promise(() => {})) // Never resolves

      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      const submitButton = screen.getByRole('button', { name: /send login link/i })

      await user.type(emailInput, '<EMAIL>')
      await user.click(submitButton)

      await waitFor(() => {
        expect(submitButton).toBeDisabled()
        expect(submitButton).toHaveTextContent('Sending...')
      })
    })

    it('should handle authentication errors gracefully', async () => {
      ;(signInWithEmail as unknown as ReturnType<typeof vi.fn>).mockRejectedValue(new Error('Authentication failed'))

      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      const submitButton = screen.getByRole('button', { name: /send login link/i })

      await user.type(emailInput, '<EMAIL>')
      await user.click(submitButton)

      await waitFor(() => {
        // Should show generic sanitized error message
        expect(screen.getByText('Unable to send login link. Please try again.')).toBeInTheDocument()
      })
    })

    it('should not expose sensitive information in error messages', async () => {
      ;(signInWithEmail as unknown as ReturnType<typeof vi.fn>).mockRejectedValue(new Error('Internal server error: Database connection failed with credentials user:password'))

      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      const submitButton = screen.getByRole('button', { name: /send login link/i })

      await user.type(emailInput, '<EMAIL>')
      await user.click(submitButton)

      await waitFor(() => {
        const errorMessage = screen.getByText(/unable to send login link/i)
        expect(errorMessage.textContent).not.toContain('password')
        expect(errorMessage.textContent).not.toContain('credentials')
        expect(errorMessage.textContent).not.toContain('Database')
        expect(errorMessage.textContent).not.toContain('Internal server')
        // Should show generic, safe error message
        expect(errorMessage.textContent).toContain('Unable to send login link')
      })
    })
  })

  describe('Session Management Security', () => {
    it('should handle authenticated state without crashing', () => {
      // Mock useAuth to return authenticated user
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>' },
        session: {},
        loading: false,
        signOut: vi.fn(),
      })

      // Test that the component handles authenticated state without crashing
      // The actual redirect logic is handled by middleware, not the component
      let component
      expect(() => {
        component = render(
          <AuthProvider>
            <LoginPage />
          </AuthProvider>
        )
      }).not.toThrow()
      
      // Basic assertion that render completed
      expect(component).toBeDefined()
    })

    it('should handle session timeout gracefully', async () => {
      // Mock expired session scenario
      mockUseAuth.mockReturnValue({
        user: null,
        session: null,
        loading: false,
        signOut: jest.fn(),
      })

      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      // Should show login form for unauthenticated users
      expect(screen.getByRole('textbox', { name: /email/i })).toBeInTheDocument()
    })
  })

  describe('Input Sanitization', () => {
    it('should sanitize email input', async () => {
      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      
      // Try to input potentially malicious content
      await user.type(emailInput, '<script>alert("xss")</script>@example.com')
      
      // Value should be sanitized or rejected
      expect(emailInput).toHaveValue('<script>alert("xss")</script>@example.com')
      // HTML5 email validation should prevent this from being submitted
      expect(emailInput).toBeInvalid()
    })

    it('should handle special characters in email safely', async () => {
      ;(signInWithEmail as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({})

      const user = userEvent.setup()
      
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const emailInput = screen.getByRole('textbox', { name: /email/i })
      const submitButton = screen.getByRole('button', { name: /send login link/i })
      
      await user.type(emailInput, "<EMAIL>")
      await user.click(submitButton)

      await waitFor(() => {
        expect(signInWithEmail).toHaveBeenCalledWith(
          '<EMAIL>',
          expect.any(String)
        )
      })
    })
  })

  describe('Security Headers and Attributes', () => {
    it('should have proper form security attributes', () => {
      render(
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      )

      const form = screen.getByRole('textbox', { name: /email/i }).closest('form')
      const emailInput = screen.getByRole('textbox', { name: /email/i })
      
      expect(emailInput).toHaveAttribute('autoComplete', 'email')
      expect(emailInput).toHaveAttribute('required')
      expect(emailInput).toHaveAttribute('type', 'email')
    })

    it('should include security-focused meta tags', () => {
      // This would be tested in a full page render with the layout
      // For now, we verify the structure exists
      expect(true).toBe(true) // Placeholder for layout security tests
    })
  })

  describe('Password Security (Future Implementation)', () => {
    it('should enforce strong password requirements when passwords are added', () => {
      // Placeholder for future password-based authentication
      expect(true).toBe(true)
    })

    it('should hash passwords securely when passwords are added', () => {
      // Placeholder for future password hashing tests
      expect(true).toBe(true)
    })
  })

  describe('Brute Force Protection', () => {
    it('should be protected by rate limiting', () => {
      // Rate limiting is tested separately, but integration test would go here
      expect(true).toBe(true)
    })
  })

  describe('Vulnerability Prevention', () => {
    it('should prevent clickjacking with proper headers', () => {
      // This would be tested with security header checks
      expect(true).toBe(true)
    })

    it('should prevent MIME sniffing attacks', () => {
      // This would be tested with X-Content-Type-Options header
      expect(true).toBe(true)
    })
  })
})