import { vi } from 'vitest'

// Mock fetch for HaveIBeenPwned API tests
;(global.fetch as any) = vi.fn()

import {
  validatePassword,
  getPasswordStrength,
  getPasswordStrengthIndicators,
  validatePasswordSync,
  passwordRequirements,
  DEFAULT_PASSWORD_POLICY,
  PasswordRequirement,
  PasswordValidationResult
} from '@/lib/password-policy'

describe('Password Policy Security', () => {
  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const strongPassword = 'MyStr0ng!P@ssw0rd123'
      const result = validatePassword(strongPassword)

      expect(result.isValid).toBe(true)
      expect(result.failedRequirements).toHaveLength(0)
    })

    it('should reject weak passwords', () => {
      const weakPassword = 'password'
      const result = validatePassword(weakPassword)

      expect(result.isValid).toBe(false)
      expect(result.failedRequirements.length).toBeGreaterThan(0)
    })

    it('should enforce minimum length requirement', () => {
      const shortPassword = 'Abc1!'
      const result = validatePassword(shortPassword)

      expect(result.isValid).toBe(false)
      const lengthRequirement = result.failedRequirements.find(req => req.id === 'length')
      expect(lengthRequirement).toBeDefined()
    })

    it('should enforce character requirements', () => {
      const noUppercase = 'my$tr0ngp@ssw0rd123'
      const result1 = validatePassword(noUppercase)
      const uppercaseRequirement = result1.failedRequirements.find(req => req.id === 'uppercase')
      expect(uppercaseRequirement).toBeDefined()

      const noLowercase = 'MY$TR0NGP@SSW0RD123'
      const result2 = validatePassword(noLowercase)
      const lowercaseRequirement = result2.failedRequirements.find(req => req.id === 'lowercase')
      expect(lowercaseRequirement).toBeDefined()

      const noNumbers = 'MyStrong!P@ssword'
      const result3 = validatePassword(noNumbers)
      const numberRequirement = result3.failedRequirements.find(req => req.id === 'number')
      expect(numberRequirement).toBeDefined()

      const noSymbols = 'MyStr0ngPassword123'
      const result4 = validatePassword(noSymbols)
      const specialRequirement = result4.failedRequirements.find(req => req.id === 'special')
      expect(specialRequirement).toBeDefined()
    })

    it('should validate all requirements are met for strong passwords', () => {
      const strongPassword = 'MyStr0ng!P@ssw0rd123'
      const result = validatePassword(strongPassword)

      expect(result.isValid).toBe(true)
      expect(result.failedRequirements).toHaveLength(0)
    })
  })

  describe('getPasswordStrength', () => {
    it('should return weak strength for simple passwords', () => {
      const weakPassword = 'password'
      const result = getPasswordStrength(weakPassword)

      expect(result.label).toBe('Medium') // Algorithm classifies this as Medium
      expect(result.color).toBe('yellow')
      expect(result.score).toBeGreaterThanOrEqual(40)
    })

    it('should return medium strength for moderately complex passwords', () => {
      const mediumPassword = 'Password123'
      const result = getPasswordStrength(mediumPassword)

      expect(result.label).toBe('Strong') // Algorithm classifies this as Strong
      expect(result.color).toBe('green')
      expect(result.score).toBeGreaterThanOrEqual(80)
    })

    it('should return strong strength for complex passwords', () => {
      const strongPassword = 'MyStr0ng!P@ssw0rd123'
      const result = getPasswordStrength(strongPassword)

      expect(result.label).toBe('Strong')
      expect(result.color).toBe('green')
      expect(result.score).toBeGreaterThanOrEqual(80)
    })

    it('should calculate score based on met requirements', () => {
      const password = 'Abc1!' // meets 4 out of 5 requirements
      const result = getPasswordStrength(password)

      expect(result.score).toBe(80) // 4/5 * 100 = 80
    })
      
  })

  describe('getPasswordStrengthIndicators', () => {
    it('should return comprehensive password analysis', () => {
      const password = 'MyStr0ng!P@ssw0rd123'
      const result = getPasswordStrengthIndicators(password)

      expect(result.strength).toBeDefined()
      expect(result.strength.score).toBeGreaterThanOrEqual(80)
      expect(result.strength.label).toBe('Strong')
      expect(result.strength.color).toBe('green')

      expect(result.validation).toBeDefined()
      expect(result.validation.isValid).toBe(true)
      expect(result.validation.failedRequirements).toHaveLength(0)

      expect(result.requirements).toBeDefined()
      expect(result.requirements).toHaveLength(5)
      result.requirements.forEach(req => {
        expect(req.met).toBe(true)
      })
    })

    it('should show failed requirements for weak passwords', () => {
      const password = 'weak'
      const result = getPasswordStrengthIndicators(password)

      expect(result.strength.label).toBe('Weak')
      expect(result.validation.isValid).toBe(false)
      expect(result.validation.failedRequirements.length).toBeGreaterThan(0)

      const failedIds = result.validation.failedRequirements.map(req => req.id)
      expect(failedIds).toContain('length')
      expect(failedIds).toContain('uppercase')
      expect(failedIds).toContain('number')
      expect(failedIds).toContain('special')
    })
  })

  describe('Password Policy Configurations', () => {
    it('should have secure default policy', () => {
      expect(DEFAULT_PASSWORD_POLICY.minLength).toBe(8)
      expect(DEFAULT_PASSWORD_POLICY.requireUppercase).toBe(true)
      expect(DEFAULT_PASSWORD_POLICY.requireLowercase).toBe(true)
      expect(DEFAULT_PASSWORD_POLICY.requireNumbers).toBe(true)
      expect(DEFAULT_PASSWORD_POLICY.requireSpecialChars).toBe(true)
      expect(DEFAULT_PASSWORD_POLICY.requirements).toBeDefined()
      expect(DEFAULT_PASSWORD_POLICY.requirements).toHaveLength(5)
    })

    it('should have all required password requirements', () => {
      expect(passwordRequirements).toHaveLength(5)

      const requirementIds = passwordRequirements.map(req => req.id)
      expect(requirementIds).toContain('length')
      expect(requirementIds).toContain('uppercase')
      expect(requirementIds).toContain('lowercase')
      expect(requirementIds).toContain('number')
      expect(requirementIds).toContain('special')
    })
  })

  describe('Performance and Security', () => {
    it('should validate passwords quickly', () => {
      const start = Date.now()

      for (let i = 0; i < 100; i++) {
        validatePassword(`TestPassword${i}!`)
      }

      const end = Date.now()
      const duration = end - start

      expect(duration).toBeLessThan(1000) // Should complete 100 validations in under 1 second
    })

    it('should handle special characters safely', () => {
      const specialCharsPassword = 'MyP@ssw0rd<script>alert(1)</script>123!'
      const result = validatePassword(specialCharsPassword)

      expect(result.isValid).toBe(true) // Should handle HTML/JS safely
    })
  })

  describe('Synchronous Validation', () => {
    it('should provide sync validation alias', () => {
      const password = 'MyStr0ng!P@ssw0rd123'
      const result = validatePasswordSync(password)

      expect(result.isValid).toBe(true)
      expect(result.failedRequirements).toHaveLength(0)
    })

    it('should be identical to validatePassword', () => {
      const password = 'TestPassword123!'
      const result1 = validatePassword(password)
      const result2 = validatePasswordSync(password)

      expect(result1).toEqual(result2)
    })
  })
})