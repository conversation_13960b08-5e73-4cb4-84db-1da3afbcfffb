# BelBooks BFF (Backend for Frontend) API

A Fastify-based API server that serves as the Backend for Frontend for the BelBooks application.

## Features

- **Health Check**: `/healthz` endpoint for monitoring
- **RPC Routes**: Journal posting, mode switching, role management
- **Entity Routes**: Account, journal, and trial balance queries
- **Authentication**: Internal key-based authentication
- **Input Validation**: Zod schemas for request validation
- **Error Handling**: Comprehensive error handling and logging
- **CORS**: Configured for development and production

## API Endpoints

### Health Check
- `GET /healthz` - Returns service health status

### RPC Endpoints
- `POST /rpc/post-journal` - Post a new journal entry
- `POST /rpc/switch-mode` - Switch entity operating mode
- `POST /rpc/grant-role` - Grant role to user for entity

### Entity Endpoints
- `GET /entities/:id/accounts` - Get entity accounts
- `GET /entities/:id/journals` - Get entity journals (supports pagination)
- `GET /entities/:id/trial-balance` - Get trial balance (supports date filtering)

## Authentication

All endpoints (except `/healthz`) require the `X-Internal-Key` header with the configured internal key.

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
BFF_PORT=3001
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
INTERNAL_KEY=your-secure-internal-key
NODE_ENV=development
```

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Type checking
pnpm typecheck

# Linting
pnpm lint
```

## Project Structure

```
src/
├── index.ts              # Main server setup
├── plugins/
│   ├── auth.ts          # Authentication plugin
│   └── supabase.ts      # Supabase client plugin
└── routes/
    ├── health.ts        # Health check routes
    └── rpc.ts           # RPC and entity routes
```

## Dependencies

- **Runtime**: Fastify, Supabase JS client, Zod validation
- **Workspace**: @ledgerly/types, @ledgerly/dal
- **Development**: TypeScript, ESLint, Nodemon, TSX