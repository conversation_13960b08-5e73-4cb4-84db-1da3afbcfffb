import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { buildApp } from '../src/index.js'
import type { FastifyInstance } from 'fastify'

/**
 * Integration route tests for Track F
 * Tests basic endpoint functionality and auth requirements
 */

describe('Integrations Routes', () => {
  let app: FastifyInstance

  beforeAll(async () => {
    try {
      app = await buildApp()
      await app.ready()
    } catch (error) {
      console.error('Failed to start app:', error)
      throw error
    }
  }, 30000) // 30 second timeout

  afterAll(async () => {
    if (app) {
      await app.close()
    }
  })

  describe('Authentication', () => {
    it('should require authentication for export jobs endpoint', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/entities/1/exports/jobs'
        // No authorization header
      })

      expect(response.statusCode).toBe(401)
    })

    it('should require authentication for summary endpoint', async () => {
      const response = await app.inject({
        method: 'GET', 
        url: '/entities/1/exports/summary'
        // No authorization header
      })

      expect(response.statusCode).toBe(401)
    })

    it('should require authentication for connector config endpoint', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/entities/1/exports/connectors'
        // No authorization header  
      })

      expect(response.statusCode).toBe(401)
    })
  })

  describe('Input Validation', () => {
    it('should validate entity ID parameter', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/entities/invalid/exports/jobs',
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      })

      // Auth should be checked first, then validation
      expect(response.statusCode).toBe(401)
    })

    it('should validate query parameters for jobs endpoint', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/entities/1/exports/jobs?limit=invalid',
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      })

      // Auth should be checked first
      expect(response.statusCode).toBe(401)
    })

    it('should validate connector config request body', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: '/entities/1/exports/connectors',
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          connector: 'invalid_connector',
          config: {}
        })
      })

      expect(response.statusCode).toBe(401)
    })

    it('should validate retry job request', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/entities/1/exports/jobs/invalid/retry',
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      })

      expect(response.statusCode).toBe(401)
      // Auth should be checked first
    })
  })

  describe('Route Registration', () => {
    it('should have registered all expected routes', async () => {
      const routes = app.printRoutes()
      
      // Check that integration routes are registered (using exact matches from Fastify's tree structure)
      expect(routes).toContain('├── jobs (GET, HEAD)')
      expect(routes).toContain('├── dry-run (POST)')
      expect(routes).toContain('├── summary (GET, HEAD)')
      expect(routes).toContain('└── connectors (GET, HEAD, PUT)')
    })
  })

  describe('Dry Run Validation', () => {
    it('should validate dry-run request body', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/entities/1/exports/dry-run',
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          // Missing required fields
        })
      })

      expect(response.statusCode).toBe(401)
    })

    it('should validate mock data structure in dry-run', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/entities/1/exports/dry-run',
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          mockData: {
            source: 'INVALID_SOURCE', // Invalid enum value
            doc_number: 'TEST-001',
            entity_id: 1,
            export_payload: {}
          }
        })
      })

      expect(response.statusCode).toBe(401)
    })
  })
})