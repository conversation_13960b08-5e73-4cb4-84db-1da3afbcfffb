{"extends": "./tsconfig.json", "compilerOptions": {"rootDir": "../../", "noEmit": true, "exactOptionalPropertyTypes": false, "baseUrl": ".", "paths": {"@belbooks/types": ["../../packages/types/src/index.ts"], "@belbooks/types/*": ["../../packages/types/src/*"], "@belbooks/dal": ["../../packages/dal/src/index.ts"], "@belbooks/dal/*": ["../../packages/dal/src/*"], "@belbooks/import-service": ["../../packages/import-service/src/index.ts"], "@belbooks/import-service/*": ["../../packages/import-service/src/*"], "@belbooks/domain-bank": ["../../packages/domain-bank/src/index.ts"], "@belbooks/domain-bank/*": ["../../packages/domain-bank/src/*"], "@belbooks/domain-invoicing": ["../../packages/domain-invoicing/src/index.ts"], "@ledgerly/domain-invoicing/*": ["../../packages/domain-invoicing/src/*"]}}, "include": ["src", "../../packages/types/src", "../../packages/dal/src", "../../packages/import-service/src", "../../packages/domain-bank/src", "../../packages/domain-invoicing/src"]}