{"name": "@belbooks/bff", "version": "0.1.0", "description": "BelB<PERSON>s Backend for Frontend API", "main": "dist/index.js", "type": "module", "scripts": {"dev": "nodemon --exec tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "typecheck": "tsc -p tsconfig.typecheck.json", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/env": "^4.4.0", "@fastify/multipart": "^8.0.0", "@belbooks/dal": "workspace:*", "@belbooks/domain-invoicing": "workspace:*", "@belbooks/import-service": "workspace:*", "@belbooks/types": "workspace:*", "@supabase/supabase-js": "^2.45.4", "fastify": "^4.28.1", "fastify-plugin": "^4.5.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.10", "@vitest/ui": "^3.2.4", "nodemon": "^3.1.4", "pino-pretty": "^11.2.1", "tsx": "^4.16.2", "typescript": "^5.5.3", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}