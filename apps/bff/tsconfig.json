{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "vitest/globals"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}