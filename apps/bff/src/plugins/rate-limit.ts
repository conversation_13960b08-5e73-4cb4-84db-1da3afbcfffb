/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
/**
 * Basic Rate Limiting Plugin for BFF
 * Simple in-memory rate limiter for basic protection
 */

import { FastifyPluginCallback } from 'fastify'
import fastifyPlugin from 'fastify-plugin'

interface RateLimitOptions {
  max: number
  windowMs: number
  skipSuccessfulRequests?: boolean
}

interface ClientInfo {
  count: number
  resetTime: number
}

const rateLimitPlugin: FastifyPluginCallback<RateLimitOptions> = async (fastify, options) => {
  const clients = new Map<string, ClientInfo>()
  const { max, windowMs, skipSuccessfulRequests = false } = options

  // Clean up expired entries periodically
  const cleanupInterval = setInterval(() => {
    const now = Date.now()
    for (const [key, info] of clients.entries()) {
      if (now > info.resetTime) {
        clients.delete(key)
      }
    }
  }, windowMs)

  // Add cleanup on server close
  fastify.addHook('onClose', () => {
    clearInterval(cleanupInterval)
  })

  fastify.addHook('preHandler', async (request, reply) => {
    // Skip rate limiting for health checks
    if (request.url === '/healthz') {
      return
    }

    // Get client identifier (IP address or X-Forwarded-For)
    const clientId = request.headers['x-forwarded-for'] as string ||
                    request.headers['x-real-ip'] as string ||
                    request.ip ||
                    'unknown'

    const now = Date.now()
    const resetTime = now + windowMs

    let clientInfo = clients.get(clientId)

    // Initialize or reset if window expired
    if (!clientInfo || now > clientInfo.resetTime) {
      clientInfo = { count: 0, resetTime }
      clients.set(clientId, clientInfo)
    }

    // Increment request count
    clientInfo.count++

    // Set rate limit headers
    const remaining = Math.max(0, max - clientInfo.count)

    void reply.header('X-RateLimit-Limit', max.toString())
    void reply.header('X-RateLimit-Remaining', remaining.toString())
    void reply.header('X-RateLimit-Reset', Math.ceil(clientInfo.resetTime / 1000).toString())

    // Check if limit exceeded
    if (clientInfo.count > max) {
      fastify.log.warn(`Rate limit exceeded for client ${clientId}: ${clientInfo.count}/${max}`)

      void reply.status(429).send({
        success: false,
        error: 'Too Many Requests',
        retryAfter: Math.ceil((clientInfo.resetTime - now) / 1000)
      })
      return
    }

    // Log high usage (80% of limit)
    if (clientInfo.count > max * 0.8) {
      fastify.log.info(`High rate limit usage for client ${clientId}: ${clientInfo.count}/${max}`)
    }
  })

  // Optional: Skip counting successful requests
  if (skipSuccessfulRequests) {
    fastify.addHook('onResponse', async (request, reply) => {
      if (reply.statusCode < 400) {
        const clientId = request.headers['x-forwarded-for'] as string ||
                        request.headers['x-real-ip'] as string ||
                        request.ip ||
                        'unknown'

        const clientInfo = clients.get(clientId)
        if (clientInfo && clientInfo.count > 0) {
          clientInfo.count--
        }
      }
    })
  }

  fastify.log.info(`Rate limiting enabled: ${max} requests per ${windowMs}ms`)
}

export default fastifyPlugin(rateLimitPlugin, {
  name: 'rate-limit-plugin'
})

