import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { FastifyPluginCallback } from 'fastify'
import fastifyPlugin from 'fastify-plugin'
import { Database } from '@belbooks/types'

declare module 'fastify' {
  interface FastifyInstance { // eslint-disable-line no-unused-vars
    supabase: SupabaseClient<Database>
    createUserClient: (accessToken: string) => SupabaseClient<Database> // eslint-disable-line no-unused-vars
  }

  interface FastifyRequest { // eslint-disable-line no-unused-vars
    userSupabase?: SupabaseClient<Database>
  }
}

export interface SupabasePluginOptions {
  url: string
  serviceRoleKey: string
  anonKey: string
}

const supabasePlugin: FastifyPluginCallback<SupabasePluginOptions> = async (fastify, options) => {
  // Service role client for system operations
  const serviceClient = createClient<Database>(options.url, options.serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  // Function to create user-scoped clients
  const createUserClient = (accessToken: string): SupabaseClient<Database> => {
    return createClient<Database>(options.url, options.anonKey, {
      global: {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      },
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  }

  fastify.decorate('supabase', serviceClient)
  fastify.decorate('createUserClient', createUserClient)
  fastify.decorateRequest('userSupabase', null)

  fastify.log.info('Supabase client initialized with user context support')
}

export default fastifyPlugin(supabasePlugin, {
  name: 'supabase-plugin'
})
