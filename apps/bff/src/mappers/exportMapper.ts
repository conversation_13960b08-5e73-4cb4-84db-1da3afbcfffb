/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
import { ExtractionResult, ExportPayload } from '@belbooks/types'
import type { ExtendedSupabaseClient, ConnectorConfig } from '../types'

interface AccountInfo {
  id: number
  code: string
  name: string
}

/**
 * Maps extraction result to canonical export payload for assist mode integrations
 * Provides standardized format for external accounting system consumption
 */
export async function extractionToExportPayload(
  supabase: ExtendedSupabaseClient,
  entityId: number,
  documentId: number,
  extraction: ExtractionResult
): Promise<ExportPayload> {

  // Get account codes for mapping
  const accountIds = extraction.lines
    .map(line => line.accountHint)
    .filter((id): id is number => id !== undefined)

  const accountMap: Map<number, AccountInfo> = new Map()

  if (accountIds.length > 0) {
    const { data: accounts } = await supabase
      .from('accounts')
      .select('id, code, name')
      .eq('entity_id', entityId)
      .in('id', accountIds)

    if (accounts) {
      (accounts as AccountInfo[]).forEach(acc => accountMap.set(acc.id, acc))
    }
  }

  // Map extraction lines to export format
  const exportLines = extraction.lines.map(line => {
    const netAmount = parseFloat(line.unitPrice) * parseFloat(line.quantity)
    const vatAmount = netAmount * (line.vatRate / 100)

    // Use account code from mapping, or generate default
    let accountCode = 'EXPENSE'
    if (line.accountHint) {
      const account = accountMap.get(line.accountHint)
      accountCode = account?.code || `ACC_${line.accountHint}`
    }

    return {
      account_code: accountCode,
      description: line.description,
      net: netAmount.toFixed(2),
      vat_rate: line.vatRate,
      vat_amount: vatAmount.toFixed(2)
    }
  })

  const payload: ExportPayload = {
    entity_id: entityId,
    document_id: documentId,
    supplier: {
      name: extraction.supplier.name,
      vat: extraction.supplier.vat,
      iban: extraction.supplier.iban
    },
    lines: exportLines,
    total_gross: extraction.invoice.gross,
    invoice_number: extraction.invoice.number,
    issue_date: extraction.invoice.issueDate,
    due_date: extraction.invoice.dueDate,
    currency: extraction.invoice.currency
  }

  return payload
}

/**
 * Creates domain event for export processing queue
 */
export function createExportDomainEvent(
  entityId: number,
  documentId: number,
  exportPayload: ExportPayload
) {
  // Generate dedupe key for idempotency
  const dedupeKey = `export_${documentId}_${Date.now()}`

  return {
    aggregate_id: documentId,
    aggregate_type: 'document',
    entity_id: entityId,
    event_type: 'export_requested',
    event_data: {
      document_id: documentId,
      export_payload: exportPayload,
      requested_at: new Date().toISOString(),
      dedupe_key: dedupeKey
    }
  }
}

/**
 * Creates export jobs for enabled connectors after domain event is created
 */
export async function createExportJobs(
  supabase: ExtendedSupabaseClient,
  entityId: number,
  eventId: number
): Promise<void> {
  try {
    // Get enabled connector configurations for this entity
    const { data: configs, error: configError } = await (supabase as ExtendedSupabaseClient)
      .from('connector_configs')
      .select('*')
      .eq('entity_id', entityId)

    if (configError) {
      console.error('Failed to fetch connector configs:', configError)
      return
    }

    if (!configs || configs.length === 0) {
      console.log(`No connector configs found for entity ${entityId}`)
      return
    }

    // Create export jobs for each enabled connector
    const jobPromises = (configs as ConnectorConfig[])
      .filter(config => config.config?.enabled !== false) // Default to enabled
      .map(async (config) => {
        // Generate placeholder content hash (will be updated by worker)
        const contentHash = `pending_${entityId}_${config.connector}_${Date.now()}`

        const { error: jobError } = await (supabase as ExtendedSupabaseClient)
          .from('export_jobs')
          .insert({
            entity_id: entityId,
            event_id: eventId,
            connector: config.connector,
            status: 'queued',
            content_hash: contentHash,
            attempts: 0
          })

        if (jobError) {
          console.error(`Failed to create export job for connector ${config.connector}:`, jobError)
        } else {
          console.log(`Created export job for entity ${entityId}, connector ${config.connector}`)
        }
      })

    await Promise.all(jobPromises)
  } catch (error) {
    console.error('Failed to create export jobs:', error)
  }
}

/**
 * Generates unique export reference for tracking
 */
export function generateExportRef(documentId: number): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `export_${timestamp}_${documentId}_${random}`
}
