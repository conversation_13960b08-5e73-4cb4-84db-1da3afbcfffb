import Fastify from 'fastify'
import cors from '@fastify/cors'
import env from '@fastify/env'
import multipart from '@fastify/multipart'


// Import plugins
import supabasePlugin from './plugins/supabase.js'
import authPlugin from './plugins/auth.js'
import rateLimitPlugin from './plugins/rate-limit.js'

// Import routes
import healthRoutes from './routes/health.js'
import rpcRoutes from './routes/rpc.js'
import inboxRoutes from './routes/inbox.js'
import bankTransactionRoutes from './routes/bank-transactions.js'
import bankImportRoutes from './routes/bank-import.js'
import arRoutes from './routes/ar'
import vatRoutes from './routes/vat.js'
import integrationsRoutes from './routes/integrations.js'
import aiSuggestRoutes from './routes/ai-suggest.js'
import emailInRoutes from './routes/email-in.js'

const envSchema = {
  type: 'object',
  required: ['BFF_PORT', 'SUPABASE_URL', 'SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_ROLE_KEY', 'INTERNAL_KEY'],
  properties: {
    BFF_PORT: {
      type: 'string',
      default: '3001'
    },
    SUPABASE_URL: {
      type: 'string'
    },
    SUPABASE_ANON_KEY: {
      type: 'string'
    },
    SUPABASE_SERVICE_ROLE_KEY: {
      type: 'string'
    },
    INTERNAL_KEY: {
      type: 'string'
    },
    EMAIL_IN_PROVIDER: {
      type: 'string',
      default: 'test'
    },
    EMAIL_IN_SIGNING_SECRET: {
      type: 'string',
      default: 'test-secret'
    },
    EMAIL_IN_DOMAIN: {
      type: 'string',
      default: 'in.ledgerly.app'
    },
    MAX_ATTACHMENTS: {
      type: 'string',
      default: '10'
    },
    MAX_ATTACHMENT_MB: {
      type: 'string',
      default: '25'
    }
  }
}

async function buildApp() {
  const loggerOptions = process.env.NODE_ENV === 'development'
    ? {
        level: 'debug' as const,
        transport: {
          target: 'pino-pretty',
          options: {
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        }
      }
    : {
        level: 'info' as const
      }

  const fastify = Fastify({
    logger: loggerOptions
  })

  // Register environment variables
  await fastify.register(env, {
    schema: envSchema,
    dotenv: true
  })

  // Register multipart support for file uploads
  await fastify.register(multipart, {
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB limit
    }
  })

  // Register CORS
  await fastify.register(cors, {
    origin: process.env.NODE_ENV === 'development'
      ? ['http://localhost:3000', 'http://127.0.0.1:3000']
      : false,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Internal-Key']
  })

  // Register plugins
  await fastify.register(rateLimitPlugin, {
    max: 100, // 100 requests per window
    windowMs: 60 * 1000, // 1 minute
    skipSuccessfulRequests: false
  })

  await fastify.register(supabasePlugin, {
    url: fastify.config.SUPABASE_URL,
    anonKey: fastify.config.SUPABASE_ANON_KEY,
    serviceRoleKey: fastify.config.SUPABASE_SERVICE_ROLE_KEY
  })

  await fastify.register(authPlugin, {
    internalKey: fastify.config.INTERNAL_KEY
  })

  // Register routes
  await fastify.register(healthRoutes)
  await fastify.register(rpcRoutes)
  await fastify.register(inboxRoutes)
  await fastify.register(bankTransactionRoutes)
  await fastify.register(bankImportRoutes)
  await fastify.register(arRoutes)
  await fastify.register(vatRoutes)
  await fastify.register(integrationsRoutes)
  await fastify.register(aiSuggestRoutes)
  await fastify.register(emailInRoutes)

  // Global error handler
  fastify.setErrorHandler(async (error, request, reply) => {
    // Log full error details internally (with sanitized request info)
    const sanitizedRequest = {
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip
      // Don't log sensitive headers
    }
    fastify.log.error({ error, request: sanitizedRequest }, 'Request error')

    // Handle validation errors
    if (error.validation) {
      void reply.status(400).send({
        success: false,
        error: 'Validation failed',
        details: process.env.NODE_ENV === 'development' ? error.validation : undefined
      })
      return
    }

    // Handle rate limit errors (already handled by plugin but just in case)
    if (error.statusCode === 429) {
      void reply.status(429).send({
        success: false,
        error: 'Too Many Requests'
      })
      return
    }

    // Handle other errors - be careful not to leak internal information
    const statusCode = error.statusCode || 500

    let errorMessage: string
    if (statusCode < 500) {
      // Client errors - safe to show message
      errorMessage = error.message || 'Bad Request'
    } else {
      // Server errors - only show generic message in production
      errorMessage = process.env.NODE_ENV === 'development'
        ? error.message || 'Internal Server Error'
        : 'Internal Server Error'
    }

    void reply.status(statusCode).send({
      success: false,
      error: errorMessage
    })
  })

  // Not found handler
  fastify.setNotFoundHandler(async (_request, reply) => {
    void reply.status(404).send({
      success: false,
      error: 'Route not found'
    })
  })

  return fastify
}

async function start() {
  try {
    const fastify = await buildApp()

    const port = parseInt(fastify.config.BFF_PORT, 10)
    const host = process.env.NODE_ENV === 'production' ? '0.0.0.0' : '127.0.0.1'

    await fastify.listen({ port, host })

    fastify.log.info(`🚀 BFF server listening on ${host}:${port}`)

    // Graceful shutdown
    const signals: string[] = ['SIGINT', 'SIGTERM']
    signals.forEach((signal) => {
      process.on(signal, () => {
        void (async () => {
          fastify.log.info(`Received ${signal}, shutting down gracefully`)
          await fastify.close()
          process.exit(0)
        })()
      })
    })

  } catch (error) {
    console.error('Error starting server:', error)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  void start()
}

export { buildApp }
