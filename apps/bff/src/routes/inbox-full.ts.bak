import { FastifyInstance } from 'fastify'
import crypto from 'crypto'

export default async function inboxRoutes(fastify: FastifyInstance) {
  // Helper function to verify entity membership
  const verifyEntityAccess = async (userId: string, entityId: number, requiredRoles?: string[]) => {
    const { data: membership, error } = await fastify.supabase
      .from('entity_memberships')
      .select('role')
      .eq('entity_id', entityId)
      .eq('user_id', userId)
      .single()
    
    if (error || !membership) {
      throw new Error('Access denied: not a member of this entity')
    }

    if (requiredRoles && !requiredRoles.includes(membership.role)) {
      throw new Error(`Access denied: requires role ${requiredRoles.join(' or ')}`)
    }

    return membership
  }

  // Helper function to check feature flags
  const checkFeatureFlag = async (entityId: number, flag: string) => {
    const { data } = await fastify.supabase
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', flag)
      .single()
    
    return { enabled: data?.enabled || false, config: data?.config || {} }
  }

  // Upload document endpoint
  fastify.post('/entities/:entityId/documents', async (request, reply) => {
    try {
      const entityId = parseInt((request.params as any).entityId)
      const { path, mime_type, source = 'upload' } = request.body as any

      // For now, assume user is authenticated and get user ID from headers
      // In production, this would come from proper JWT authentication
      const userId = request.headers['x-user-id'] as string
      if (!userId) {
        return reply.code(401).send({ success: false, error: 'Not authenticated' })
      }

      // Verify entity access
      await verifyEntityAccess(userId, entityId, ['owner', 'admin', 'accountant', 'bookkeeper'])

      // Check feature flag
      const featureFlag = await checkFeatureFlag(entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        return reply.code(403).send({ success: false, error: 'Inbox feature not enabled' })
      }

      // Insert document record
      const { data: document, error } = await fastify.supabase
        .from('documents')
        .insert({
          entity_id: entityId,
          path,
          mime_type,
          source,
          status: 'uploaded'
        })
        .select('id')
        .single()

      if (error) {
        fastify.log.error(`Failed to insert document: ${error.message}`)
        return reply.code(500).send({ success: false, error: 'Failed to create document' })
      }

      // Generate signed URL for workers
      const { data: signedUrl } = await fastify.supabase.storage
        .from('inbox')
        .createSignedUrl(path, 3600)

      if (!signedUrl) {
        return reply.code(500).send({ success: false, error: 'Failed to generate file URL' })
      }

      // Call workers to process document
      try {
        const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'
        const workersResponse = await fetch(`${workersUrl}/process-inbox-document`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            document_id: document.id,
            entity_id: entityId,
            file_url: signedUrl.signedUrl
          })
        })

        if (!workersResponse.ok) {
          throw new Error(`Workers responded with status ${workersResponse.status}`)
        }

        const workersData = await workersResponse.json()
        fastify.log.info(`Document processing started with task ${workersData.task_id}`)

      } catch (workersError) {
        fastify.log.error(`Failed to start processing: ${workersError}`)
        // Document is created but processing failed
      }

      return {
        success: true,
        data: {
          document_id: document.id,
          status: 'uploaded',
          message: 'Document uploaded and processing started'
        }
      }

    } catch (error: any) {
      fastify.log.error(`Upload failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // List documents
  fastify.get('/entities/:entityId/documents', async (request, reply) => {
    try {
      const entityId = parseInt((request.params as any).entityId)
      const query = request.query as any
      const { status, page = 1, limit = 20 } = query

      const userId = request.headers['x-user-id'] as string
      if (!userId) {
        return reply.code(401).send({ success: false, error: 'Not authenticated' })
      }

      // Verify entity access
      await verifyEntityAccess(userId, entityId)

      // Check feature flag
      const featureFlag = await checkFeatureFlag(entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        return reply.code(403).send({ success: false, error: 'Inbox feature not enabled' })
      }

      // Build query
      let queryBuilder = fastify.supabase
        .from('documents')
        .select(`
          id, entity_id, path, mime_type, source, status, confidence,
          created_at, updated_at, error_msg,
          extraction
        `)
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (status) {
        queryBuilder = queryBuilder.eq('status', status)
      }

      // Apply pagination
      const offset = (page - 1) * limit
      queryBuilder = queryBuilder.range(offset, offset + limit - 1)

      const { data: documents, error, count } = await queryBuilder

      if (error) {
        fastify.log.error(`Failed to fetch documents: ${error.message}`)
        return reply.code(500).send({ success: false, error: 'Failed to fetch documents' })
      }

      const totalPages = count ? Math.ceil(count / limit) : 0

      return {
        success: true,
        data: {
          documents: documents || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            pages: totalPages
          }
        }
      }

    } catch (error: any) {
      fastify.log.error(`List documents failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Get document detail
  fastify.get('/documents/:documentId', async (request, reply) => {
    try {
      const documentId = parseInt((request.params as any).documentId)

      const userId = request.headers['x-user-id'] as string
      if (!userId) {
        return reply.code(401).send({ success: false, error: 'Not authenticated' })
      }

      // Fetch document
      const { data: document, error } = await fastify.supabase
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        return reply.code(404).send({ success: false, error: 'Document not found' })
      }

      // Verify entity access
      await verifyEntityAccess(userId, document.entity_id)

      return {
        success: true,
        data: document
      }

    } catch (error: any) {
      fastify.log.error(`Get document failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Confirm document - simplified version
  fastify.post('/documents/:documentId/confirm', async (request, reply) => {
    try {
      const documentId = parseInt((request.params as any).documentId)

      const userId = request.headers['x-user-id'] as string
      if (!userId) {
        return reply.code(401).send({ success: false, error: 'Not authenticated' })
      }

      // Fetch document
      const { data: document, error } = await fastify.supabase
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        return reply.code(404).send({ success: false, error: 'Document not found' })
      }

      // Verify entity access
      await verifyEntityAccess(userId, document.entity_id, ['owner', 'admin', 'accountant', 'bookkeeper'])

      if (document.status === 'posted' || document.status === 'exported') {
        return reply.code(400).send({ success: false, error: 'Document already processed' })
      }

      // For now, just mark as confirmed - full implementation would handle ledger vs assist mode
      const { error: updateError } = await fastify.supabase
        .from('documents')
        .update({
          status: 'confirmed',
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId)

      if (updateError) {
        return reply.code(500).send({ success: false, error: 'Failed to confirm document' })
      }

      return {
        success: true,
        data: {
          document_id: documentId,
          status: 'confirmed',
          message: 'Document confirmed successfully'
        }
      }

    } catch (error: any) {
      fastify.log.error(`Confirm document failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Check feature flag
  fastify.get('/entities/:entityId/feature-flags', async (request, reply) => {
    try {
      const entityId = parseInt((request.params as any).entityId)
      const { flag } = request.query as any

      const userId = request.headers['x-user-id'] as string
      if (!userId) {
        return reply.code(401).send({ success: false, error: 'Not authenticated' })
      }

      // Verify entity access
      await verifyEntityAccess(userId, entityId)

      const featureFlag = await checkFeatureFlag(entityId, flag)

      return {
        success: true,
        data: featureFlag
      }

    } catch (error: any) {
      fastify.log.error(`Feature flag check failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })
}