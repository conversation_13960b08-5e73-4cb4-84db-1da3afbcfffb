/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { FastifyPluginCallback, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import type { ExtendedSupabaseClient } from '../types'
import '../types' // Import type extensions

/**
 * Track F: Integrations API Routes
 *
 * Provides endpoints for managing export jobs, connector configurations,
 * and integration status/operations.
 */

// =============================================================================
// REQUEST/RESPONSE SCHEMAS
// =============================================================================

const ExportJobsQuerySchema = z.object({
  status: z.enum(['queued', 'delivering', 'delivered', 'failed', 'skipped']).optional(),
  connector: z.enum(['winbooks_sftp', 'email']).optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0)
})


const DryRunSchema = z.object({
  eventId: z.number().int().positive().optional(),
  mockData: z.object({
    source: z.enum(['AP', 'AR']),
    doc_number: z.string(),
    entity_id: z.number().int().positive(),
    export_payload: z.record(z.unknown())
  }).optional()
})

const ConnectorConfigSchema = z.object({
  connector: z.enum(['winbooks_sftp', 'email']),
  config: z.object({
    // SFTP config
    host: z.string().optional(),
    port: z.number().int().min(1).max(65535).optional(),
    username: z.string().optional(),
    remote_dir: z.string().optional(),

    // Email config
    to_email: z.string().email().optional(),

    // Shared settings
    enabled: z.boolean().default(true),
    dry_run: z.boolean().default(false)
  })
})

// =============================================================================
// ROUTE HANDLERS
// =============================================================================

/**
 * GET /entities/:entityId/exports/jobs
 * List export jobs for an entity with optional filtering
 */
async function getExportJobs(request: FastifyRequest, reply: FastifyReply) {
  const { entityId } = request.params as { entityId: string }
  const query = ExportJobsQuerySchema.parse(request.query)

  const entityIdNum = parseInt(entityId, 10)
  if (isNaN(entityIdNum)) {
    request.log.warn({ entityId }, 'Invalid entity ID provided to getExportJobs')
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID'
    })
  }

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  request.log.info({
    entityId: entityIdNum,
    filters: { status: query.status, connector: query.connector },
    pagination: { limit: query.limit, offset: query.offset }
  }, 'Fetching export jobs')

  // Query export jobs directly (using typed client)
  const { data: jobs, error } = await supabaseClient
    .from('export_jobs')
    .select(`
      *,
      domain_events!inner(event_type, event_data)
    `)
    .eq('entity_id', entityIdNum)
    .eq(query.status ? 'status' : '', query.status || '')
    .eq(query.connector ? 'connector' : '', query.connector || '')
    .order('created_at', { ascending: false })
    .range(query.offset, query.offset + query.limit - 1)

  if (error) {
    request.log.error({ error, entityId }, 'Failed to fetch export jobs')
    return reply.status(500).send({
      success: false,
      error: 'Failed to fetch export jobs'
    })
  }

  // Get total count for pagination
  const { count: total } = await supabaseClient
    .from('export_jobs')
    .select('*', { count: 'exact', head: true })
    .eq('entity_id', entityIdNum)

  request.log.info({
    entityId: entityIdNum,
    jobCount: jobs?.length || 0,
    total: total || 0
  }, 'Export jobs fetched successfully')

  await reply.send({
    jobs: jobs || [],
    pagination: {
      limit: query.limit,
      offset: query.offset,
      total: total || 0,
      has_more: (query.offset + query.limit) < (total || 0)
    }
  })
}

/**
 * POST /entities/:entityId/exports/jobs/:jobId/retry
 * Retry a failed export job
 */
async function retryExportJob(request: FastifyRequest, reply: FastifyReply) {
  const { entityId, jobId } = request.params as { entityId: string; jobId: string }

  const entityIdNum = parseInt(entityId, 10)
  const jobIdNum = parseInt(jobId, 10)

  if (isNaN(entityIdNum) || isNaN(jobIdNum)) {
    request.log.warn({ entityId, jobId }, 'Invalid entity ID or job ID provided to retryExportJob')
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID or job ID'
    })
  }

  request.log.info({ entityId: entityIdNum, jobId: jobIdNum }, 'Retrying export job')

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  // Use the RPC function we created in the migration
  const { data: result, error } = await supabaseClient
    .rpc('rpc_retry_export_job', { p_job_id: jobIdNum })

  if (error) {
    request.log.error({ error, entityId, jobId }, 'Failed to retry export job')
    return reply.status(500).send({
      success: false,
      error: 'Failed to retry export job'
    })
  }

  if (!result) {
    request.log.warn({ entityId: entityIdNum, jobId: jobIdNum }, 'Export job not found or cannot be retried')
    return reply.status(404).send({
      success: false,
      error: 'Export job not found or cannot be retried'
    })
  }

  request.log.info({ entityId: entityIdNum, jobId: jobIdNum }, 'Export job retry queued successfully')

  await reply.send({
    success: true,
    message: 'Export job queued for retry',
    job_id: jobIdNum
  })
}

/**
 * POST /entities/:entityId/exports/dry-run
 * Generate export bundle in dry-run mode for testing
 */
async function createDryRunExport(request: FastifyRequest, reply: FastifyReply) {
  const { entityId } = request.params as { entityId: string }
  const body = DryRunSchema.parse(request.body)

  const entityIdNum = parseInt(entityId, 10)
  if (isNaN(entityIdNum)) {
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID'
    })
  }

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  // Validate input - either eventId or mockData required
  if (body.eventId) {
    // Validate existing event exists
    const { data: event, error } = await supabaseClient
      .from('domain_events')
      .select('*')
      .eq('id', body.eventId)
      .eq('entity_id', entityIdNum)
      .single()

    if (error || !event) {
      return reply.status(404).send({
      success: false,
      error: 'Export event not found'
    })
  }
  } else if (!body.mockData) {
    return reply.status(400).send({
      success: false,
      error: 'Either eventId or mockData must be provided'
    })
  }

  // Queue dry-run job (this would trigger the worker)
  const { data: job, error: jobError } = await supabaseClient
    .from('export_jobs')
    .insert({
      entity_id: entityIdNum,
      event_id: body.eventId || 0, // Use 0 for mock events
      connector: 'winbooks_sftp', // Default for dry-run
      status: 'queued',
      content_hash: `dry-run-${Date.now()}`,
      attempts: 0
    })
    .select()
    .single()

  if (jobError) {
    request.log.error({ error: jobError, entityId }, 'Failed to create dry-run job')
    return reply.status(400).send({
      success: false,
      error: 'Failed to create dry-run export'
    })
  }

  // In a real implementation, this would trigger the worker task
  // For now, return the job details

  await reply.send({
    success: true,
    message: 'Dry-run export job created',
    job: job,
    dry_run: true
  })
}

/**
 * GET /entities/:entityId/exports/summary
 * Get export summary statistics for an entity
 */
async function getExportSummary(request: FastifyRequest, reply: FastifyReply) {
  const { entityId } = request.params as { entityId: string }

  const entityIdNum = parseInt(entityId, 10)
  if (isNaN(entityIdNum)) {
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID'
    })
  }

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  // Use the RPC function we created in the migration (type assertion for new RPC)
  const { data: summary, error } = await supabaseClient
    .rpc('rpc_export_jobs_summary', { p_entity_id: entityIdNum })

  if (error) {
    request.log.error({ error, entityId }, 'Failed to fetch export summary')
    return reply.status(400).send({
      success: false,
      error: 'Failed to fetch export summary'
    })
  }

  await reply.send({
    entity_id: entityIdNum,
    summary: summary || []
  })
}

/**
 * GET /entities/:entityId/exports/connectors
 * Get connector configurations for an entity
 */
async function getConnectorConfigs(request: FastifyRequest, reply: FastifyReply) {
  const { entityId } = request.params as { entityId: string }

  const entityIdNum = parseInt(entityId, 10)
  if (isNaN(entityIdNum)) {
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID'
    })
  }

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  const { data: configs, error } = await supabaseClient
    .from('connector_configs')
    .select('*')
    .eq('entity_id', entityIdNum)

  if (error) {
    request.log.error({ error, entityId }, 'Failed to fetch connector configs')
    return reply.status(400).send({
      success: false,
      error: 'Failed to fetch connector configurations'
    })
  }

  await reply.send({
    entity_id: entityIdNum,
    connectors: configs || []
  })
}

/**
 * PUT /entities/:entityId/exports/connectors
 * Update connector configuration for an entity
 */
async function updateConnectorConfig(request: FastifyRequest, reply: FastifyReply) {
  const { entityId } = request.params as { entityId: string }
  const body = ConnectorConfigSchema.parse(request.body)

  const entityIdNum = parseInt(entityId, 10)
  if (isNaN(entityIdNum)) {
    return reply.status(400).send({
      success: false,
      error: 'Invalid entity ID'
    })
  }

  // Use user-scoped client for RLS enforcement
  const supabaseClient = (request.userSupabase || request.server.supabase) as ExtendedSupabaseClient

  // Upsert connector configuration
  const { data: config, error } = await supabaseClient
    .from('connector_configs')
    .upsert({
      entity_id: entityIdNum,
      connector: body.connector,
      config: body.config,
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    request.log.error({ error, entityId, connector: body.connector }, 'Failed to update connector config')
    return reply.status(400).send({
      success: false,
      error: 'Failed to update connector configuration'
    })
  }

  await reply.send({
    success: true,
    message: 'Connector configuration updated',
    config
  })
}

// =============================================================================
// PLUGIN REGISTRATION
// =============================================================================

const integrationsRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // Export jobs management
  fastify.get('/entities/:entityId/exports/jobs', getExportJobs)
  fastify.post('/entities/:entityId/exports/jobs/:jobId/retry', retryExportJob)
  fastify.post('/entities/:entityId/exports/dry-run', createDryRunExport)

  // Summary and status endpoints
  fastify.get('/entities/:entityId/exports/summary', getExportSummary)

  // Connector configuration
  fastify.get('/entities/:entityId/exports/connectors', getConnectorConfigs)
  fastify.put('/entities/:entityId/exports/connectors', updateConnectorConfig)

}

export default integrationsRoutes
