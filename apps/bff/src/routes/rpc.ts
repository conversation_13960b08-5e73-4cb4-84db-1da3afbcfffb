/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback } from 'fastify'
import {
  PostJournalSchema,
  SwitchModeSchema,
  GrantEntityRoleSchema,
  ApiResponse,
  Json
} from '@belbooks/types'
import { z } from 'zod'
import '../types' // Import type extensions

// Type definitions for balance calculation
interface AccountBalance {
  account_id: number
  code: string
  name: string
  account_type: string
  normal_balance: string
  debit_total: number
  credit_total: number
  balance: number
}

interface JournalLineWithAccount {
  account_id: number
  debit_amount: number | null
  credit_amount: number | null
  accounts: {
    code: string
    name: string
    account_type: string
    normal_balance: string
  }
}

// Query parameter schemas
const EntityParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10))
})

const TrialBalanceQuerySchema = z.object({
  asOfDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional()
})

const JournalQuerySchema = z.object({
  limit: z.string().transform(val => parseInt(val, 10)).optional().default('50'),
  offset: z.string().transform(val => parseInt(val, 10)).optional().default('0')
})

const rpcRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // RPC Routes
  fastify.post('/rpc/post-journal', async (request, reply) => {
    try {
      const validatedBody = PostJournalSchema.parse(request.body)

      // Use user-scoped client if available, otherwise service client
      const supabaseClient = request.userSupabase || fastify.supabase

      // Call Supabase RPC directly
      const { data, error } = await supabaseClient.rpc('rpc_post_journal', {
        p_entity: validatedBody.entity_id,
        p_type: validatedBody.journal_type,
        p_description: validatedBody.description,
        p_date: validatedBody.transaction_date,
        p_lines: validatedBody.lines,
        ...(validatedBody.reference && { p_reference: validatedBody.reference })
      })

      if (error) {
        throw new Error(`Failed to post journal: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: { journalId: data }
      }

      void reply.status(201).send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })

  fastify.post('/rpc/switch-mode', async (request, reply) => {
    try {
      const validatedBody = SwitchModeSchema.parse(request.body)

      // Use user-scoped client if available, otherwise service client
      const supabaseClient = request.userSupabase || fastify.supabase

      // Call Supabase RPC directly
      const { error } = await supabaseClient.rpc('rpc_switch_mode', {
        p_entity: validatedBody.entity_id,
        p_mode: validatedBody.mode,
        p_config: validatedBody.config as Json
      })

      if (error) {
        throw new Error(`Failed to switch mode: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: { success: true }
      }

      void reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })

  fastify.post('/rpc/grant-role', async (request, reply) => {
    try {
      const validatedBody = GrantEntityRoleSchema.parse(request.body)

      // Use user-scoped client if available, otherwise service client
      const supabaseClient = request.userSupabase || fastify.supabase

      // Call Supabase RPC directly
      const { error } = await supabaseClient.rpc('rpc_grant_entity_role', {
        p_entity: validatedBody.entity_id,
        p_user: validatedBody.user_id,
        p_role: validatedBody.role
      })

      if (error) {
        throw new Error(`Failed to grant role: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: { success: true }
      }

      void reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })

  // Entity-specific routes
  fastify.get('/entities/:id/accounts', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: accounts, error } = await supabaseClient
        .from('accounts')
        .select('id, code, name, account_type, normal_balance, is_active')
        .eq('entity_id', entityId)
        .eq('is_active', true)
        .order('code')

      if (error) {
        throw new Error(`Failed to fetch accounts: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: accounts || []
      }

      void reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })

  fastify.get('/entities/:id/journals', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const { limit, offset } = JournalQuerySchema.parse(request.query)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: journals, error } = await supabaseClient
        .from('journals')
        .select(`
          id,
          journal_type,
          reference,
          description,
          transaction_date,
          created_at,
          is_balanced,
          journal_lines (
            id,
            account_id,
            description,
            debit_amount,
            credit_amount,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('entity_id', entityId)
        .order('transaction_date', { ascending: false })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        throw new Error(`Failed to fetch journals: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: journals || []
      }

      void reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })

  fastify.get('/entities/:id/trial-balance', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const { asOfDate } = TrialBalanceQuerySchema.parse(request.query)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      let query = supabaseClient
        .from('journal_lines')
        .select(`
          account_id,
          accounts!inner (
            code,
            name,
            account_type,
            normal_balance
          ),
          debit_amount,
          credit_amount,
          journals!inner (
            entity_id,
            transaction_date
          )
        `)
        .eq('journals.entity_id', entityId)

      if (asOfDate) {
        query = query.lte('journals.transaction_date', asOfDate)
      }

      const { data, error } = await query

      if (error) {
        throw new Error(`Failed to fetch trial balance: ${error.message}`)
      }

      // Calculate balances by account
      const balances = new Map<number, AccountBalance>()

      data?.forEach((line: JournalLineWithAccount) => {
        const accountId = line.account_id
        const account = line.accounts

        if (!balances.has(accountId)) {
          balances.set(accountId, {
            account_id: accountId,
            code: account.code,
            name: account.name,
            account_type: account.account_type,
            normal_balance: account.normal_balance,
            debit_total: 0,
            credit_total: 0,
            balance: 0
          })
        }

        const balance = balances.get(accountId)!
        balance.debit_total += Number(line.debit_amount || 0)
        balance.credit_total += Number(line.credit_amount || 0)
      })

      // Calculate final balances based on account type
      const result = Array.from(balances.values()).map((balance: AccountBalance) => {
        const netBalance = balance.debit_total - balance.credit_total

        // For normal debit accounts (assets, expenses), positive means debit balance
        // For normal credit accounts (liabilities, equity, revenue), negative means credit balance
        if (balance.normal_balance === 'debit') {
          balance.balance = netBalance
        } else {
          balance.balance = -netBalance
        }

        return balance
      })

      const filteredResult = result.filter(balance => Math.abs(balance.balance) > 0.01)

      const response: ApiResponse = {
        success: true,
        data: filteredResult
      }

      void reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }

      void reply.status(400).send(response)
    }
  })
}

export default rpcRoutes
