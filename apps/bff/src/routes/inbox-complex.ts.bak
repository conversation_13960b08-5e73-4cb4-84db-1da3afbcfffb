import { FastifyInstance, FastifyRequest } from 'fastify'
import { 
  DocumentUploadResponse,
  DocumentListResponse,
  DocumentDetail,
  ConfirmResponse,
  FeatureFlagResponse,
  Suggestion,
  ExtractionResult
} from '@ledgerly/types'
import crypto from 'crypto'

// TypeScript interfaces for request data
interface UploadDocumentBody {
  path: string
  mime_type: string
  source?: 'upload' | 'email' | 'api'
}

interface ConfirmDocumentBody {
  document_id: number
  correction?: ExtractionResult
}

interface EntityParams {
  entityId: string
}

interface DocumentParams {
  documentId: string
}

interface ListQuery {
  status?: string
  page?: number
  limit?: number
}

interface AuthenticatedRequest extends FastifyRequest {
  user: {
    id: string
  }
}

export default async function inboxRoutes(fastify: FastifyInstance) {
  // Helper function to verify entity membership with proper permissions
  const verifyEntityAccess = async (userId: string, entityId: number, requiredRoles?: string[]) => {
    const client = fastify.supabase
    
    const { data: membership, error } = await client
      .from('entity_memberships')
      .select('role')
      .eq('entity_id', entityId)
      .eq('user_id', userId)
      .single()
    
    if (error || !membership) {
      throw new Error('Access denied: not a member of this entity')
    }

    if (requiredRoles && !requiredRoles.includes(membership.role)) {
      throw new Error(`Access denied: requires role ${requiredRoles.join(' or ')}`)
    }

    return membership
  }

  // Helper function to check feature flags
  const checkFeatureFlag = async (entityId: number, flag: string) => {
    const client = fastify.supabase
    
    const { data } = await client
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', flag)
      .single()
    
    return { enabled: data?.enabled || false, config: data?.config || {} }
  }

  // Upload document endpoint - registers document and triggers processing
  fastify.post<{
    Params: EntityParams
    Body: UploadDocumentBody
  }>('/entities/:entityId/documents', async (request, reply) => {
    const req = request as AuthenticatedRequest
    const entityId = parseInt(request.params.entityId)
    const body = request.body as UploadDocumentBody
    const { path, mime_type, source = 'upload' } = body

    try {
      fastify.log.info(`Processing document upload for entity ${entityId}`)

      // Verify entity access and permissions
      await verifyEntityAccess(req.user.id, entityId, ['owner', 'admin', 'accountant', 'bookkeeper'])

      // Check if inbox is enabled for this entity
      const featureFlag = await checkFeatureFlag(entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        return reply.code(403).send({
          success: false,
          error: 'Inbox feature is not enabled for this entity'
        })
      }

      // Use user-scoped client for RLS enforcement
      const userClient = fastify.supabase.auth.admin.setSession({
        access_token: req.headers.authorization?.replace('Bearer ', '') || ''
      })

      // Insert document record
      const { data: document, error } = await userClient.from('documents')
        .insert({
          entity_id: entityId,
          path,
          mime_type,
          source,
          status: 'uploaded'
        })
        .select('id')
        .single()

      if (error) {
        fastify.log.error(`Failed to insert document: ${error.message}`)
        return reply.code(500).send({
          success: false,
          error: 'Failed to create document record'
        })
      }

      // Generate signed URL for workers to download file
      const { data: signedUrl } = await fastify.supabase.client.storage
        .from('inbox')
        .createSignedUrl(path, 3600) // 1 hour expiry

      if (!signedUrl) {
        return reply.code(500).send({
          success: false,
          error: 'Failed to generate file access URL'
        })
      }

      // Call workers to process document
      try {
        const workersResponse = await fetch(`${process.env.WORKERS_URL}/process-inbox-document`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            document_id: document.id,
            entity_id: entityId,
            file_url: signedUrl.signedUrl
          })
        })

        if (!workersResponse.ok) {
          throw new Error(`Workers responded with status ${workersResponse.status}`)
        }

        const workersData = await workersResponse.json()
        fastify.log.info(`Document processing started with task ${workersData.task_id}`)

      } catch (workersError) {
        fastify.log.error(`Failed to start processing: ${workersError}`)
        // Document is created but processing failed - this is still a partial success
      }

      return {
        success: true,
        data: {
          document_id: document.id,
          status: 'uploaded',
          message: 'Document uploaded and processing started'
        } as DocumentUploadResponse
      }

    } catch (error) {
      fastify.log.error(`Upload failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // List documents with filtering and pagination
  fastify.get<{
    Params: { entityId: string }
    Querystring: typeof ListQuery.static
  }>('/entities/:entityId/documents', {
    schema: {
      params: EntityParam,
      querystring: ListQuery
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const req = request as AuthenticatedRequest
    const entityId = parseInt(request.params.entityId)
    const { status, page = 1, limit = 20 } = request.query

    try {
      // Verify entity access
      await verifyEntityAccess(req.user.id, entityId)

      // Check feature flag
      const featureFlag = await checkFeatureFlag(entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        return reply.code(403).send({
          success: false,
          error: 'Inbox feature is not enabled for this entity'
        })
      }

      // Build query with RLS enforcement
      let query = fastify.supabase.client
        .from('documents')
        .select(`
          id, entity_id, path, mime_type, source, status, confidence,
          created_at, updated_at, error_msg,
          extraction->supplier->>name as supplier_name,
          extraction->invoice->>number as invoice_number,
          extraction->invoice->>issue_date as invoice_date,
          extraction->invoice->>gross as gross_amount
        `)
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      // Apply pagination
      const offset = (page - 1) * limit
      query = query.range(offset, offset + limit - 1)

      const { data: documents, error, count } = await query

      if (error) {
        fastify.log.error(`Failed to fetch documents: ${error.message}`)
        return reply.code(500).send({
          success: false,
          error: 'Failed to fetch documents'
        })
      }

      const totalPages = count ? Math.ceil(count / limit) : 0

      return {
        success: true,
        data: {
          documents: documents || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            pages: totalPages
          }
        } as DocumentListResponse
      }

    } catch (error) {
      fastify.log.error(`List documents failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Get document detail with full extraction and suggestion
  fastify.get<{
    Params: { documentId: string }
  }>('/documents/:documentId', {
    schema: {
      params: DocumentParam
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const req = request as AuthenticatedRequest
    const documentId = parseInt(request.params.documentId)

    try {
      // Fetch document with RLS enforcement
      const { data: document, error } = await fastify.supabase.client
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        return reply.code(404).send({
          success: false,
          error: 'Document not found or access denied'
        })
      }

      // Verify entity access
      await verifyEntityAccess(req.user.id, document.entity_id)

      return {
        success: true,
        data: document as DocumentDetail
      }

    } catch (error) {
      fastify.log.error(`Get document failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Generate suggestion from extraction
  fastify.post<{
    Params: { documentId: string }
  }>('/documents/:documentId/suggest', {
    schema: {
      params: DocumentParam
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const req = request as AuthenticatedRequest
    const documentId = parseInt(request.params.documentId)

    try {
      // Fetch document
      const { data: document, error } = await fastify.supabase.client
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        return reply.code(404).send({
          success: false,
          error: 'Document not found or access denied'
        })
      }

      // Verify entity access and permissions
      await verifyEntityAccess(req.user.id, document.entity_id, ['owner', 'admin', 'accountant', 'bookkeeper'])

      if (!document.extraction) {
        return reply.code(400).send({
          success: false,
          error: 'Document has not been extracted yet'
        })
      }

      // Build balanced suggestion from extraction
      // This is a simplified version - in production would be more sophisticated
      const extraction = document.extraction as ExtractionResult
      const grossAmount = parseFloat(extraction.invoice.gross)
      const vatAmount = parseFloat(extraction.invoice.vat)
      const netAmount = parseFloat(extraction.invoice.net)

      // Fetch default accounts for this entity
      const { data: accounts } = await fastify.supabase.client
        .from('accounts')
        .select('id, code, account_type')
        .eq('entity_id', document.entity_id)
        .eq('is_active', true)

      const expenseAccount = accounts?.find(a => a.account_type === 'expense' && a.code.startsWith('6'))
      const vatAccount = accounts?.find(a => a.account_type === 'asset' && a.code.includes('VAT'))
      const payableAccount = accounts?.find(a => a.account_type === 'liability' && a.code.includes('payable'))

      if (!expenseAccount || !payableAccount) {
        return reply.code(500).send({
          success: false,
          error: 'Could not find required accounts for suggestion'
        })
      }

      const suggestion: Suggestion = {
        journalDate: extraction.invoice.issueDate,
        reference: extraction.invoice.number,
        description: `Purchase from ${extraction.supplier.name}`,
        lines: [
          {
            accountId: expenseAccount.id,
            debit: netAmount.toFixed(2),
            credit: "0",
            memo: extraction.lines[0]?.description || 'Expense'
          }
        ]
      }

      // Add VAT line if applicable
      if (vatAmount > 0 && vatAccount) {
        suggestion.lines.push({
          accountId: vatAccount.id,
          debit: vatAmount.toFixed(2),
          credit: "0",
          memo: 'Input VAT'
        })
      }

      // Add payable line (credit)
      suggestion.lines.push({
        accountId: payableAccount.id,
        debit: "0",
        credit: grossAmount.toFixed(2),
        memo: `Payable to ${extraction.supplier.name}`
      })

      // Update document with suggestion
      const { error: updateError } = await fastify.supabase.client
        .from('documents')
        .update({
          suggestion: suggestion,
          status: 'suggested',
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId)

      if (updateError) {
        fastify.log.error(`Failed to save suggestion: ${updateError.message}`)
        return reply.code(500).send({
          success: false,
          error: 'Failed to save suggestion'
        })
      }

      return {
        success: true,
        data: suggestion
      }

    } catch (error) {
      fastify.log.error(`Generate suggestion failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Confirm document - post to journal or export
  fastify.post<{
    Params: { documentId: string }
    Body: typeof ConfirmDocumentBody.static
  }>('/documents/:documentId/confirm', {
    schema: {
      params: DocumentParam,
      body: ConfirmDocumentBody
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const req = request as AuthenticatedRequest
    const documentId = parseInt(request.params.documentId)
    const { correction } = request.body

    try {
      // Fetch document
      const { data: document, error } = await fastify.supabase.client
        .from('documents')
        .select('*, entities!inner(operating_modes(mode))')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        return reply.code(404).send({
          success: false,
          error: 'Document not found or access denied'
        })
      }

      // Verify entity access and permissions
      await verifyEntityAccess(req.user.id, document.entity_id, ['owner', 'admin', 'accountant', 'bookkeeper'])

      if (document.status === 'posted' || document.status === 'exported') {
        return reply.code(400).send({
          success: false,
          error: 'Document has already been processed'
        })
      }

      if (!document.suggestion) {
        return reply.code(400).send({
          success: false,
          error: 'Document must have a suggestion before confirming'
        })
      }

      // Apply correction if provided
      if (correction) {
        // Update extraction with corrections
        await fastify.supabase.client
          .from('documents')
          .update({ extraction: correction })
          .eq('id', documentId)
        
        // Would need to regenerate suggestion here
      }

      // Get entity operating mode
      const operatingMode = document.entities.operating_modes[0]?.mode || 'assist'

      if (operatingMode === 'ledger') {
        // Post to journal using RPC
        const suggestion = document.suggestion as Suggestion
        const { data: journalResult, error: journalError } = await fastify.supabase.client
          .rpc('rpc_post_journal', {
            p_entity: document.entity_id,
            p_type: 'AP',
            p_description: suggestion.description,
            p_date: suggestion.journalDate,
            p_lines: suggestion.lines,
            p_reference: suggestion.reference
          })

        if (journalError) {
          fastify.log.error(`Failed to post journal: ${journalError.message}`)
          return reply.code(500).send({
            success: false,
            error: 'Failed to post to ledger'
          })
        }

        // Update document status
        await fastify.supabase.client
          .from('documents')
          .update({
            status: 'posted',
            posted_journal_id: journalResult,
            updated_at: new Date().toISOString()
          })
          .eq('id', documentId)

        return {
          success: true,
          data: {
            document_id: documentId,
            status: 'posted',
            journal_id: journalResult,
            message: 'Document posted to ledger successfully'
          } as ConfirmResponse
        }

      } else {
        // Assist mode - create export request
        const exportRef = crypto.randomUUID()
        
        // Create export payload (would be sent to outbox/queue in production)
        const exportPayload = {
          entity_id: document.entity_id,
          document_id: documentId,
          // ... full export payload structure
        }

        // Update document status
        await fastify.supabase.client
          .from('documents')
          .update({
            status: 'exported',
            export_ref: exportRef,
            updated_at: new Date().toISOString()
          })
          .eq('id', documentId)

        fastify.log.info(`Export queued for document ${documentId} with ref ${exportRef}`)

        return {
          success: true,
          data: {
            document_id: documentId,
            status: 'exported',
            export_ref: exportRef,
            message: 'Document queued for export successfully'
          } as ConfirmResponse
        }
      }

    } catch (error) {
      fastify.log.error(`Confirm document failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })

  // Check feature flag
  fastify.get<{
    Params: { entityId: string }
    Querystring: { flag: string }
  }>('/entities/:entityId/feature-flags', {
    schema: {
      params: EntityParam,
      querystring: Type.Object({ flag: Type.String() })
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const req = request as AuthenticatedRequest
    const entityId = parseInt(request.params.entityId)
    const { flag } = request.query

    try {
      // Verify entity access
      await verifyEntityAccess(req.user.id, entityId)

      const featureFlag = await checkFeatureFlag(entityId, flag)

      return {
        success: true,
        data: featureFlag as FeatureFlagResponse
      }

    } catch (error) {
      fastify.log.error(`Feature flag check failed: ${error}`)
      return reply.code(error.message.includes('Access denied') ? 403 : 500).send({
        success: false,
        error: error.message || 'Internal server error'
      })
    }
  })
}