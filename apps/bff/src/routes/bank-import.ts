/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback } from 'fastify'
import { BankFileImportSchema, ApiResponse } from '@belbooks/types'
import { importService } from '@belbooks/import-service'
import { z } from 'zod'
import '../types'

// Multipart form schema for file uploads
const MultipartImportSchema = z.object({
  entity_id: z.coerce.number(),
  bank_account_id: z.coerce.number(),
  format: z.enum(['coda', 'csv', 'auto']).default('auto')
})

// Query parameter schemas
const EntityParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10))
})

const bankImportRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // Import bank transactions from file (supports both JSON with base64 and multipart)
  fastify.post('/entities/:id/bank-transactions/import', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)

      let importData: {
        entity_id: number
        bank_account_id: number
        format: 'coda' | 'csv' | 'auto'
        file_content?: string
        filename?: string
      }
      let fileContent: string
      let filename: string | undefined

      // Handle multipart file upload
      if (request.isMultipart()) {
        const parts = request.parts()
        const formData: Record<string, unknown> = {}
        let fileBuffer: Buffer | undefined

        for await (const part of parts) {
          if (part.type === 'file') {
            fileBuffer = await part.toBuffer()
            filename = part.filename
          } else {
            formData[part.fieldname] = part.value
          }
        }

        if (!fileBuffer) {
          const response: ApiResponse = {
            success: false,
            error: 'No file provided'
          }
          return reply.code(400).send(response)
        }

        importData = MultipartImportSchema.parse(formData)
        fileContent = fileBuffer.toString('utf-8')
      } else {
        // Handle JSON with base64 content
        const jsonData = BankFileImportSchema.parse(request.body)
        importData = {
          entity_id: jsonData.entity_id,
          bank_account_id: jsonData.bank_account_id,
          format: jsonData.format
        }
        filename = jsonData.filename

        // Decode base64 file content
        try {
          fileContent = Buffer.from(jsonData.file_content, 'base64').toString('utf-8')
        } catch (error) {
          const response: ApiResponse = {
            success: false,
            error: 'Invalid base64 file content'
          }
          return reply.code(400).send(response)
        }
      }

      // Validate that entity_id matches the route parameter
      if (importData.entity_id !== entityId) {
        const response: ApiResponse = {
          success: false,
          error: 'Entity ID in request body must match route parameter'
        }
        return reply.code(400).send(response)
      }

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Verify the bank account exists and belongs to the entity
      const { data: bankAccount, error: bankAccountError } = await supabaseClient
        .from('bank_accounts')
        .select('id, entity_id')
        .eq('id', importData.bank_account_id)
        .eq('entity_id', entityId)
        .single()

      if (bankAccountError || !bankAccount) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank account not found or does not belong to this entity'
        }
        return reply.code(404).send(response)
      }


      // Auto-detect format if not specified
      let format = importData.format
      if (format === 'auto') {
        if (fileContent.startsWith('0000')) {
          format = 'coda'
        } else {
          format = 'csv'
        }
      }

      // Parse file using import service
      const importResult = importService.importFile(
        fileContent,
        format,
        filename
      )

      // Convert normalized transactions for database storage
      const dbTransactions = importResult.entries.map(tx => ({
        transaction_date: tx.transaction_date,
        value_date: tx.value_date,
        amount: tx.amount,
        description: tx.description,
        transaction_id: tx.transaction_id,
        counterparty_name: tx.counterparty_name,
        reference: tx.reference,
        // Enhanced fields from CODA v2 parser
        dedupe_hash: tx.dedupe_hash,
        currency: tx.currency,
        structured_ref: tx.structured_ref,
        raw_json: tx.raw_json
      })) as unknown

      // Call the enhanced database RPC function to import transactions
      // Using the enhanced version with batch tracking (migration 0008 deployed)
      const { data: rpcResult, error: importError } = await supabaseClient.rpc('rpc_import_bank_transactions', {
        p_entity: entityId,
        p_bank_account_id: importData.bank_account_id,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
        p_transactions: dbTransactions as any
      })

      if (importError) {
        throw new Error(`Failed to import transactions: ${importError.message}`)
      }

      // Build result response using RPC result if available
      const rpcData = rpcResult as Record<string, unknown> | null
      const result = {
        imported: (rpcData?.imported as number) || importResult.summary.imported,
        skipped: (rpcData?.skipped as number) || importResult.summary.skipped,
        deduped: (rpcData?.deduped as number) || 0,
        warnings: importResult.warnings,
        errors: (rpcData?.errors as string[]) || [],
        batch_id: (rpcData?.batch_id as string) || importResult.batchId,
        transactions: importResult.entries.map((tx, index) => ({
          id: index + 1,
          status: 'imported',
          amount: tx.amount.toString(),
          description: tx.description
        }))
      }

      const response: ApiResponse = {
        success: true,
        data: result
      }

      return reply.code(201).send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

}

export default bankImportRoutes
