/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyInstance } from 'fastify'
import { SupabaseClient } from '@supabase/supabase-js'
import { z } from 'zod'
import { ApiResponse, Database } from '@belbooks/types'
import '../types' // Import type extensions

// Workers service API response interface
interface WorkersApiResponse {
  suggestions?: AISuggestionData[]
}

// Parameter schemas
const DocumentParamsSchema = z.object({
  documentId: z.string().transform(val => parseInt(val, 10))
})

const SuggestRequestSchema = z.object({
  extraction: z.object({
    supplier: z.object({
      name: z.string().optional(),
      vat: z.string().optional()
    }).optional(),
    lines: z.array(z.object({
      description: z.string().optional(),
      quantity: z.number().optional(),
      unit_price: z.number().optional(),
      vat_rate: z.object({
        value: z.number()
      }).optional()
    })).optional(),
    invoice: z.object({
      number: z.string().optional(),
      issue_date: z.string().optional(),
      gross: z.number().optional()
    }).optional()
  }).optional(),
  override_supplier: z.object({
    name: z.string().optional(),
    vat: z.string().optional()
  }).optional()
})

const FeedbackRequestSchema = z.object({
  selected_account_id: z.number(),
  selected_vat_code_id: z.number().nullable().optional(),
  suggestion_score: z.number().optional(),
  user_comment: z.string().optional()
})

// Response type for AI suggestion data structure (matches database/business logic)
interface AISuggestionData {
  account_id: number
  vat_code_id: number | null
  score: number
  confidence: 'high' | 'medium' | 'low'
  votes: number
  avg_similarity: number
  explanation: string
  examples: Array<{
    supplier_name: string | null
    confirmed_date: string | null
  }>
}

export default async function aiSuggestRoutes(fastify: FastifyInstance) {
  // Helper function to check AISuggestEnabled feature flag
  const checkAISuggestFlag = async (supabaseClient: SupabaseClient<Database>, entityId: number) => {
    const { data } = await supabaseClient
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', 'AISuggestEnabled')
      .single()

    return {
      enabled: data?.enabled || false,
      config: data?.config || {}
    }
  }

  // Get document entity and verify access
  const getDocumentEntity = async (supabaseClient: SupabaseClient<Database>, documentId: number): Promise<number> => {
    const { data: document, error } = await supabaseClient
      .from('inbox_documents')
      .select('entity_id')
      .eq('id', documentId)
      .single()

    if (error || !document) {
      throw new Error('Document not found or access denied')
    }

    return document.entity_id
  }

  // Call workers service for AI suggestions
  const callWorkersForSuggestions = async (
    entityId: number,
    extraction: Record<string, unknown>,
    supplierOverride?: { name?: string; vat?: string }
  ): Promise<AISuggestionData[]> => {
    const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'

    const requestPayload = {
      entity_id: entityId,
      extraction: extraction,
      ...(supplierOverride && { supplier_override: supplierOverride })
    }

    const response = await fetch(`${workersUrl}/ai-suggest`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestPayload)
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Workers service error: ${response.status} - ${error}`)
    }

    const result = await response.json() as WorkersApiResponse
    return result.suggestions || []
  }

  // Generate AI suggestions for a document
  fastify.post('/documents/:documentId/suggest', async (request, reply) => {
    const requestId = Math.random().toString(36).substring(2)
    const startTime = Date.now()
    let documentId: number | undefined

    try {
      const params = DocumentParamsSchema.parse(request.params)
      documentId = params.documentId

      const body = SuggestRequestSchema.parse(request.body || {})

      // CRITICAL: Enforce JWT authentication - NO FALLBACK
      if (!request.userSupabase) {
        fastify.log.warn(`AI suggest attempt without JWT token - requestId: ${requestId}, documentId: ${documentId}`)
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        }
        return reply.code(401).send(response)
      }

      const supabaseClient = request.userSupabase
      const userId = request.user?.id

      if (!userId) {
        fastify.log.warn(`AI suggest attempt with invalid JWT - requestId: ${requestId}, documentId: ${documentId}`)
        const response: ApiResponse = {
          success: false,
          error: 'Invalid authentication token'
        }
        return reply.code(401).send(response)
      }

      // Get entity ID and verify access through RLS
      const entityId = await getDocumentEntity(supabaseClient, documentId)

      // Check AISuggestEnabled feature flag
      const featureFlag = await checkAISuggestFlag(supabaseClient, entityId)
      if (!featureFlag.enabled) {
        fastify.log.info(`AI suggestions not enabled for entity ${entityId} - requestId: ${requestId}`)
        const response: ApiResponse = {
          success: false,
          error: 'AI suggestions not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Get extraction data - either from request or fetch from document
      let extraction = body.extraction
      if (!extraction) {
        // Fetch extraction from document
        const { data: document, error } = await supabaseClient
          .from('inbox_documents')
          .select('extraction')
          .eq('id', documentId)
          .single()

        if (error || !document?.extraction) {
          const response: ApiResponse = {
            success: false,
            error: 'No extraction data available for suggestions'
          }
          return reply.code(400).send(response)
        }

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
        extraction = document.extraction as any // Supabase JSON column
      }

      fastify.log.info(`Generating AI suggestions - requestId: ${requestId}, documentId: ${documentId}, entityId: ${entityId}, userId: ${userId}`)

      // Call workers service for suggestions
      const supplierOverride = body.override_supplier ? {
        ...(body.override_supplier.name && { name: body.override_supplier.name }),
        ...(body.override_supplier.vat && { vat: body.override_supplier.vat })
      } : undefined

      const suggestions = await callWorkersForSuggestions(
        entityId,
        extraction || {},
        supplierOverride
      )

      const latency = Date.now() - startTime
      fastify.log.info(`AI suggestions generated - requestId: ${requestId}, documentId: ${documentId}, count: ${suggestions.length}, latencyMs: ${latency}`)

      // Format response
      const topSuggestion = suggestions[0] || null
      const alternatives = suggestions.slice(1, 4) // Top 3 alternatives

      const response: ApiResponse = {
        success: true,
        data: {
          suggestion: topSuggestion ? {
            account_id: topSuggestion.account_id,
            vat_code_id: topSuggestion.vat_code_id,
            confidence: topSuggestion.confidence,
            score: topSuggestion.score
          } : null,
          explanation: topSuggestion?.explanation || 'No similar documents found',
          alternatives: alternatives.map(alt => ({
            account_id: alt.account_id,
            vat_code_id: alt.vat_code_id,
            confidence: alt.confidence,
            score: alt.score
          })),
          metadata: {
            total_suggestions: suggestions.length,
            entity_id: entityId,
            examples_count: topSuggestion?.examples?.length || 0
          }
        }
      }

      return reply.code(200).send(response)

    } catch (error: unknown) {
      const latency = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : 'Internal server error'

      fastify.log.error(`AI suggest failed - requestId: ${requestId}, documentId: ${documentId || 'unknown'}, latencyMs: ${latency}, error: ${errorMessage}`)

      const response: ApiResponse = {
        success: false,
        error: errorMessage.includes('not found') ? 'Document not found or access denied' :
               errorMessage.includes('not enabled') ? 'AI suggestions not enabled' :
               'Failed to generate suggestions'
      }

      const statusCode = errorMessage.includes('not found') ? 404 :
                        errorMessage.includes('not enabled') ? 403 :
                        errorMessage.includes('Authentication') ? 401 : 500

      return reply.code(statusCode).send(response)
    }
  })

  // Record feedback on AI suggestions (for future improvement)
  fastify.post('/documents/:documentId/suggest-feedback', async (request, reply) => {
    const requestId = Math.random().toString(36).substring(2)
    let documentId: number | undefined

    try {
      const params = DocumentParamsSchema.parse(request.params)
      documentId = params.documentId

      const feedback = FeedbackRequestSchema.parse(request.body)

      // Enforce JWT authentication
      if (!request.userSupabase) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        }
        return reply.code(401).send(response)
      }

      const supabaseClient = request.userSupabase
      const userId = request.user?.id

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid authentication token'
        }
        return reply.code(401).send(response)
      }

      // Verify document access
      const entityId = await getDocumentEntity(supabaseClient, documentId)

      // Check feature flag
      const featureFlag = await checkAISuggestFlag(supabaseClient, entityId)
      if (!featureFlag.enabled) {
        const response: ApiResponse = {
          success: false,
          error: 'AI suggestions not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Record feedback (for future model improvement)
      // This could be stored in a feedback table or sent to analytics
      fastify.log.info(`AI suggestion feedback recorded - requestId: ${requestId}, documentId: ${documentId}, entityId: ${entityId}, userId: ${userId}, selectedAccount: ${feedback.selected_account_id}, score: ${feedback.suggestion_score}`)

      // Future: Store in suggestion_feedback table or send to analytics service

      const response: ApiResponse = {
        success: true,
        data: {
          message: 'Feedback recorded successfully'
        }
      }

      return reply.code(200).send(response)

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Internal server error'

      fastify.log.error(`AI suggest feedback failed - requestId: ${requestId}, documentId: ${documentId || 'unknown'}, error: ${errorMessage}`)

      const response: ApiResponse = {
        success: false,
        error: errorMessage.includes('not found') ? 'Document not found or access denied' :
               errorMessage.includes('not enabled') ? 'AI suggestions not enabled' :
               'Failed to record feedback'
      }

      const statusCode = errorMessage.includes('not found') ? 404 :
                        errorMessage.includes('not enabled') ? 403 :
                        errorMessage.includes('Authentication') ? 401 : 500

      return reply.code(statusCode).send(response)
    }
  })

  // Health check endpoint for AI suggestions
  fastify.get('/ai-suggest/health', async (_, reply) => {
    try {
      // Test workers service connectivity
      const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'
      const response = await fetch(`${workersUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      })

      const workersHealthy = response.ok

      const healthStatus = {
        status: workersHealthy ? 'healthy' : 'degraded',
        workers_service: workersHealthy ? 'up' : 'down',
        timestamp: new Date().toISOString()
      }

      const statusCode = workersHealthy ? 200 : 503

      const apiResponse: ApiResponse = {
        success: workersHealthy,
        data: healthStatus
      }

      return reply.code(statusCode).send(apiResponse)

    } catch (error) {
      fastify.log.error(`AI suggest health check failed: ${error instanceof Error ? error.message : String(error)}`)

      const apiResponse: ApiResponse = {
        success: false,
        data: {
          status: 'unhealthy',
          workers_service: 'error',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Health check failed'
        }
      }

      return reply.code(503).send(apiResponse)
    }
  })
}
