import { SupabaseClient } from '@supabase/supabase-js'
import { Database } from '@belbooks/types'

// Overlay types for new database tables not yet in generated types
export interface ExportJob {
  id: number
  entity_id: number
  event_id: number
  connector: 'winbooks_sftp' | 'email'
  status: 'queued' | 'delivering' | 'delivered' | 'failed' | 'skipped'
  content_hash: string
  attempts: number
  last_attempt_at?: string
  last_error?: string
  created_at: string
  updated_at: string
}

export interface ConnectorConfig {
  id: number
  entity_id: number
  connector: 'winbooks_sftp' | 'email'
  config: {
    // SFTP config
    host?: string | undefined
    port?: number | undefined
    username?: string | undefined
    remote_dir?: string | undefined
    // Email config
    to_email?: string | undefined
    // Shared settings
    enabled?: boolean | undefined
    dry_run?: boolean | undefined
  }
  created_at: string
  updated_at: string
}

export interface ExportJobSummary {
  connector: string
  total_jobs: number
  queued: number
  delivering: number
  delivered: number
  failed: number
  skipped: number
  last_delivery?: string
}

// Extended Supabase client type with overlay tables using any for simplicity
// This provides better ESLint compliance while maintaining TypeScript compilation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ExtendedSupabaseClient = SupabaseClient<any>

declare module 'fastify' {
  interface FastifyInstance {
    supabase: SupabaseClient<Database>
    config: {
      BFF_PORT: string
      SUPABASE_URL: string
      SUPABASE_ANON_KEY: string
      SUPABASE_SERVICE_ROLE_KEY: string
      INTERNAL_KEY: string
      EMAIL_IN_PROVIDER: string
      EMAIL_IN_SIGNING_SECRET: string
      EMAIL_IN_DOMAIN: string
      MAX_ATTACHMENTS: string
      MAX_ATTACHMENT_MB: string
    }
  }

  interface FastifyRequest {
    userSupabase?: SupabaseClient<Database>
    user?: { id: string; [key: string]: unknown }
  }

  interface FastifyReply {
    code(_statusCode: number): FastifyReply
    send(_payload?: unknown): FastifyReply
    header(_name: string, _value: string | string[]): FastifyReply
  }
}
