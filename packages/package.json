{"name": "@belbooks/domain-invoicing", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc -p tsconfig.json", "typecheck": "tsc -p tsconfig.json --noEmit", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"decimal.js": "^10.4.3", "xmlbuilder2": "^3.1.1", "zod": "^3.23.0", "date-fns": "^3.6.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.12.7", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.5.3"}}