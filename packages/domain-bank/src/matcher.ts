import { BankTransaction } from './parsers'

export interface MatchingRule {
  id: string
  name: string
  priority: number
  conditions: MatchCondition[]
  actions: MatchAction[]
}

export interface MatchCondition {
  field: 'amount' | 'counterparty_name' | 'counterparty_account' | 'description' | 'reference'
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex' | 'range' | 'greater_than' | 'less_than'
  value: string | number | { min: number; max: number }
  caseSensitive?: boolean
}

export interface MatchAction {
  type: 'set_account' | 'set_counterparty' | 'set_category' | 'auto_reconcile' | 'flag_review'
  value: string | number | boolean
}

export interface TransactionMatch {
  confidence: number // 0-1
  matchedRules: MatchingRule[]
  suggestedAccount?: number
  suggestedCounterparty?: string
  suggestedCategory?: string
  requiresReview: boolean
  autoReconcile: boolean
}

export class BankTransactionMatcher {
  private rules: MatchingRule[] = []

  constructor(rules: MatchingRule[] = []) {
    this.rules = rules.sort((a, b) => b.priority - a.priority)
  }

  addRule(rule: MatchingRule): void {
    this.rules.push(rule)
    this.rules.sort((a, b) => b.priority - a.priority)
  }

  matchTransaction(transaction: BankTransaction): TransactionMatch {
    const matchedRules: MatchingRule[] = []
    let totalConfidence = 0
    let suggestedAccount: number | undefined
    let suggestedCounterparty: string | undefined
    let suggestedCategory: string | undefined
    let requiresReview = false
    let autoReconcile = false

    for (const rule of this.rules) {
      if (this.evaluateRule(transaction, rule)) {
        matchedRules.push(rule)
        totalConfidence += (1 - totalConfidence) * 0.1 // Diminishing returns

        // Apply actions
        for (const action of rule.actions) {
          switch (action.type) {
            case 'set_account':
              if (!suggestedAccount && typeof action.value === 'number') {
                suggestedAccount = action.value
              }
              break
            case 'set_counterparty':
              if (!suggestedCounterparty && typeof action.value === 'string') {
                suggestedCounterparty = action.value
              }
              break
            case 'set_category':
              if (!suggestedCategory && typeof action.value === 'string') {
                suggestedCategory = action.value
              }
              break
            case 'auto_reconcile':
              if (typeof action.value === 'boolean') {
                autoReconcile = action.value
              }
              break
            case 'flag_review':
              if (typeof action.value === 'boolean') {
                requiresReview = action.value
              }
              break
          }
        }
      }
    }

    // Calculate final confidence based on number and quality of matches
    const confidence = Math.min(
      totalConfidence + (matchedRules.length * 0.1),
      1.0
    )

    return {
      confidence,
      matchedRules,
      suggestedAccount,
      suggestedCounterparty,
      suggestedCategory,
      requiresReview,
      autoReconcile: autoReconcile && confidence > 0.8
    }
  }

  private evaluateRule(transaction: BankTransaction, rule: MatchingRule): boolean {
    // All conditions must be met (AND logic)
    return rule.conditions.every(condition =>
      this.evaluateCondition(transaction, condition)
    )
  }

  private evaluateCondition(transaction: BankTransaction, condition: MatchCondition): boolean {
    const fieldValue = this.getFieldValue(transaction, condition.field)
    
    if (fieldValue === undefined || fieldValue === null) {
      return false
    }

    switch (condition.operator) {
      case 'equals':
        if (typeof fieldValue === 'string' && typeof condition.value === 'string') {
          const val1 = condition.caseSensitive ? fieldValue : fieldValue.toLowerCase()
          const val2 = condition.caseSensitive ? condition.value : condition.value.toLowerCase()
          return val1 === val2
        }
        return fieldValue === condition.value

      case 'contains':
        if (typeof fieldValue === 'string' && typeof condition.value === 'string') {
          const val1 = condition.caseSensitive ? fieldValue : fieldValue.toLowerCase()
          const val2 = condition.caseSensitive ? condition.value : condition.value.toLowerCase()
          return val1.includes(val2)
        }
        return false

      case 'starts_with':
        if (typeof fieldValue === 'string' && typeof condition.value === 'string') {
          const val1 = condition.caseSensitive ? fieldValue : fieldValue.toLowerCase()
          const val2 = condition.caseSensitive ? condition.value : condition.value.toLowerCase()
          return val1.startsWith(val2)
        }
        return false

      case 'ends_with':
        if (typeof fieldValue === 'string' && typeof condition.value === 'string') {
          const val1 = condition.caseSensitive ? fieldValue : fieldValue.toLowerCase()
          const val2 = condition.caseSensitive ? condition.value : condition.value.toLowerCase()
          return val1.endsWith(val2)
        }
        return false

      case 'regex':
        if (typeof fieldValue === 'string' && typeof condition.value === 'string') {
          try {
            // Sanitize regex pattern to prevent ReDoS attacks
            const sanitizedPattern = condition.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            const flags = condition.caseSensitive ? 'g' : 'gi'
            // eslint-disable-next-line security/detect-non-literal-regexp
            const regex = new RegExp(sanitizedPattern, flags)
            return regex.test(fieldValue)
          } catch {
            return false
          }
        }
        return false

      case 'range':
        if (typeof fieldValue === 'number' && typeof condition.value === 'object' && 
            'min' in condition.value && 'max' in condition.value) {
          return fieldValue >= condition.value.min && fieldValue <= condition.value.max
        }
        return false

      case 'greater_than':
        return typeof fieldValue === 'number' && typeof condition.value === 'number' &&
               fieldValue > condition.value

      case 'less_than':
        return typeof fieldValue === 'number' && typeof condition.value === 'number' &&
               fieldValue < condition.value

      default:
        return false
    }
  }

  private getFieldValue(transaction: BankTransaction, field: MatchCondition['field']): string | number | undefined {
    switch (field) {
      case 'amount':
        return Math.abs(transaction.amount) // Always use absolute value for matching
      case 'counterparty_name':
        return transaction.counterpartyName
      case 'counterparty_account':
        return transaction.counterpartyAccount
      case 'description':
        return transaction.description
      case 'reference':
        return transaction.reference
      default:
        return undefined
    }
  }
}

// Predefined matching rules for common Belgian transactions
export const DEFAULT_BELGIAN_RULES: MatchingRule[] = [
  {
    id: 'salary-incoming',
    name: 'Salary Payment',
    priority: 100,
    conditions: [
      { field: 'amount', operator: 'greater_than', value: 1000 },
      { field: 'description', operator: 'contains', value: 'salaris', caseSensitive: false }
    ],
    actions: [
      { type: 'set_category', value: 'salary' },
      { type: 'auto_reconcile', value: true }
    ]
  },
  {
    id: 'rent-payment',
    name: 'Rent Payment',
    priority: 90,
    conditions: [
      { field: 'amount', operator: 'less_than', value: 0 },
      { field: 'description', operator: 'contains', value: 'huur', caseSensitive: false }
    ],
    actions: [
      { type: 'set_category', value: 'rent' },
      { type: 'auto_reconcile', value: true }
    ]
  },
  {
    id: 'utility-bills',
    name: 'Utility Bills',
    priority: 80,
    conditions: [
      { field: 'amount', operator: 'less_than', value: 0 },
      { field: 'counterparty_name', operator: 'contains', value: 'engie|luminus|fluvius', caseSensitive: false }
    ],
    actions: [
      { type: 'set_category', value: 'utilities' },
      { type: 'auto_reconcile', value: true }
    ]
  },
  {
    id: 'large-transaction-review',
    name: 'Large Transaction Review',
    priority: 10,
    conditions: [
      { field: 'amount', operator: 'greater_than', value: 10000 }
    ],
    actions: [
      { type: 'flag_review', value: true }
    ]
  }
]