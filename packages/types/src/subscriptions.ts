// Subscription-related types for future implementation
// This is a skeleton for subscription awareness

export type SubscriptionStatus = 
  | 'trial' 
  | 'active' 
  | 'past_due' 
  | 'canceled' 
  | 'incomplete'

export type PlanTier = 
  | 'trial'
  | 'starter' 
  | 'professional' 
  | 'enterprise'

export interface SubscriptionLimits {
  entities: number
  users: number
  transactions_per_month: number
  storage_gb: number
  features: string[]
}

export interface TenantSubscription {
  tenant_id: number
  status: SubscriptionStatus
  plan: PlanTier
  current_period_start: Date
  current_period_end: Date
  limits: SubscriptionLimits
  trial_ends_at?: Date
  stripe_subscription_id?: string
  stripe_customer_id?: string
}

// Plan configurations (to be moved to database/external config later)
export const PLAN_LIMITS: Record<PlanTier, SubscriptionLimits> = {
  trial: {
    entities: 1,
    users: 2,
    transactions_per_month: 100,
    storage_gb: 1,
    features: ['basic_ledger', 'bank_import']
  },
  starter: {
    entities: 3,
    users: 5,
    transactions_per_month: 1000,
    storage_gb: 5,
    features: ['basic_ledger', 'bank_import', 'vat_reporting', 'basic_analytics']
  },
  professional: {
    entities: 10,
    users: 20,
    transactions_per_month: 10000,
    storage_gb: 50,
    features: ['basic_ledger', 'bank_import', 'vat_reporting', 'advanced_analytics', 'multi_entity', 'api_access']
  },
  enterprise: {
    entities: -1, // unlimited
    users: -1, // unlimited
    transactions_per_month: -1, // unlimited
    storage_gb: 1000,
    features: ['all_features', 'priority_support', 'custom_integrations', 'audit_logs']
  }
}

export interface UsageMetrics {
  tenant_id: number
  period_start: Date
  period_end: Date
  entities_used: number
  users_active: number
  transactions_count: number
  storage_used_gb: number
}

// Feature flags based on subscription
export const hasFeature = (subscription: TenantSubscription, feature: string): boolean => {
  const limits = PLAN_LIMITS[subscription.plan]
  return limits.features.includes(feature) || limits.features.includes('all_features')
}

export const isWithinLimits = (subscription: TenantSubscription, usage: Partial<UsageMetrics>): boolean => {
  const limits = PLAN_LIMITS[subscription.plan]
  
  // Check each limit (-1 means unlimited)
  if (limits.entities !== -1 && usage.entities_used && usage.entities_used > limits.entities) {
    return false
  }
  
  if (limits.users !== -1 && usage.users_active && usage.users_active > limits.users) {
    return false
  }
  
  if (limits.transactions_per_month !== -1 && usage.transactions_count && usage.transactions_count > limits.transactions_per_month) {
    return false
  }
  
  if (limits.storage_gb !== -1 && usage.storage_used_gb && usage.storage_used_gb > limits.storage_gb) {
    return false
  }
  
  return true
}