// VAT overlay types for new RPC functions
// These provide type safety until next Supabase typegen from staging

export interface VATPreviewRow {
  grid_code: string
  direction: 'input' | 'output'
  base_total: number
  vat_total: number
}

export interface VATExportParams {
  p_entity: number
  p_start: string  // YYYY-MM-DD format
  p_end: string    // YYYY-MM-DD format
}

export interface VATPreviewParams extends VATExportParams {
  // Same parameters as export
}

export interface VATSummaryTotals {
  base_total: number
  vat_total: number
}

export interface VATSummary {
  output: VATSummaryTotals
  input: VATSummaryTotals
  net_vat_payable: number
}

export interface VATPreviewResponse {
  period: {
    start: string
    end: string
  }
  entries: VATPreviewRow[]
  summary: VATSummary
}

// Error types for VAT operations
export interface VATError extends Error {
  code?: string
  details?: unknown
}

// Grid code constants for Belgian VAT
export const BELGIAN_VAT_GRIDS = {
  BE_OUT_21: 'BE_OUT_21',
  BE_OUT_12: 'BE_OUT_12', 
  BE_OUT_6: 'BE_OUT_6',
  BE_OUT_0: 'BE_OUT_0',
  BE_IN_21: 'BE_IN_21',
  BE_IN_12: 'BE_IN_12',
  BE_IN_6: 'BE_IN_6',
  BE_IN_0: 'BE_IN_0',
  UNMAPPED: 'UNMAPPED'
} as const

export type BelgianVATGrid = typeof BELGIAN_VAT_GRIDS[keyof typeof BELGIAN_VAT_GRIDS]

// Date validation helpers
export const VAT_DATE_REGEX = /^\d{4}-\d{2}-\d{2}$/

export interface VATDateRange {
  start: string
  end: string
}

export function isValidVATDateRange(range: VATDateRange): boolean {
  return VAT_DATE_REGEX.test(range.start) && VAT_DATE_REGEX.test(range.end)
}