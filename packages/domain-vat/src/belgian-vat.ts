export interface BelgianVatCode {
  code: string
  name: string
  rate: number
  isPurchase: boolean
  isSale: boolean
  gridMapping?: {
    salesGrid?: number[]
    purchaseGrid?: number[]
  }
}

export const BELGIAN_VAT_CODES: Record<string, BelgianVatCode> = {
  'BE21': {
    code: 'BE21',
    name: 'Belgian VAT 21%',
    rate: 0.21,
    isPurchase: true,
    isSale: true,
    gridMapping: {
      salesGrid: [1, 54],     // Grid 1: turnover, Grid 54: VAT due
      purchaseGrid: [81, 59]  // Grid 81: goods/services, Grid 59: VAT deductible
    }
  },
  'BE12': {
    code: 'BE12',
    name: 'Belgian VAT 12%',
    rate: 0.12,
    isPurchase: true,
    isSale: true,
    gridMapping: {
      salesGrid: [2, 54],
      purchaseGrid: [82, 59]
    }
  },
  'BE06': {
    code: 'BE06',
    name: 'Belgian VAT 6%',
    rate: 0.06,
    isPurchase: true,
    isSale: true,
    gridMapping: {
      salesGrid: [3, 54],
      purchaseGrid: [83, 59]
    }
  },
  'BE00': {
    code: 'BE00',
    name: 'Belgian VAT 0% (exempt)',
    rate: 0.00,
    isPurchase: true,
    isSale: true,
    gridMapping: {
      salesGrid: [45],        // Grid 45: exempt operations
      purchaseGrid: [84]      // Grid 84: other deductible
    }
  },
  'BEIC': {
    code: 'BEIC',
    name: 'Belgian Intra-Community',
    rate: 0.00,
    isPurchase: true,
    isSale: true,
    gridMapping: {
      salesGrid: [46],        // Grid 46: intra-community supplies
      purchaseGrid: [86, 87]  // Grid 86: acquisitions, Grid 87: VAT due
    }
  },
  'BEEX': {
    code: 'BEEX',
    name: 'Belgian Export',
    rate: 0.00,
    isPurchase: false,
    isSale: true,
    gridMapping: {
      salesGrid: [47]         // Grid 47: exports outside EU
    }
  },
  'BEREVERSE': {
    code: 'BEREVERSE',
    name: 'Belgian Reverse Charge',
    rate: 0.00,
    isPurchase: true,
    isSale: false,
    gridMapping: {
      purchaseGrid: [88, 87]  // Grid 88: other operations, Grid 87: VAT due
    }
  }
}

export interface VatCalculation {
  netAmount: number
  vatAmount: number
  grossAmount: number
  vatRate: number
  vatCode: string
}

export class BelgianVatCalculator {
  static calculateVatFromNet(netAmount: number, vatCode: string): VatCalculation {
    // eslint-disable-next-line security/detect-object-injection
    const code = BELGIAN_VAT_CODES[vatCode]
    if (!code) {
      throw new Error(`Unknown VAT code: ${vatCode}`)
    }

    const vatAmount = Math.round(netAmount * code.rate * 100) / 100
    const grossAmount = netAmount + vatAmount

    return {
      netAmount,
      vatAmount,
      grossAmount,
      vatRate: code.rate,
      vatCode
    }
  }

  static calculateVatFromGross(grossAmount: number, vatCode: string): VatCalculation {
    // eslint-disable-next-line security/detect-object-injection
    const code = BELGIAN_VAT_CODES[vatCode]
    if (!code) {
      throw new Error(`Unknown VAT code: ${vatCode}`)
    }

    if (code.rate === 0) {
      return {
        netAmount: grossAmount,
        vatAmount: 0,
        grossAmount,
        vatRate: code.rate,
        vatCode
      }
    }

    const netAmount = Math.round((grossAmount / (1 + code.rate)) * 100) / 100
    const vatAmount = grossAmount - netAmount

    return {
      netAmount,
      vatAmount,
      grossAmount,
      vatRate: code.rate,
      vatCode
    }
  }

  static isValidVatNumber(vatNumber: string): boolean {
    // Belgian VAT number format: BE0123456789 or BE 0123 456 789
    const cleanVat = vatNumber.replace(/[\s-]/g, '').toUpperCase()
    
    if (!cleanVat.startsWith('BE')) {
      return false
    }

    const numberPart = cleanVat.substring(2)
    if (numberPart.length !== 10) {
      return false
    }

    if (!/^\d{10}$/.test(numberPart)) {
      return false
    }

    // Validate check digits (last 2 digits)
    const baseNumber = numberPart.substring(0, 8)
    const checkDigits = parseInt(numberPart.substring(8, 10))
    
    const calculatedCheck = 97 - (parseInt(baseNumber) % 97)
    
    return checkDigits === calculatedCheck
  }

  static formatVatNumber(vatNumber: string): string {
    const cleanVat = vatNumber.replace(/[\s-]/g, '').toUpperCase()
    
    if (!this.isValidVatNumber(cleanVat)) {
      throw new Error('Invalid Belgian VAT number')
    }

    const numberPart = cleanVat.substring(2)
    return `BE ${numberPart.substring(0, 4)} ${numberPart.substring(4, 7)} ${numberPart.substring(7, 10)}`
  }
}