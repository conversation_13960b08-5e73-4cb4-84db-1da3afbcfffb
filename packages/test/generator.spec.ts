import fs from 'node:fs'
import path from 'node:path'
import { generateUblInvoice } from '../src/ubl/generateUblInvoice'
import type { ARInvoiceInput } from '../src/types'

function loadFixture(name: string): ARInvoiceInput {
  const p = path.join(__dirname, 'fixtures', name + '.json')
  return JSON.parse(fs.readFileSync(p, 'utf8'))
}

function normalize(xml: string): string {
  return xml.replace(/\r\n/g, '\n').trim()
}

describe('UBL generator (goldens)', () => {
  const cases = ['ar_be_21', 'ar_be_12', 'ar_be_6']
  for (const c of cases) {
    it(`matches golden for ${c}`, () => {
      const input = loadFixture(c)
      const { xml } = generateUblInvoice(input)
      // Golden bootstrap: if golden file missing, write it once to assist local dev
      const goldenPath = path.join(__dirname, 'golden', c + '.xml')
      if (!fs.existsSync(goldenPath)) {
        fs.writeFileSync(goldenPath, xml, 'utf8')
      }
      const golden = fs.readFileSync(goldenPath, 'utf8')
      expect(normalize(xml)).toBe(normalize(golden))
    })
  }

  it('integrity totals are consistent', () => {
    const input = loadFixture('ar_be_21')
    const { meta } = generateUblInvoice(input)
    const taxExclusive = Number(meta.totals.lineNet)
    const taxTotal = Number(meta.totals.taxTotal)
    const gross = Number(meta.totals.gross)
    expect(Math.abs(taxExclusive + taxTotal - gross)).toBeLessThanOrEqual(0.01)
  })
})
