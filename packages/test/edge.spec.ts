import { generateUblInvoice } from '../src/ubl/generateUblInvoice'
import type { ARInvoiceInput } from '../src/types'

describe('edge cases', () => {
  it('emits two TaxSubtotals for mixed VAT rates (21% and 6%)', () => {
    const input: ARInvoiceInput = {
      invoice: { id: 10, number: 'INV-10', issueDate: '2024-01-01', currency: 'EUR' },
      seller: { name: 'Seller' },
      buyer: { name: 'Buyer' },
      lines: [
        { id: 1, description: 'A', quantity: '1', unitPrice: '100.00', vatRate: 21 },
        { id: 2, description: 'B', quantity: '1', unitPrice: '50.00', vatRate: 6 }
      ]
    }
    const { meta } = generateUblInvoice(input)
    expect(Object.keys(meta.totals.taxByRate).sort()).toEqual(['21', '6'])
    const net = Number(meta.totals.lineNet)
    const tax = Number(meta.totals.taxTotal)
    const gross = Number(meta.totals.gross)
    expect(Math.abs(net + tax - gross)).toBeLessThanOrEqual(0.01)
  })

  it('handles zero VAT (Z) with correct totals', () => {
    const input: ARInvoiceInput = {
      invoice: { id: 11, number: 'INV-11', issueDate: '2024-01-02', currency: 'EUR' },
      seller: { name: 'Seller' },
      buyer: { name: 'Buyer' },
      lines: [
        { id: 1, description: 'Zero', quantity: '2', unitPrice: '10.00', vatRate: 0 }
      ]
    }
    const { meta } = generateUblInvoice(input)
    expect(Object.keys(meta.totals.taxByRate)).toEqual(['0'])
    expect(Number(meta.totals.taxTotal)).toBeCloseTo(0, 2)
    const net = Number(meta.totals.lineNet)
    const gross = Number(meta.totals.gross)
    expect(Math.abs(net - gross)).toBeLessThanOrEqual(0.01)
  })

  it('rounding stress: qty 1 x 33.3333 at 21%', () => {
    const input: ARInvoiceInput = {
      invoice: { id: 12, number: 'INV-12', issueDate: '2024-01-03', currency: 'EUR' },
      seller: { name: 'Seller' },
      buyer: { name: 'Buyer' },
      lines: [
        { id: 1, description: 'Stress', quantity: '1', unitPrice: '33.3333', vatRate: 21 }
      ]
    }
    const { meta } = generateUblInvoice(input)
    const net = Number(meta.totals.lineNet)
    const tax = Number(meta.totals.taxTotal)
    const gross = Number(meta.totals.gross)
    expect(Math.abs(net + tax - gross)).toBeLessThanOrEqual(0.01)
  })
})
