import { create } from 'xmlbuilder2'
import { createHash } from 'crypto'
import Decimal from 'decimal.js'
import { ARInvoiceInput, ARInvoiceSchema, VatRate } from '../types'
import { d, mul, round2, sum, toAmountString } from '../money'

function vatCategory(rate: VatRate): { id: 'S' | 'AA' | 'Z'; percent: number } {
  if (rate === 21) return { id: 'S', percent: 21 }
  if (rate === 12 || rate === 6) return { id: 'AA', percent: rate }
  return { id: 'Z', percent: 0 }
}

export interface UblTotalsMeta {
  lineNet: string
  taxByRate: Record<string, { taxable: string; tax: string }>
  taxTotal: string
  gross: string
}

export function generateUblInvoice(input: ARInvoiceInput): {
  xml: string
  meta: { hash: string; totals: UblTotalsMeta }
} {
  const validated = ARInvoiceSchema.parse(input)

  // Per-line net rounded
  const lineNets: Decimal[] = validated.lines.map(ln =>
    round2(mul(ln.quantity, ln.unitPrice))
  )
  const lineNetSum = round2(sum(lineNets))

  // Bucket by VAT rate
  const byRate = new Map<VatRate, { taxable: Decimal; tax: Decimal }>()
  validated.lines.forEach((ln, _idx) => {
    // eslint-disable-next-line security/detect-object-injection
    const net: Decimal = round2(mul(ln.quantity, ln.unitPrice))
    const prev = byRate.get(ln.vatRate) || { taxable: d(0), tax: d(0) }
    const rate = new Decimal(ln.vatRate).div(100)
    const taxable = prev.taxable.add(net)
    const tax = prev.tax.add(round2(net.mul(rate)))
    byRate.set(ln.vatRate, { taxable, tax })
  })

  const taxTotal = round2(sum(Array.from(byRate.values()).map(b => b.tax)))
  const taxExclusive = lineNetSum
  const taxInclusive = round2(taxExclusive.add(taxTotal))

  const doc = create({ version: '1.0', encoding: 'UTF-8' }).ele('Invoice', {
    xmlns: 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2',
    'xmlns:cac':
      'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
    'xmlns:cbc':
      'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
  })

  // Header
  doc.ele('cbc:CustomizationID').txt('urn:cen.eu:en16931:2017').up()
  doc
    .ele('cbc:ProfileID')
    .txt('urn:fdc:peppol.eu:2017:poacc:billing:01:1.0')
    .up()
  doc.ele('cbc:ID').txt(validated.invoice.number).up()
  doc.ele('cbc:IssueDate').txt(validated.invoice.issueDate).up()
  doc.ele('cbc:InvoiceTypeCode').txt('380').up()
  doc.ele('cbc:DocumentCurrencyCode').txt(validated.invoice.currency).up()

  // Supplier
  const supplierParty = doc.ele('cac:AccountingSupplierParty').ele('cac:Party')
  supplierParty
    .ele('cac:PartyName')
    .ele('cbc:Name')
    .txt(validated.seller.name)
    .up()
    .up()
  if (validated.seller.vatNumber) {
    const pts = supplierParty.ele('cac:PartyTaxScheme')
    pts.ele('cbc:CompanyID').txt(validated.seller.vatNumber).up()
    pts.ele('cac:TaxScheme').ele('cbc:ID').txt('VAT').up().up()
  }
  if (
    validated.seller.street ||
    validated.seller.city ||
    validated.seller.zip ||
    validated.seller.country
  ) {
    const addr = supplierParty.ele('cac:PostalAddress')
    if (validated.seller.street)
      addr.ele('cbc:StreetName').txt(validated.seller.street).up()
    if (validated.seller.city)
      addr.ele('cbc:CityName').txt(validated.seller.city).up()
    if (validated.seller.zip)
      addr.ele('cbc:PostalZone').txt(validated.seller.zip).up()
    if (validated.seller.country)
      addr
        .ele('cac:Country')
        .ele('cbc:IdentificationCode')
        .txt(validated.seller.country)
        .up()
        .up()
  }
  supplierParty.up().up()

  // Customer
  const customerParty = doc.ele('cac:AccountingCustomerParty').ele('cac:Party')
  customerParty
    .ele('cac:PartyName')
    .ele('cbc:Name')
    .txt(validated.buyer.name)
    .up()
    .up()
  if (validated.buyer.vatNumber) {
    const pts = customerParty.ele('cac:PartyTaxScheme')
    pts.ele('cbc:CompanyID').txt(validated.buyer.vatNumber).up()
    pts.ele('cac:TaxScheme').ele('cbc:ID').txt('VAT').up().up()
  }
  if (
    validated.buyer.street ||
    validated.buyer.city ||
    validated.buyer.zip ||
    validated.buyer.country
  ) {
    const addr = customerParty.ele('cac:PostalAddress')
    if (validated.buyer.street)
      addr.ele('cbc:StreetName').txt(validated.buyer.street).up()
    if (validated.buyer.city)
      addr.ele('cbc:CityName').txt(validated.buyer.city).up()
    if (validated.buyer.zip)
      addr.ele('cbc:PostalZone').txt(validated.buyer.zip).up()
    if (validated.buyer.country)
      addr
        .ele('cac:Country')
        .ele('cbc:IdentificationCode')
        .txt(validated.buyer.country)
        .up()
        .up()
  }
  customerParty.up().up()

  // Lines
  validated.lines.forEach((ln, _idx) => {
    // eslint-disable-next-line security/detect-object-injection
    const lineNet: Decimal = round2(mul(ln.quantity, ln.unitPrice))
    const il = doc.ele('cac:InvoiceLine')
    il.ele('cbc:ID').txt(String(ln.id)).up()
    il.ele('cbc:InvoicedQuantity', { unitCode: ln.unit ?? 'C62' })
      .txt(d(ln.quantity).toString())
      .up()
    il.ele('cbc:LineExtensionAmount', {
      currencyID: validated.invoice.currency,
    })
      .txt(toAmountString(lineNet))
      .up()
    const item = il.ele('cac:Item')
    item.ele('cbc:Description').txt(ln.description).up()
    const cat = item.ele('cac:ClassifiedTaxCategory')
    const mapping = vatCategory(ln.vatRate)
    cat.ele('cbc:ID').txt(mapping.id).up()
    cat.ele('cbc:Percent').txt(mapping.percent.toString()).up()
    cat.ele('cac:TaxScheme').ele('cbc:ID').txt('VAT').up().up()
    item.up()
    const price = il.ele('cac:Price')
    price
      .ele('cbc:PriceAmount', { currencyID: validated.invoice.currency })
      .txt(toAmountString(d(ln.unitPrice)))
      .up()
    price.up()
    il.up()
  })

  // Tax totals
  const taxTotalEl = doc.ele('cac:TaxTotal')
  taxTotalEl
    .ele('cbc:TaxAmount', { currencyID: validated.invoice.currency })
    .txt(toAmountString(taxTotal))
    .up()
  Array.from(byRate.entries())
    .sort(([a], [b]) => a - b)
    .forEach(([rate, bucket]) => {
      const subtotal = taxTotalEl.ele('cac:TaxSubtotal')
      subtotal
        .ele('cbc:TaxableAmount', { currencyID: validated.invoice.currency })
        .txt(toAmountString(round2(bucket.taxable)))
        .up()
      subtotal
        .ele('cbc:TaxAmount', { currencyID: validated.invoice.currency })
        .txt(toAmountString(round2(bucket.tax)))
        .up()
      const cat = subtotal.ele('cac:TaxCategory')
      const mapping = vatCategory(rate)
      cat.ele('cbc:ID').txt(mapping.id).up()
      cat.ele('cbc:Percent').txt(mapping.percent.toString()).up()
      cat.ele('cac:TaxScheme').ele('cbc:ID').txt('VAT').up().up()
      subtotal.up()
    })
  taxTotalEl.up()

  // Monetary totals
  const legal = doc.ele('cac:LegalMonetaryTotal')
  legal
    .ele('cbc:LineExtensionAmount', { currencyID: validated.invoice.currency })
    .txt(toAmountString(lineNetSum))
    .up()
  legal
    .ele('cbc:TaxExclusiveAmount', { currencyID: validated.invoice.currency })
    .txt(toAmountString(taxExclusive))
    .up()
  legal
    .ele('cbc:TaxInclusiveAmount', { currencyID: validated.invoice.currency })
    .txt(toAmountString(taxInclusive))
    .up()
  legal
    .ele('cbc:PayableAmount', { currencyID: validated.invoice.currency })
    .txt(toAmountString(taxInclusive))
    .up()
  legal.up()

  const xml = doc.end({ prettyPrint: true })
  const hash = createHash('sha256').update(xml).digest('hex')

  return {
    xml,
    meta: {
      hash,
      totals: {
        lineNet: toAmountString(lineNetSum),
        taxByRate: Object.fromEntries(
          Array.from(byRate.entries()).map(([rate, bucket]) => [
            String(rate),
            {
              taxable: toAmountString(round2(bucket.taxable)),
              tax: toAmountString(round2(bucket.tax)),
            },
          ])
        ),
        taxTotal: toAmountString(taxTotal),
        gross: toAmountString(taxInclusive),
      },
    },
  }
}
