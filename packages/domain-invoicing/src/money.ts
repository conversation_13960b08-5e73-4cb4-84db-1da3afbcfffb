import Decimal from 'decimal.js'

Decimal.set({ precision: 40, rounding: Decimal.ROUND_HALF_UP })

export function d(value: string | number | Decimal): Decimal {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any
  return new Decimal(value as any)
}

export function round2(value: Decimal): Decimal {
  return value.toDecimalPlaces(2, Decimal.ROUND_HALF_UP)
}

export function mul(a: string, b: string): Decimal {
  return d(a).mul(d(b))
}

export function add(a: Decimal, b: Decimal): Decimal {
  return a.add(b)
}

export function sum(values: Decimal[]): Decimal {
  return values.reduce((acc, v) => acc.add(v), new Decimal(0))
}

export function toAmountString(value: Decimal): string {
  return round2(value).toFixed(2)
}
