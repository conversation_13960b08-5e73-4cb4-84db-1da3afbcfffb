import { z } from 'zod'

export type VatRate = 21 | 12 | 6 | 0

export interface ARInvoiceInput {
  invoice: {
    id: number
    number: string
    issueDate: string
    dueDate?: string
    currency: 'EUR'
  }
  seller: {
    name: string
    vatNumber?: string
    companyId?: string
    street?: string
    city?: string
    zip?: string
    country?: string
    email?: string
  }
  buyer: {
    name: string
    vatNumber?: string
    street?: string
    city?: string
    zip?: string
    country?: string
    email?: string
  }
  lines: Array<{
    id: number
    description: string
    quantity: string
    unitPrice: string
    vatRate: VatRate
    unit?: string
  }>
  payment?: {
    iban?: string
    bic?: string
    termsText?: string
  }
}

export const ARInvoiceSchema = z.object({
  invoice: z.object({
    id: z.number().int().positive(),
    number: z.string().min(1),
    issueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    currency: z.literal('EUR')
  }),
  seller: z.object({
    name: z.string().min(1),
    vatNumber: z.string().optional(),
    companyId: z.string().optional(),
    street: z.string().optional(),
    city: z.string().optional(),
    zip: z.string().optional(),
    country: z.string().optional(),
    email: z.string().email().optional()
  }),
  buyer: z.object({
    name: z.string().min(1),
    vatNumber: z.string().optional(),
    street: z.string().optional(),
    city: z.string().optional(),
    zip: z.string().optional(),
    country: z.string().optional(),
    email: z.string().email().optional()
  }),
  lines: z.array(z.object({
    id: z.number().int().positive(),
    description: z.string().min(1),
    quantity: z.string().regex(/^-?\d+(\.\d+)?$/),
    unitPrice: z.string().regex(/^-?\d+(\.\d+)?$/),
    vatRate: z.union([z.literal(21), z.literal(12), z.literal(6), z.literal(0)]),
    unit: z.string().optional()
  })).min(1),
  payment: z.object({
    iban: z.string().optional(),
    bic: z.string().optional(),
    termsText: z.string().optional()
  }).optional()
})

export type ARInvoiceValidated = z.infer<typeof ARInvoiceSchema>
