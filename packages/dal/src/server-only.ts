// SERVER-SIDE ONLY FUNCTIONS
// These functions should NEVE<PERSON> be imported in client-side code
// They use service role permissions for administrative operations

import { createClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'

function createServiceClient() {
  const url = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url || !key) {
    throw new Error(
      'Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables'
    )
  }

  return createClient<Database>(url, key)
}

/**
 * SERVER ONLY: Create tenant (administrative operation)
 * This bypasses RLS for system-level tenant creation
 */
export async function createTenantAsAdmin(
  name: string,
  kind: 'SME' | 'FIRM',
  ownerId: string
) {
  const client = createServiceClient()

  const { data: tenant, error: tenantError } = await client
    .from('tenants')
    .insert({ name, kind })
    .select()
    .single()

  if (tenantError) throw tenantError

  // Grant owner role
  const { error: membershipError } = await client
    .from('tenant_memberships')
    .insert({
      tenant_id: tenant.id,
      user_id: ownerId,
      role: 'tenant_owner',
    })

  if (membershipError) throw membershipError

  return tenant
}

/**
 * SERVER ONLY: Get user by email (administrative operation)
 */
export async function getUserByEmail(email: string) {
  const client = createServiceClient()

  const {
    data: { users },
    error,
  } = await client.auth.admin.listUsers()
  const user = users?.find(u => u.email === email) || null

  if (error) throw error
  return user
}

/**
 * SERVER ONLY: Delete user account (administrative operation)
 */
export async function deleteUserAccount(userId: string) {
  const client = createServiceClient()

  const { error } = await client.auth.admin.deleteUser(userId)

  if (error) throw error
}
