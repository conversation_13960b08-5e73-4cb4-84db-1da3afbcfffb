/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */

import { createBrowserClient, createServerClient } from './client'
import type { Database } from '@belbooks/types'
import type { ExtendedSupabaseClient } from './client'

type Tenant = Database['public']['Tables']['tenants']['Row']
type TenantInsert = Database['public']['Tables']['tenants']['Insert']
type UserTenant = Database['public']['Views']['v_user_tenants']['Row']

/**
 * Get all tenants accessible to the current user
 */
export async function listUserTenants(userId?: string) {
  const client = createBrowserClient()

  let query = client.from('v_user_tenants').select('*')

  if (userId) {
    query = query.eq('user_id', userId)
  }

  const { data, error } = await query

  if (error) throw error
  return data as UserTenant[]
}

/**
 * Get a specific tenant by ID (server-side only)
 */
export async function getTenant(tenantId: number) {
  const client = createServerClient()

  const { data, error } = await client
    .from('tenants')
    .select('*')
    .eq('id', tenantId)
    .single()

  if (error) throw error
  return data as Tenant
}

/**
 * Create a new tenant (server-side only)
 */
export async function createTenant(tenant: TenantInsert) {
  const client = createServerClient()

  const { data, error } = await client
    .from('tenants')
    .insert(tenant)
    .select()
    .single()

  if (error) throw error
  return data as Tenant
}

/**
 * Check if user has specific role in tenant
 */
export async function hasTenanRole(
  tenantId: number,
  userId: string,
  roles: string[]
): Promise<boolean> {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('tenant_memberships')
    .select('role')
    .eq('tenant_id', tenantId)
    .eq('user_id', userId)
    .in('role', roles)
    .maybeSingle()

  if (error) throw error
  return !!data
}

/**
 * Grant tenant role to user (requires tenant admin)
 */
export async function grantTenantRole(
  tenantId: number,
  targetUserId: string,
  role: string
) {
  const client = createBrowserClient()

  const { error } = await client.rpc('rpc_grant_tenant_role', {
    p_tenant_id: tenantId,
    p_user_id: targetUserId,
    p_role: role,
  })

  if (error) throw error
}

/**
 * Create a tenant using RPC (client-side allowed for onboarding)
 */
export async function createTenantRPC(
  name: string,
  orgType: 'sme' | 'firm'
): Promise<number> {
  const client = createBrowserClient()

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data, error } = await (client as ExtendedSupabaseClient).rpc(
    'rpc_create_tenant',
    {
      p_name: name,
      p_org_type: orgType,
    }
  )

  if (error) throw error
  return data as number // Returns tenant ID
}
