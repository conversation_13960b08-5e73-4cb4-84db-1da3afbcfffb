import { createBrowserClient } from './client'
import type { Database } from '@belbooks/types'

type PendingInvite = Database['public']['Tables']['pending_invites']['Row']

/**
 * Create an invitation for tenant or entity access
 */
export async function createInvite(
  scope: 'tenant' | 'entity',
  scopeId: string,
  email: string,
  role: string
) {
  const client = createBrowserClient()

  const { data, error } = await client.rpc('rpc_invite', {
    p_email: email,
    p_tenant_id: scope === 'tenant' ? parseInt(scopeId) : undefined,
    p_entity_id: scope === 'entity' ? parseInt(scopeId) : undefined,
    p_role: role,
  })

  if (error) throw error
  return data as string | null // Returns token or null if user already exists
}

/**
 * Accept a pending invitation
 */
export async function acceptInvite(token: string) {
  const client = createBrowserClient()

  const { error } = await client.rpc('rpc_accept_invite', {
    p_token: token,
  })

  if (error) throw error
}

/**
 * Get pending invitations sent by current user
 */
export async function listSentInvites() {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('pending_invites')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) throw error
  return data as PendingInvite[]
}

/**
 * Get a specific pending invite by token
 */
export async function getPendingInvite(token: string) {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('pending_invites')
    .select('*')
    .eq('token', token)
    .gt('expires_at', new Date().toISOString())
    .single()

  if (error) throw error
  return data as PendingInvite
}

/**
 * Cancel a pending invitation
 */
export async function cancelInvite(token: string) {
  const client = createBrowserClient()

  const { error } = await client
    .from('pending_invites')
    .delete()
    .eq('token', token)

  if (error) throw error
}

/**
 * Check if a user has pending invitations
 */
export async function getPendingInvitesForUser(email: string) {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('pending_invites')
    .select('*')
    .eq('email', email)
    .gt('expires_at', new Date().toISOString())
    .order('created_at', { ascending: false })

  if (error) throw error
  return data as PendingInvite[]
}
