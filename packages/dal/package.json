{"name": "@belbooks/dal", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "typecheck": "tsc -p tsconfig.typecheck.json", "lint": "eslint src --ext .ts"}, "dependencies": {"@belbooks/types": "workspace:*", "@supabase/supabase-js": "^2.38.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.3"}}