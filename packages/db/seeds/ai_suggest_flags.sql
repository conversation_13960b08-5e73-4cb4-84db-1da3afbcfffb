-- Add AI Suggestions feature flag for testing entities
-- This should be run after basic seeds are loaded

-- Add AISuggestEnabled flag for test/development entities
INSERT INTO feature_flags (entity_id, flag, enabled, config, created_at, updated_at)
VALUES 
    -- Enable for entity ID 1 (typically the first test entity)
    (1, 'AISuggestEnabled', true, '{"max_suggestions": 5, "min_similarity": 0.3}', NOW(), NOW())
ON CONFLICT (entity_id, flag) DO UPDATE SET
    enabled = EXCLUDED.enabled,
    config = EXCLUDED.config,
    updated_at = NOW();

-- Note: In production, this flag should be controlled via admin interface
-- and enabled selectively for entities that want AI suggestions