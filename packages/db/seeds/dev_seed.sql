-- Development seed data
-- Creates sample tenant, entity, and basic chart of accounts

-- Create test tenant
INSERT INTO tenants (id, name) VALUES (1, 'Test Tenant') ON CONFLICT (
  id
) DO NOTHING;

-- Create test entity
INSERT INTO entities (id, tenant_id, name, currency, fiscal_year_start)
VALUES (1, 1, 'Test Company BVBA', 'EUR', '2024-01-01') ON CONFLICT (
  id
) DO NOTHING;

-- Create operating mode (default to ledger mode)
INSERT INTO operating_modes (entity_id, mode)
VALUES (1, 'ledger') ON CONFLICT (entity_id) DO NOTHING;

-- Create basic Belgian Chart of Accounts for the test entity
INSERT INTO accounts (
  entity_id, code, name, account_type, normal_balance
) VALUES
(1, '1000', 'FORMATION EXPENSES', 'asset', 'debit')
, (1, '1100', 'INTANGIBLE ASSETS', 'asset', 'debit')
, (1, '1200', 'TANGIBLE ASSETS', 'asset', 'debit')
, (1, '1300', 'FINANCIAL ASSETS', 'asset', 'debit')
, (1, '2000', 'RAW MATERIALS', 'asset', 'debit')
, (1, '2100', 'SUPPLIES', 'asset', 'debit')
, (1, '2900', 'ADVANCE PAYMENTS', 'asset', 'debit')
, (1, '3000', 'WORK IN PROGRESS', 'asset', 'debit')
, (1, '3100', 'FINISHED GOODS', 'asset', 'debit')
, (1, '3200', 'MERCHANDISE', 'asset', 'debit')
, (1, '4000', 'TRADE RECEIVABLES', 'asset', 'debit')
, (1, '4100', 'OTHER RECEIVABLES', 'asset', 'debit')
, (1, '4110', 'VAT RECEIVABLE', 'asset', 'debit')
, (1, '4400', 'PREPAID EXPENSES', 'asset', 'debit')
, (1, '4500', 'MARKETABLE SECURITIES', 'asset', 'debit')
, (1, '5500', 'BANK CURRENT ACCOUNT', 'asset', 'debit')
, (1, '5700', 'CASH', 'asset', 'debit')
, (1, '1000', 'CAPITAL', 'equity', 'credit')
, (1, '1400', 'RETAINED EARNINGS', 'equity', 'credit')
, (1, '4300', 'TRADE PAYABLES', 'liability', 'credit')
, (1, '4400', 'OTHER PAYABLES', 'liability', 'credit')
, (1, '4510', 'VAT PAYABLE', 'liability', 'credit')
, (1, '4600', 'ACCRUED EXPENSES', 'liability', 'credit')
, (1, '7000', 'SALES REVENUE', 'revenue', 'credit')
, (1, '7100', 'SERVICE REVENUE', 'revenue', 'credit')
, (1, '6000', 'COST OF GOODS SOLD', 'expense', 'debit')
, (1, '6100', 'RAW MATERIALS', 'expense', 'debit')
, (1, '6200', 'SALARY EXPENSES', 'expense', 'debit')
, (1, '6300', 'SOCIAL SECURITY', 'expense', 'debit')
, (1, '6400', 'OTHER PERSONNEL COSTS', 'expense', 'debit')
, (1, '6500', 'DEPRECIATION', 'expense', 'debit')
, (1, '6600', 'RENT EXPENSE', 'expense', 'debit')
, (1, '6700', 'UTILITIES', 'expense', 'debit')
, (1, '6800', 'OFFICE SUPPLIES', 'expense', 'debit')
, (1, '6900', 'OTHER EXPENSES', 'expense', 'debit')
ON CONFLICT (entity_id, code) DO NOTHING;

-- Create a sample bank account
INSERT INTO bank_accounts (
  entity_id, account_id, bank_name, account_number, iban
)
SELECT
  1
  , a.id
  , 'KBC Bank'
  , '****************'
  , '****************'
FROM accounts AS a
WHERE a.entity_id = 1 AND a.code = '5500'
ON CONFLICT (entity_id, account_number) DO NOTHING;

-- Note: User memberships will be created when users authenticate
-- The RLS policies will handle access control based on auth.uid()
