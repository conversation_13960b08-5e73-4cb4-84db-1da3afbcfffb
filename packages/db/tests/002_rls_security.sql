-- RLS Security Tests using pgTAP
-- Tests to verify Row Level Security policies prevent unauthorized access

BEGIN;

SELECT plan(20);

-- Create test users for RLS testing
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES
(
  '********-0000-0000-0000-************'::uuid
  , '<EMAIL>'
  , now()
  , now()
  , now()
)
, ('********-0000-0000-0000-************'::uuid, '<EMAIL>', now(), now(), now())
, ('********-0000-0000-0000-********0003'::uuid, '<EMAIL>', now(), now(), now());

-- Create test tenants
INSERT INTO tenants (id, name, kind)
VALUES
(1, 'Test SME Tenant', 'SME')
, (2, 'Test FIRM Tenant', 'FIRM')
, (3, 'Another SME Tenant', 'SME');

-- Create tenant memberships
INSERT INTO tenant_memberships (tenant_id, user_id, role)
VALUES
(1, '********-0000-0000-0000-************'::uuid, 'tenant_owner')
, (1, '********-0000-0000-0000-************'::uuid, 'tenant_member')
, (2, '********-0000-0000-0000-********0003'::uuid, 'tenant_owner');

-- Create test entities
INSERT INTO entities (id, tenant_id, name, currency)
VALUES
(1, 1, 'SME Entity 1', 'EUR')
, (2, 1, 'SME Entity 2', 'EUR')
, (3, 2, 'FIRM Entity 1', 'USD');

-- Create entity memberships
INSERT INTO entity_memberships (entity_id, user_id, role)
VALUES
(1, '********-0000-0000-0000-************'::uuid, 'owner')
, (2, '********-0000-0000-0000-************'::uuid, 'accountant')
, (3, '********-0000-0000-0000-********0003'::uuid, 'owner');

-- Test 1: Tenant RLS - Users can only see their own tenants
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenants'
  , ARRAY[1]
  , 'User 1 should only see tenants they belong to'
);

-- Test 2: Tenant RLS - Different user sees different tenants  
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0003"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenants'
  , ARRAY[1]
  , 'User 3 should only see their own tenants'
);

-- Test 3: Entity RLS - Entity member can see their entities
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM entities WHERE id = 1'
  , ARRAY[1]
  , 'Entity owner should see their entity'
);

-- Test 4: Entity RLS - User cannot see entities they are not members of
SELECT results_eq(
  'SELECT COUNT(*)::int FROM entities WHERE id = 3'
  , ARRAY[0]
  , 'User should not see entities from other tenants they do not belong to'
);

-- Test 5: Tenant admin can see all entities in their tenant
SELECT results_eq(
  'SELECT COUNT(*)::int FROM entities WHERE tenant_id = 1'
  , ARRAY[2]
  , 'Tenant owner should see all entities in their tenant'
);

-- Test 6: Pending invites RLS - Only inviter can see their invites
INSERT INTO pending_invites (scope, scope_id, email, role, inviter_user_id)
VALUES (
  'tenant'
  , '1'
  , '<EMAIL>'
  , 'tenant_member'
  , '********-0000-0000-0000-************'::uuid
);

SELECT results_eq(
  'SELECT COUNT(*)::int FROM pending_invites WHERE inviter_user_id = auth.uid()'
  , ARRAY[1]
  , 'User should see invites they created'
);

-- Test 7: Pending invites RLS - Other users cannot see invites
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM pending_invites'
  , ARRAY[0]
  , 'User should not see invites created by others'
);

-- Test 8: Tenant membership RLS - Members can see tenant memberships
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenant_memberships WHERE tenant_id = 1'
  , ARRAY[2]
  , 'Tenant owner should see all memberships in their tenant'
);

-- Test 9: Tenant membership RLS - Non-members cannot see memberships  
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0003"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenant_memberships WHERE tenant_id = 1'
  , ARRAY[0]
  , 'Non-member should not see memberships of other tenants'
);

-- Test 10: Entity membership RLS - Entity members can see entity memberships
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM entity_memberships WHERE entity_id = 1'
  , ARRAY[1]
  , 'Entity owner should see entity memberships'
);

-- Test 11: Data isolation - Journals are properly isolated by entity
-- Create test journals
INSERT INTO journals (
  id, entity_id, journal_type, description, transaction_date, created_by
)
VALUES
(
  1
  , 1
  , 'general'
  , 'Test Journal 1'
  , '2024-01-01'
  , '********-0000-0000-0000-************'::uuid
)
, (2, 2, 'general', 'Test Journal 2', '2024-01-01', '********-0000-0000-0000-************'::uuid)
, (3, 3, 'general', 'Test Journal 3', '2024-01-01', '********-0000-0000-0000-********0003'::uuid);

SELECT results_eq(
  'SELECT COUNT(*)::int FROM journals'
  , ARRAY[2] -- Should see journals from entities in tenant 1
  , 'User should see journals from accessible entities'
);

-- Test 12: Cannot access journals from other tenants
SELECT results_eq(
  'SELECT COUNT(*)::int FROM journals WHERE entity_id = 3'
  , ARRAY[0]
  , 'User should not see journals from other tenant entities'
);

-- Test 13: Accounts are properly isolated
INSERT INTO accounts (id, entity_id, code, name, account_type, is_active)
VALUES
(1, 1, '1000', 'Cash', 'asset', true)
, (2, 2, '1000', 'Cash', 'asset', true)
, (3, 3, '1000', 'Cash', 'asset', true);

SELECT results_eq(
  'SELECT COUNT(*)::int FROM accounts'
  , ARRAY[2] -- Should see accounts from entities in tenant 1
  , 'User should see accounts from accessible entities'
);

-- Test 14: Cannot access accounts from other tenants
SELECT results_eq(
  'SELECT COUNT(*)::int FROM accounts WHERE entity_id = 3'
  , ARRAY[0]
  , 'User should not see accounts from other tenant entities'
);

-- Test 15: Bank accounts isolation
INSERT INTO bank_accounts (
  id, entity_id, name, account_number, bank_name, currency
)
VALUES
(1, 1, 'Main Account', '*********', 'Test Bank', 'EUR')
, (2, 3, 'Firm Account', '*********', 'Other Bank', 'USD');

SELECT results_eq(
  'SELECT COUNT(*)::int FROM bank_accounts WHERE entity_id = 1'
  , ARRAY[1]
  , 'User should see bank accounts from their entities'
);

SELECT results_eq(
  'SELECT COUNT(*)::int FROM bank_accounts WHERE entity_id = 3'
  , ARRAY[0]
  , 'User should not see bank accounts from other tenant entities'
);

-- Test 16: Invoices isolation  
INSERT INTO invoices (
  id
  , entity_id
  , kind
  , number
  , counterparty_name
  , invoice_date
  , due_date
  , total_amount
  , vat_amount
  , status
)
VALUES
(
  1
  , 1
  , 'sale'
  , 'INV-001'
  , 'Customer A'
  , '2024-01-01'
  , '2024-01-31'
  , 1000.00
  , 200.00
  , 'draft'
)
, (2, 3, 'sale', 'INV-002', 'Customer B', '2024-01-01', '2024-01-31', 500.00, 100.00, 'draft');

SELECT results_eq(
  'SELECT COUNT(*)::int FROM invoices WHERE entity_id = 1'
  , ARRAY[1]
  , 'User should see invoices from their entities'
);

SELECT results_eq(
  'SELECT COUNT(*)::int FROM invoices WHERE entity_id = 3'
  , ARRAY[0]
  , 'User should not see invoices from other tenant entities'
);

-- Test 17: View-based access control - v_user_tenants
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_user_tenants'
  , ARRAY[1]
  , 'View should only show tenants for current user'
);

-- Test 18: View-based access control - v_user_entities  
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_user_entities'
  , ARRAY[2] -- User 1 has access to 2 entities in tenant 1
  , 'View should only show entities accessible to current user'
);

-- Test 19: Super admin cannot bypass RLS (no god mode)
-- Switch to a hypothetical super admin (should still be restricted by RLS)
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0999"}';

SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenants'
  , ARRAY[0]
  , 'Even admin users should be restricted by RLS if not explicitly granted access'
);

-- Test 20: RPC function security - tenant creation requires authentication
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-************"}';

SELECT lives_ok(
  'SELECT rpc_create_tenant(''New Tenant'', ''SME'')'
  , 'Authenticated user should be able to create tenant'
);

-- Verify the new tenant was created and user has owner role
SELECT results_eq(
  'SELECT COUNT(*)::int FROM tenant_memberships WHERE user_id = ''********-0000-0000-0000-************''::uuid AND role = ''tenant_owner'''
  , ARRAY[2] -- Original tenant + newly created tenant
  , 'User should be owner of both original and newly created tenant'
);

-- Clean up test data
DELETE FROM tenant_memberships;
DELETE FROM entity_memberships;
DELETE FROM pending_invites;
DELETE FROM journal_lines;
DELETE FROM journals;
DELETE FROM accounts;
DELETE FROM bank_accounts;
DELETE FROM invoices;
DELETE FROM entities;
DELETE FROM tenants;
DELETE FROM auth.users
WHERE email LIKE '%@example.com';

SELECT finish();

ROLLBACK;
