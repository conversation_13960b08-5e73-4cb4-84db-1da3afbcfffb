-- pgTAP tests for Track F Integrations migration
-- Tests RLS, table structure, constraints, and helper functions

BEGIN;

SELECT plan(42); -- Adjusted for comprehensive test coverage

-- Load required extensions
SELECT has_extension('pgtap');

-- =============================================================================
-- TABLE STRUCTURE TESTS
-- =============================================================================

-- Test that all tables exist
SELECT has_table('domain_events', 'domain_events table should exist');
SELECT has_table('export_jobs', 'export_jobs table should exist'); 
SELECT has_table('connector_configs', 'connector_configs table should exist');

-- Test domain_events enhancements
SELECT has_column('domain_events', 'dedupe_key', 'domain_events should have dedupe_key column');
SELECT has_column('domain_events', 'metadata', 'domain_events should have metadata column');

-- Test export_jobs structure
SELECT has_column('export_jobs', 'id', 'export_jobs should have id column');
SELECT has_column('export_jobs', 'entity_id', 'export_jobs should have entity_id column');
SELECT has_column('export_jobs', 'event_id', 'export_jobs should have event_id column');
SELECT has_column('export_jobs', 'connector', 'export_jobs should have connector column');
SELECT has_column('export_jobs', 'status', 'export_jobs should have status column');
SELECT has_column('export_jobs', 'content_hash', 'export_jobs should have content_hash column');
SELECT has_column('export_jobs', 'attempts', 'export_jobs should have attempts column');
SELECT has_column('export_jobs', 'last_error', 'export_jobs should have last_error column');
SELECT has_column('export_jobs', 'delivered_at', 'export_jobs should have delivered_at column');

-- Test connector_configs structure
SELECT has_column('connector_configs', 'entity_id', 'connector_configs should have entity_id column');
SELECT has_column('connector_configs', 'connector', 'connector_configs should have connector column');
SELECT has_column('connector_configs', 'config', 'connector_configs should have config column');

-- =============================================================================
-- CONSTRAINT TESTS
-- =============================================================================

-- Test export_jobs constraints
SELECT has_check('export_jobs', 'export_jobs_connector_check', 'export_jobs should have connector check constraint');
SELECT has_check('export_jobs', 'export_jobs_status_check', 'export_jobs should have status check constraint');
SELECT has_unique('export_jobs', ARRAY['entity_id', 'connector', 'content_hash'], 'export_jobs should have unique constraint on (entity_id, connector, content_hash)');

-- Test connector_configs constraints
SELECT has_check('connector_configs', 'connector_configs_connector_check', 'connector_configs should have connector check constraint');

-- =============================================================================
-- INDEX TESTS  
-- =============================================================================

-- Test critical indexes exist
SELECT has_index('domain_events', 'idx_domain_events_dedupe_key', 'domain_events should have dedupe_key index');
SELECT has_index('domain_events', 'idx_domain_events_type', 'domain_events should have event_type index');
SELECT has_index('export_jobs', 'idx_export_jobs_entity_status', 'export_jobs should have entity_status index');
SELECT has_index('export_jobs', 'idx_export_jobs_event_id', 'export_jobs should have event_id index');
SELECT has_index('export_jobs', 'idx_export_jobs_content_hash', 'export_jobs should have content_hash index');

-- =============================================================================
-- RLS TESTS
-- =============================================================================

-- Test RLS is enabled
SELECT is_rls_enabled('domain_events', 'domain_events should have RLS enabled');
SELECT is_rls_enabled('export_jobs', 'export_jobs should have RLS enabled');  
SELECT is_rls_enabled('connector_configs', 'connector_configs should have RLS enabled');

-- Test policies exist
SELECT has_policy('domain_events', 'domain_events_entity_access', 'domain_events should have entity access policy');
SELECT has_policy('export_jobs', 'export_jobs_entity_access', 'export_jobs should have entity access policy');
SELECT has_policy('connector_configs', 'connector_configs_entity_access', 'connector_configs should have entity access policy');

-- =============================================================================
-- FUNCTION TESTS
-- =============================================================================

-- Test helper functions exist
SELECT has_function('rpc_export_jobs_summary', ARRAY['bigint'], 'rpc_export_jobs_summary function should exist');
SELECT has_function('rpc_retry_export_job', ARRAY['bigint'], 'rpc_retry_export_job function should exist');
SELECT has_function('update_updated_at_column', 'update_updated_at_column trigger function should exist');

-- =============================================================================
-- DATA INTEGRITY TESTS WITH MOCK DATA
-- =============================================================================

-- Set up test data (will be rolled back)
INSERT INTO entities (id, name, company_name, type) VALUES 
  (9001, 'Test Entity Integration', 'Test Company Ltd', 'company');

INSERT INTO users (id, email) VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>');

INSERT INTO entity_memberships (entity_id, user_id, role) VALUES
  (9001, '11111111-1111-1111-1111-111111111111', 'owner');

-- Test domain events with dedupe_key
INSERT INTO domain_events (id, entity_id, event_type, event_data, dedupe_key, metadata) VALUES
  (8001, 9001, 'export_requested', '{"document_id": 123}', 'export_123_20241201', '{"source": "confirm_orchestrator"}'),
  (8002, 9001, 'export_requested', '{"document_id": 124}', 'export_124_20241201', '{"source": "manual"}');

-- Test export_jobs
INSERT INTO export_jobs (id, entity_id, event_id, connector, content_hash, status) VALUES
  (7001, 9001, 8001, 'winbooks_sftp', 'hash123', 'queued'),
  (7002, 9001, 8002, 'email', 'hash124', 'delivered');

-- Test unique constraint on export_jobs  
SELECT throws_ok(
  $$INSERT INTO export_jobs (entity_id, event_id, connector, content_hash) VALUES (9001, 8001, 'winbooks_sftp', 'hash123')$$,
  'duplicate key value violates unique constraint "unique_entity_connector_content"',
  'export_jobs should enforce unique constraint on (entity_id, connector, content_hash)'
);

-- Test connector_configs
INSERT INTO connector_configs (entity_id, connector, config) VALUES
  (9001, 'winbooks_sftp', '{"host": "sftp.example.com", "port": 22, "username": "test", "remote_dir": "/inbox"}');

-- =============================================================================
-- RPC FUNCTION INTEGRATION TESTS
-- =============================================================================

-- Test rpc_export_jobs_summary
SELECT set_config('request.jwt.claims', '{"sub": "11111111-1111-1111-1111-111111111111", "role": "authenticated"}', true);

-- Test that summary function works
SELECT results_eq(
  $$SELECT connector, queued_count, delivered_count FROM rpc_export_jobs_summary(9001) ORDER BY connector$$,
  $$VALUES ('email'::text, 0::bigint, 1::bigint), ('winbooks_sftp'::text, 1::bigint, 0::bigint)$$,
  'rpc_export_jobs_summary should return correct status counts'
);

-- Test retry function with failed job
UPDATE export_jobs SET status = 'failed', last_error = 'Connection timeout' WHERE id = 7001;

SELECT is(
  rpc_retry_export_job(7001),
  true,
  'rpc_retry_export_job should successfully retry failed job'
);

SELECT is(
  (SELECT status FROM export_jobs WHERE id = 7001),
  'queued',
  'Failed job should be reset to queued status after retry'
);

-- Test retry function rejects non-failed jobs
SELECT is(
  rpc_retry_export_job(7002), -- delivered job
  false,
  'rpc_retry_export_job should reject non-failed jobs'
);

-- Test retry function rejects non-existent jobs
SELECT is(
  rpc_retry_export_job(99999),
  false,
  'rpc_retry_export_job should reject non-existent jobs'
);

-- =============================================================================
-- RLS SECURITY TESTS
-- =============================================================================

-- Test unauthorized user cannot access export_jobs
SELECT set_config('request.jwt.claims', '{"sub": "22222222-2222-2222-2222-222222222222", "role": "authenticated"}', true);

SELECT is_empty(
  $$SELECT * FROM export_jobs WHERE entity_id = 9001$$,
  'Unauthorized user should not see export_jobs'
);

SELECT is_empty(
  $$SELECT * FROM connector_configs WHERE entity_id = 9001$$,
  'Unauthorized user should not see connector_configs'
);

-- Reset to authorized user for cleanup
SELECT set_config('request.jwt.claims', '{"sub": "11111111-1111-1111-1111-111111111111", "role": "authenticated"}', true);

-- =============================================================================
-- CLEANUP AND COMPLETE TESTS
-- =============================================================================

SELECT finish();
ROLLBACK;