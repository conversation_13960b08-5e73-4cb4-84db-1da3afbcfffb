-- pgTAP tests for Track B Confirm Orchestrator
-- Tests RLS policies, concurrency scenarios, and data integrity

-- Start transaction and plan tests
BEGIN;
SELECT plan(18);

-- Test 1: Verify audit columns exist
SELECT has_column('inbox_documents', 'confirmed_at', 'inbox_documents should have confirmed_at audit column');
SELECT has_column('inbox_documents', 'confirmed_by', 'inbox_documents should have confirmed_by audit column');

-- Test 2: Verify domain_events table exists with proper structure
SELECT has_table('domain_events', 'domain_events table should exist for export queueing');
SELECT has_column('domain_events', 'event_type', 'domain_events should have event_type column');
SELECT has_column('domain_events', 'event_data', 'domain_events should have event_data column');

-- Test 3: Verify advisory lock function exists
SELECT has_function('pg_try_advisory_xact_lock', ARRAY['integer', 'integer'], 'Advisory lock wrapper function should exist');

-- Test 4-8: RLS Policy Tests
-- Create test users and entity for RLS testing
INSERT INTO auth.users (id, email) VALUES 
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>'),
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>');

-- Create test tenant and entity
INSERT INTO tenants (id, name) VALUES (1, 'Test Tenant');
INSERT INTO entities (id, tenant_id, name) VALUES (1, 1, 'Test Entity');

-- Add entity membership for owner only
INSERT INTO entity_memberships (entity_id, user_id, role) VALUES 
  (1, '550e8400-e29b-41d4-a716-************', 'owner');

-- Test document for RLS testing  
INSERT INTO inbox_documents (id, entity_id, path, file_hash, mime_type, source, status) VALUES 
  (1, 1, 'test/document.pdf', 'abc123', 'application/pdf', 'upload', 'extracted');

-- Test 4: Entity member should be able to read document
SET LOCAL "request.jwt.claim.sub" = '550e8400-e29b-41d4-a716-************';
SELECT results_eq(
  'SELECT COUNT(*)::int FROM inbox_documents WHERE id = 1',
  ARRAY[1],
  'Entity member should be able to read inbox document'
);

-- Test 5: Non-member should NOT be able to read document
SET LOCAL "request.jwt.claim.sub" = '550e8400-e29b-41d4-a716-************';
SELECT results_eq(
  'SELECT COUNT(*)::int FROM inbox_documents WHERE id = 1',
  ARRAY[0],
  'Non-entity member should NOT be able to read inbox document (RLS enforcement)'
);

-- Test 6: Entity member should be able to update document
SET LOCAL "request.jwt.claim.sub" = '550e8400-e29b-41d4-a716-************';
UPDATE inbox_documents SET status = 'posted', confirmed_at = NOW(), confirmed_by = '550e8400-e29b-41d4-a716-************' WHERE id = 1;
SELECT results_eq(
  'SELECT status FROM inbox_documents WHERE id = 1',
  ARRAY['posted'::text],
  'Entity member should be able to update inbox document'
);

-- Test 7: Verify audit fields are properly set
SELECT is(
  (SELECT confirmed_by FROM inbox_documents WHERE id = 1),
  '550e8400-e29b-41d4-a716-************'::uuid,
  'confirmed_by should be properly set'
);

SELECT ok(
  (SELECT confirmed_at FROM inbox_documents WHERE id = 1) IS NOT NULL,
  'confirmed_at should be set after confirmation'
);

-- Test 8: Domain events should be accessible to entity members only
INSERT INTO domain_events (entity_id, event_type, event_data) VALUES 
  (1, 'export_requested', '{"document_id": 1}');

SELECT results_eq(
  'SELECT COUNT(*)::int FROM domain_events WHERE entity_id = 1',
  ARRAY[1],
  'Entity member should be able to read domain events'
);

-- Test 9: Non-member should not see domain events
SET LOCAL "request.jwt.claim.sub" = '550e8400-e29b-41d4-a716-************';
SELECT results_eq(
  'SELECT COUNT(*)::int FROM domain_events WHERE entity_id = 1',
  ARRAY[0],
  'Non-entity member should NOT be able to read domain events'
);

-- Test 10-12: Business Logic Validation Tests
-- Reset to entity member context
SET LOCAL "request.jwt.claim.sub" = '550e8400-e29b-41d4-a716-************';

-- Test 10: Verify journal balance validation (simulate via RPC call structure)
-- This tests the structure that would be passed to rpc_post_journal
SELECT ok(
  -- Simulate balanced journal lines
  abs(
    (100.00 + 21.00) - 121.00  -- debits - credits
  ) < 0.01,
  'Journal lines should balance (debits = credits)'
);

-- Test 11: Verify unbalanced journal detection
SELECT ok(
  -- Simulate unbalanced journal lines  
  abs(
    (100.00 + 20.00) - 121.00  -- debits - credits (off by 1.00)
  ) > 0.01,
  'Unbalanced journal lines should be detected'
);

-- Test 12: Verify idempotency - document already processed
INSERT INTO inbox_documents (id, entity_id, path, file_hash, mime_type, source, status, posted_journal_id, confirmed_at, confirmed_by) 
VALUES (2, 1, 'test/document2.pdf', 'def456', 'application/pdf', 'upload', 'posted', 123, NOW(), '550e8400-e29b-41d4-a716-************');

SELECT ok(
  (SELECT posted_journal_id FROM inbox_documents WHERE id = 2) IS NOT NULL,
  'Previously processed document should have posted_journal_id set'
);

-- Test 13-14: Advisory Lock Function Tests
-- Test 13: Verify advisory lock can be acquired
SELECT ok(
  (SELECT pg_try_advisory_xact_lock(42, 999)) = true,
  'Advisory lock should be acquired successfully'
);

-- Test 14: Verify same lock cannot be acquired again in same transaction
SELECT ok(
  (SELECT pg_try_advisory_xact_lock(42, 999)) = true,  -- Same session can re-acquire
  'Same session can re-acquire advisory lock'
);

-- Test 15-16: Status Transition Tests
-- Test 15: Valid status transitions
UPDATE inbox_documents SET status = 'suggested' WHERE id = 1;
SELECT results_eq(
  'SELECT status FROM inbox_documents WHERE id = 1', 
  ARRAY['suggested'::text],
  'Should allow transition from posted to suggested (for test setup)'
);

-- Test 16: Verify extraction and suggestion fields can store JSON
UPDATE inbox_documents SET 
  extraction = '{"supplier": {"name": "Test Supplier"}, "invoice": {"number": "INV001", "issueDate": "2024-01-01", "currency": "EUR", "net": "100.00", "vat": "21.00", "gross": "121.00"}, "lines": []}',
  suggestion = '{"journalDate": "2024-01-01", "description": "Test", "lines": []}'
WHERE id = 1;

SELECT ok(
  (SELECT extraction->'supplier'->>'name' FROM inbox_documents WHERE id = 1) = 'Test Supplier',
  'Extraction JSON field should store and retrieve data correctly'
);

-- Test 17-18: Constraint and Index Tests
-- Test 17: Verify unique constraint on entity + file_hash works
DO $$
BEGIN
  -- Try to insert duplicate file_hash for same entity
  INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
  VALUES (1, 'test/duplicate.pdf', 'abc123', 'application/pdf', 'upload', 'uploaded');
  
  -- Should fail due to unique constraint
  RAISE EXCEPTION 'Should not reach here - unique constraint should prevent duplicate';
EXCEPTION 
  WHEN unique_violation THEN
    -- Expected - unique constraint working
    NULL;
END $$;

SELECT pass('Unique constraint on entity_id + file_hash should prevent duplicates');

-- Test 18: Verify indexes exist for performance
SELECT has_index('inbox_documents', 'idx_inbox_documents_confirmed_by', 'Should have index on confirmed_by for audit queries');

-- Clean up test data
ROLLBACK;