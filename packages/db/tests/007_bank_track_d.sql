-- pgTAP tests for Track D bank enhancements

BEGIN;

SELECT plan(10);

-- Structure checks
SELECT has_column(
  'bank_transactions'
  , 'dedupe_hash'
  , 'bank_transactions.dedupe_hash exists'
);
SELECT has_column(
  'bank_transactions'
  , 'batch_id'
  , 'bank_transactions.batch_id exists'
);
SELECT has_column(
  'bank_transactions'
  , 'status'
  , 'bank_transactions.status exists'
);
SELECT has_column(
  'bank_transactions'
  , 'raw_json'
  , 'bank_transactions.raw_json exists'
);

-- Status constraint
SELECT throws_ok(
  $$UPDATE bank_transactions SET status = 'invalid_status'
    WHERE id = -1$$
  , 23514
  , NULL
  , 'Invalid status is rejected by CHECK constraint'
);

-- Unique constraint presence
SELECT ok(
  EXISTS (
    SELECT 1
    FROM pg_constraint
    WHERE conname = 'uq_bank_transactions_account_hash'
  )
  , 'Unique constraint on (bank_account_id, dedupe_hash) exists'
);

-- Link table checks
SELECT has_table('bank_tx_links', 'bank_tx_links table exists');
SELECT has_column('bank_tx_links', 'kind', 'bank_tx_links.kind exists');
SELECT ok(
  EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE indexname = 'idx_btl_entity_tx'
  )
  , 'Index idx_btl_entity_tx exists'
);

-- RLS on link table
SELECT ok(
  EXISTS (
    SELECT 1
    FROM pg_tables
    WHERE
      tablename = 'bank_tx_links'
      AND rowsecurity = TRUE
  )
  , 'RLS enabled on bank_tx_links'
);

SELECT finish();

ROLLBACK;
