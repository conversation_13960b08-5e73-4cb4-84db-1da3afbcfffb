-- Database invariant tests
-- Tests that critical constraints and RLS policies work correctly

-- Test 1: Balanced journal constraint
DO $$
DECLARE
  test_entity_id BIGINT := 1;
  test_journal_id BIGINT;
  test_account1_id BIGINT;
  test_account2_id BIGINT;
  error_caught BOOLEAN := FALSE;
BEGIN
  -- Get test accounts
  SELECT id INTO test_account1_id FROM accounts WHERE entity_id = test_entity_id AND code = '5500' LIMIT 1;
  SELECT id INTO test_account2_id FROM accounts WHERE entity_id = test_entity_id AND code = '7000' LIMIT 1;
  
  IF test_account1_id IS NULL OR test_account2_id IS NULL THEN
    RAISE NOTICE 'SKIP: Test accounts not found - ensure dev_seed.sql has been run';
    RETURN;
  END IF;
  
  -- Create a test journal
  INSERT INTO journals (entity_id, description, transaction_date)
  VALUES (test_entity_id, 'Test Unbalanced Journal', CURRENT_DATE)
  RETURNING id INTO test_journal_id;
  
  -- Add unbalanced lines (should fail)
  BEGIN
    INSERT INTO journal_lines (journal_id, account_id, description, debit_amount) 
    VALUES (test_journal_id, test_account1_id, 'Unbalanced debit', 100.00);
    
    -- This should trigger the balance constraint error
    COMMIT;
    
    -- If we reach here, the test failed
    RAISE EXCEPTION 'TEST FAILED: Unbalanced journal was allowed';
    
  EXCEPTION 
    WHEN OTHERS THEN
      error_caught := TRUE;
      ROLLBACK;
  END;
  
  IF error_caught THEN
    RAISE NOTICE 'PASS: Unbalanced journal correctly rejected';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: Unbalanced journal was not rejected';
  END IF;
  
END $$;

-- Test 2: Journal lines append-only constraint
DO $$
DECLARE
  test_entity_id BIGINT := 1;
  test_journal_id BIGINT;
  test_account_id BIGINT;
  test_line_id BIGINT;
  error_caught BOOLEAN := FALSE;
BEGIN
  -- Get test account
  SELECT id INTO test_account_id FROM accounts WHERE entity_id = test_entity_id AND code = '5500' LIMIT 1;
  
  IF test_account_id IS NULL THEN
    RAISE NOTICE 'SKIP: Test account not found - ensure dev_seed.sql has been run';
    RETURN;
  END IF;
  
  -- Create a balanced journal for testing
  INSERT INTO journals (entity_id, description, transaction_date)
  VALUES (test_entity_id, 'Test Append Only', CURRENT_DATE)
  RETURNING id INTO test_journal_id;
  
  -- Add balanced lines
  INSERT INTO journal_lines (journal_id, account_id, description, debit_amount) 
  VALUES (test_journal_id, test_account_id, 'Test debit', 100.00)
  RETURNING id INTO test_line_id;
  
  INSERT INTO journal_lines (journal_id, account_id, description, credit_amount) 
  VALUES (test_journal_id, test_account_id, 'Test credit', 100.00);
  
  -- Try to update the line (should fail)
  BEGIN
    UPDATE journal_lines SET debit_amount = 200.00 WHERE id = test_line_id;
    RAISE EXCEPTION 'TEST FAILED: Journal line update was allowed';
  EXCEPTION 
    WHEN OTHERS THEN
      error_caught := TRUE;
      ROLLBACK;
  END;
  
  IF error_caught THEN
    RAISE NOTICE 'PASS: Journal line modification correctly prevented';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: Journal line modification was not prevented';
  END IF;
  
END $$;

-- Test 3: RLS basic functionality (simulated)
DO $$
BEGIN
  -- Note: Full RLS testing requires authenticated sessions
  -- This test verifies the policies exist and are enabled
  
  DECLARE
    rls_count INTEGER;
  BEGIN
    SELECT COUNT(*) INTO rls_count
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'public'
      AND c.relname IN ('tenants', 'entities', 'accounts', 'journals', 'journal_lines')
      AND c.relrowsecurity = true;
      
    IF rls_count = 5 THEN
      RAISE NOTICE 'PASS: RLS enabled on critical tables';
    ELSE
      RAISE EXCEPTION 'TEST FAILED: RLS not enabled on all required tables (found % of 5)', rls_count;
    END IF;
  END;
END $$;

-- Test 4: VAT codes exist
DO $$
DECLARE
  vat_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO vat_count
  FROM vat_codes 
  WHERE entity_id IS NULL 
    AND code IN ('BE21', 'BE12', 'BE06', 'BE00');
    
  IF vat_count >= 4 THEN
    RAISE NOTICE 'PASS: Belgian VAT codes seeded correctly';
  ELSE
    RAISE EXCEPTION 'TEST FAILED: Missing Belgian VAT codes (found % of 4+)', vat_count;
  END IF;
END $$;

DO $$
BEGIN
  RAISE NOTICE 'All database invariant tests completed successfully';
END $$;