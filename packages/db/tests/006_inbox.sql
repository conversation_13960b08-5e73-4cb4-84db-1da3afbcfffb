-- pgTAP tests for inbox functionality
-- Tests RLS policies, data integrity, and business rules

BEGIN;

-- Import the TAP functions
SELECT plan(35);

-- Test 1: Inbox documents table structure
SELECT has_table('inbox_documents', 'inbox_documents table should exist');

-- Test 2: Required columns exist
SELECT
  has_column('inbox_documents', 'id', 'inbox_documents.id column should exist');
SELECT
  has_column('inbox_documents', 'entity_id', 'inbox_documents.entity_id column should exist');
SELECT
  has_column('inbox_documents', 'file_hash', 'inbox_documents.file_hash column should exist');
SELECT
  has_column('inbox_documents', 'status', 'inbox_documents.status column should exist');
SELECT
  has_column('inbox_documents', 'extraction', 'inbox_documents.extraction column should exist');

-- Test 3: RLS is enabled
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_tables
    WHERE tablename = 'inbox_documents' AND rowsecurity = true
  )
  , 'RLS should be enabled on inbox_documents table'
);

-- Test 4: Unique constraint on entity_id + file_hash
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_indexes
    WHERE
      tablename = 'inbox_documents'
      AND indexname = 'uq_inbox_documents_entity_filehash'
  )
  , 'Unique index on entity_id + file_hash should exist'
);

-- Test 5: Status constraint allows only valid values
-- Create a test entity first (this will be rolled back)
INSERT INTO tenants (name) VALUES ('test-tenant');
INSERT INTO entities (tenant_id, name)
SELECT
  id
  , 'test-entity'
FROM tenants
WHERE name = 'test-tenant';

-- Try to insert invalid status (should fail)
SELECT throws_ok(
  $$INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
      VALUES (1, 'test/path', 'hash123', 'application/pdf', 'upload', 'invalid_status')$$
  , 23514
  , null
  , 'Invalid status should be rejected by CHECK constraint'
);

-- Test 6: Valid status should work
SELECT lives_ok(
  $$INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
      VALUES (1, 'test/path', 'hash123', 'application/pdf', 'upload', 'uploaded')$$
  , 'Valid status should be accepted'
);

-- Test 7: Feature flags table structure
SELECT has_table('feature_flags', 'feature_flags table should exist');

-- Test 8: Feature flags primary key on (entity_id, flag)
SELECT ok(
  EXISTS (
    SELECT 1 FROM information_schema.table_constraints AS tc
    INNER JOIN
      information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    WHERE
      tc.table_name = 'feature_flags'
      AND tc.constraint_type = 'PRIMARY KEY'
      AND kcu.column_name IN ('entity_id', 'flag')
  )
  , 'Primary key on (entity_id, flag) should exist'
);

-- Test 9: Supplier templates table structure
SELECT has_table('supplier_templates', 'supplier_templates table should exist');

-- Test 10: Supplier templates unique constraint
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_indexes
    WHERE
      tablename = 'supplier_templates'
      AND indexname LIKE '%entity_id%supplier_vat%'
  )
  , 'Unique constraint on entity_id + supplier_vat should exist'
);

-- Test RLS policies - these require proper user context
-- For now, just test that policies exist

-- Test 11: Inbox documents select policy exists
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_policies
    WHERE
      tablename = 'inbox_documents'
      AND policyname = 'inbox_documents_select'
      AND cmd = 'SELECT'
  )
  , 'inbox_documents_select policy should exist'
);

-- Test 12: Inbox documents insert policy exists
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_policies
    WHERE
      tablename = 'inbox_documents'
      AND policyname = 'inbox_documents_insert'
      AND cmd = 'INSERT'
  )
  , 'inbox_documents_insert policy should exist'
);

-- Test 13: Inbox documents update policy exists
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_policies
    WHERE
      tablename = 'inbox_documents'
      AND policyname = 'inbox_documents_update'
      AND cmd = 'UPDATE'
  )
  , 'inbox_documents_update policy should exist'
);

-- Test 14: Inbox documents delete policy exists
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_policies
    WHERE
      tablename = 'inbox_documents'
      AND policyname = 'inbox_documents_delete'
      AND cmd = 'DELETE'
  )
  , 'inbox_documents_delete policy should exist'
);

-- Test 15: Feature flags RLS policies exist
SELECT ok(
  EXISTS (
    SELECT 1 FROM pg_policies
    WHERE
      tablename = 'feature_flags'
      AND policyname = 'feature_flags_select'
  )
  , 'feature_flags_select policy should exist'
);

-- Test 16: Updated_at trigger exists for inbox_documents
SELECT ok(
  EXISTS (
    SELECT 1 FROM information_schema.triggers
    WHERE
      event_object_table = 'inbox_documents'
      AND trigger_name = 'inbox_documents_updated_at'
  )
  , 'inbox_documents updated_at trigger should exist'
);

-- Test 17: Test file hash deduplication
-- First insert should succeed
SELECT lives_ok(
  $$INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
      VALUES (1, 'test/path2', 'hash456', 'application/pdf', 'upload', 'uploaded')$$
  , 'First document with unique hash should be accepted'
);

-- Test 18: Duplicate hash should fail
SELECT throws_ok(
  $$INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
      VALUES (1, 'test/path3', 'hash456', 'application/pdf', 'upload', 'uploaded')$$
  , 23505
  , null
  , 'Duplicate file_hash for same entity should be rejected'
);

-- Test 19: Different entity can have same hash
INSERT INTO entities (tenant_id, name)
SELECT
  id
  , 'test-entity-2'
FROM tenants
WHERE name = 'test-tenant';
SELECT lives_ok(
  $$INSERT INTO inbox_documents (entity_id, path, file_hash, mime_type, source, status) 
      VALUES (2, 'test/path4', 'hash456', 'application/pdf', 'upload', 'uploaded')$$
  , 'Same file_hash for different entity should be allowed'
);

-- Test 20: JSON columns accept valid JSON
SELECT lives_ok(
  $$UPDATE inbox_documents SET extraction = '{"supplier": {"name": "Test"}}' WHERE id = 1$$
  , 'Valid JSON should be accepted in extraction column'
);

-- Test 21: Confidence constraint validation
SELECT throws_ok(
  $$UPDATE inbox_documents SET confidence = 1.5 WHERE id = 1$$
  , 23514
  , null
  , 'Confidence > 1 should be rejected'
);

-- Test 22: Confidence constraint validation (negative)
SELECT throws_ok(
  $$UPDATE inbox_documents SET confidence = -0.1 WHERE id = 1$$
  , 23514
  , null
  , 'Negative confidence should be rejected'
);

-- Test 23: Valid confidence values
SELECT lives_ok(
  $$UPDATE inbox_documents SET confidence = 0.95 WHERE id = 1$$
  , 'Valid confidence (0.95) should be accepted'
);

-- Test 24: Source constraint validation
SELECT throws_ok(
  $$UPDATE inbox_documents SET source = 'invalid_source' WHERE id = 1$$
  , 23514
  , null
  , 'Invalid source should be rejected by CHECK constraint'
);

-- Test 25: Valid source values
SELECT lives_ok(
  $$UPDATE inbox_documents SET source = 'email' WHERE id = 1$$
  , 'Valid source (email) should be accepted'
);

-- Test 26: Status transition validation
SELECT lives_ok(
  $$UPDATE inbox_documents SET status = 'extracted' WHERE id = 1$$
  , 'Status can be updated to extracted'
);

-- Test 27: Status transition validation
SELECT lives_ok(
  $$UPDATE inbox_documents SET status = 'suggested' WHERE id = 1$$
  , 'Status can be updated to suggested'
);

-- Test 28: RLS policy prevents cross-entity access
INSERT INTO entities (tenant_id, name)
SELECT
  id
  , 'test-entity-3'
FROM tenants
WHERE name = 'test-tenant';

-- Create a document for entity 1
INSERT INTO inbox_documents (
  entity_id, path, file_hash, mime_type, source, status
)
VALUES (
  1, 'test/cross-entity', 'hash999', 'application/pdf', 'upload', 'uploaded'
);

-- Try to access it as entity 2 (should be empty result due to RLS)
SELECT is(
  (
    SELECT count(*)::int FROM inbox_documents
    WHERE entity_id = 1 AND file_hash = 'hash999'
  )
  , 1
  , 'Document exists for entity 1'
);

-- Test 29: Feature flags composite primary key works
SELECT lives_ok(
  $$INSERT INTO feature_flags (entity_id, flag, enabled) VALUES (1, 'TestFlag', true)$$
  , 'Feature flag insertion should succeed'
);

-- Test 30: Feature flags duplicate prevention
SELECT throws_ok(
  $$INSERT INTO feature_flags (entity_id, flag, enabled) VALUES (1, 'TestFlag', false)$$
  , 23505
  , null
  , 'Duplicate feature flag should be rejected'
);

-- Test 31: Feature flags different entity same flag allowed
SELECT lives_ok(
  $$INSERT INTO feature_flags (entity_id, flag, enabled) VALUES (2, 'TestFlag', true)$$
  , 'Same flag name for different entity should be allowed'
);

-- Test 32: Supplier templates unique constraint
SELECT lives_ok(
  $$INSERT INTO supplier_templates (entity_id, supplier_vat, supplier_name, default_account_id) 
      VALUES (1, 'BE0123456789', 'Test Supplier', 6000)$$
  , 'Supplier template insertion should succeed'
);

-- Test 33: Supplier templates VAT uniqueness per entity
SELECT throws_ok(
  $$INSERT INTO supplier_templates (entity_id, supplier_vat, supplier_name) 
      VALUES (1, 'BE0123456789', 'Different Supplier')$$
  , 23505
  , null
  , 'Duplicate supplier VAT for same entity should be rejected'
);

-- Test 34: Supplier templates same VAT different entity allowed
SELECT lives_ok(
  $$INSERT INTO supplier_templates (entity_id, supplier_vat, supplier_name) 
      VALUES (2, 'BE0123456789', 'Test Supplier Entity 2')$$
  , 'Same supplier VAT for different entity should be allowed'
);

-- Test 35: Complex JSON extraction validation
SELECT lives_ok(
  $$UPDATE inbox_documents 
      SET extraction = '{
        "supplier": {"name": "Test Co", "vat": "BE0123456789"},
        "invoice": {"number": "INV-001", "issueDate": "2024-08-26", "currency": "EUR", "net": "100.00", "vat": "21.00", "gross": "121.00"},
        "lines": [{"description": "Test item", "quantity": "1", "unitPrice": "100.00", "vatRate": 21}],
        "confidence": 0.95
      }' 
      WHERE id = 1$$
  , 'Complex extraction JSON should be accepted'
);

-- Finish the test suite
SELECT finish();

ROLLBACK;
