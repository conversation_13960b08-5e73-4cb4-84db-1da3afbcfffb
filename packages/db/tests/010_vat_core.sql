-- VAT Core Tests using pgTAP
-- Tests to verify VAT calculation, grid mapping, and RLS security

BEGIN;

SELECT plan(25);

-- Create test users for RLS testing
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES
('********-0000-0000-0000-********0001'::uuid, '<EMAIL>', now(), now(), now()),
('********-0000-0000-0000-********0002'::uuid, '<EMAIL>', now(), now(), now());

-- Create test tenants
INSERT INTO tenants (id, name)
VALUES
(1, 'VAT Test Tenant 1'),
(2, 'VAT Test Tenant 2');

-- Create tenant memberships
INSERT INTO tenant_memberships (tenant_id, user_id, role)
VALUES
(1, '********-0000-0000-0000-********0001'::uuid, 'owner'),
(2, '********-0000-0000-0000-********0002'::uuid, 'owner');

-- Create test entities with VAT enabled
INSERT INTO entities (id, tenant_id, name, currency)
VALUES
(1, 1, 'VAT Entity 1', 'EUR'),
(2, 2, 'VAT Entity 2', 'EUR');

-- Create entity memberships
INSERT INTO entity_memberships (entity_id, user_id, role)
VALUES
(1, '********-0000-0000-0000-********0001'::uuid, 'owner'),
(2, '********-0000-0000-0000-********0002'::uuid, 'owner');

-- Enable VAT for entity 1
INSERT INTO operating_modes (entity_id, mode, config)
VALUES
(1, 'ledger', '{"VATEnabled": true}'),
(2, 'ledger', '{"VATEnabled": false}');

-- Create test accounts with proper types
INSERT INTO accounts (id, entity_id, code, name, account_type, type, normal_balance, is_active)
VALUES
-- Entity 1 accounts
(1, 1, '1000', 'Cash', 'asset', 'asset', 'debit', true),
(2, 1, '4000', 'Sales Revenue', 'revenue', 'income', 'credit', true),
(3, 1, '5000', 'Cost of Sales', 'expense', 'expense', 'debit', true),
(4, 1, '2100', 'VAT Payable', 'liability', 'vat', 'credit', true),
(5, 1, '1210', 'VAT Receivable', 'asset', 'vat', 'debit', true),
-- Entity 2 accounts
(6, 2, '1000', 'Cash', 'asset', 'asset', 'debit', true),
(7, 2, '4000', 'Sales Revenue', 'revenue', 'income', 'credit', true);

-- Create entity-specific VAT codes (inheriting from system defaults)
INSERT INTO vat_codes (id, entity_id, code, name, rate, direction, label, is_purchase, is_sale, is_active)
VALUES
(100, 1, 'BE21', 'Belgian VAT 21% (Entity 1)', 0.2100, 'output', 'Belgian VAT 21% Sales', false, true, true),
(101, 1, 'BE21', 'Belgian VAT 21% (Entity 1)', 0.2100, 'input', 'Belgian VAT 21% Purchases', true, false, true),
(102, 1, 'BE06', 'Belgian VAT 6% (Entity 1)', 0.0600, 'output', 'Belgian VAT 6% Sales', false, true, true);

-- Create VAT grid mappings for entity 1 VAT codes
INSERT INTO vat_grid_mapping (vat_code_id, grid_id)
SELECT 100, id FROM vat_grids WHERE grid_code = 'BE_OUT_21'
UNION ALL
SELECT 101, id FROM vat_grids WHERE grid_code = 'BE_IN_21'  
UNION ALL
SELECT 102, id FROM vat_grids WHERE grid_code = 'BE_OUT_6';

-- Create test journals with VAT transactions
INSERT INTO journals (id, entity_id, journal_type, description, transaction_date, created_by, is_balanced)
VALUES
(1, 1, 'sales', 'Sale with 21% VAT', '2024-03-15', '********-0000-0000-0000-********0001'::uuid, false),
(2, 1, 'purchase', 'Purchase with 21% VAT', '2024-03-16', '********-0000-0000-0000-********0001'::uuid, false),
(3, 1, 'sales', 'Sale with 6% VAT', '2024-03-17', '********-0000-0000-0000-********0001'::uuid, false),
(4, 2, 'sales', 'Entity 2 Sale', '2024-03-15', '********-0000-0000-0000-********0002'::uuid, false);

-- Create journal lines for sales with output VAT
-- Journal 1: €1000 sale with €210 VAT (21%)
INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
-- Cash receipt
(1, 1, 1, 'Cash received', 1210.00, NULL, NULL),
-- Sales revenue (base amount)
(2, 1, 2, 'Sales revenue', NULL, 1000.00, 100),
-- VAT payable
(3, 1, 4, 'Output VAT 21%', NULL, 210.00, 100);

-- Journal 2: €500 purchase with €105 VAT (21%)
INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
-- Expense (base amount)
(4, 2, 3, 'Purchase expense', 500.00, NULL, 101),
-- VAT receivable
(5, 2, 5, 'Input VAT 21%', 105.00, NULL, 101),
-- Cash payment
(6, 2, 1, 'Cash paid', NULL, 605.00, NULL);

-- Journal 3: €300 sale with €18 VAT (6%)
INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
-- Cash receipt
(7, 3, 1, 'Cash received', 318.00, NULL, NULL),
-- Sales revenue (base amount)
(8, 3, 2, 'Sales revenue', NULL, 300.00, 102),
-- VAT payable
(9, 3, 4, 'Output VAT 6%', NULL, 18.00, 102);

-- Journal 4: Entity 2 sale (no VAT codes)
INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
(10, 4, 6, 'Cash', 1000.00, NULL, NULL),
(11, 4, 7, 'Sales', NULL, 1000.00, NULL);

-- Update journal balanced flags
UPDATE journals SET is_balanced = true WHERE id IN (1, 2, 3, 4);

-- Set user context for testing
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0001"}';

-- Test 1: VAT grids are readable by authenticated users
SELECT ok(
  (SELECT COUNT(*) FROM vat_grids) >= 8,
  'VAT grids should be readable and contain Belgian grids'
);

-- Test 2: VAT grid mappings respect RLS
SELECT results_eq(
  'SELECT COUNT(*)::int FROM vat_grid_mapping',
  ARRAY[3], -- Entity 1 has 3 mapped VAT codes
  'User should see VAT grid mappings for accessible VAT codes'
);

-- Test 3: v_journal_lines_signed view works correctly
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_journal_lines_signed WHERE entity_id = 1',
  ARRAY[9], -- 9 journal lines for entity 1
  'Signed journal lines view should show all lines for accessible entity'
);

-- Test 4: v_vat_bases extracts base amounts correctly
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_bases WHERE entity_id = 1',
  ARRAY[3], -- 3 base amounts (2 sales, 1 purchase)
  'VAT bases view should extract base amounts from income/expense lines'
);

-- Test 5: Base amounts are calculated correctly
SELECT results_eq(
  'SELECT base_amount FROM v_vat_bases WHERE entity_id = 1 AND vat_code_id = 100 ORDER BY tx_date',
  ARRAY[1000.00], -- €1000 sales base
  'Output VAT base amount should be calculated correctly'
);

-- Test 6: v_vat_lines extracts VAT amounts from VAT accounts
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_lines WHERE entity_id = 1',
  ARRAY[3], -- 3 VAT lines (2 output, 1 input)
  'VAT lines view should extract VAT amounts from VAT accounts'
);

-- Test 7: VAT amounts are calculated correctly
SELECT results_eq(
  'SELECT vat_amount_booked FROM v_vat_lines WHERE entity_id = 1 AND vat_code_id = 100 ORDER BY tx_date',
  ARRAY[210.00], -- €210 output VAT
  'Output VAT amount should be extracted correctly'
);

-- Test 8: v_vat_daily combines base and VAT amounts
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_daily WHERE entity_id = 1',
  ARRAY[3], -- 3 daily VAT entries
  'Daily VAT view should combine base and VAT amounts'
);

-- Test 9: Daily VAT prefers booked VAT over computed VAT
SELECT results_eq(
  'SELECT vat_amount FROM v_vat_daily WHERE entity_id = 1 AND vat_code_id = 100',
  ARRAY[210.00], -- Uses booked VAT amount, not computed (1000 * 0.21 = 210)
  'Daily VAT should prefer booked VAT amount over computed'
);

-- Test 10: v_vat_period aggregates by month
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_period WHERE entity_id = 1',
  ARRAY[3], -- All transactions in March 2024
  'Period VAT view should aggregate transactions by month'
);

-- Test 11: Period aggregation sums amounts correctly  
SELECT results_eq(
  'SELECT base_total FROM v_vat_period WHERE entity_id = 1 AND vat_code_id = 100',
  ARRAY[1000.00], -- €1000 total base for 21% output VAT
  'Period view should sum base amounts correctly'
);

-- Test 12: Grid mapping works in period view
SELECT results_eq(
  'SELECT DISTINCT grid_code FROM v_vat_period WHERE entity_id = 1 ORDER BY grid_code',
  ARRAY['BE_IN_21', 'BE_OUT_21', 'BE_OUT_6'], -- All mapped grids
  'Period view should include proper grid codes'
);

-- Test 13: RPC VAT preview function works
SELECT results_eq(
  'SELECT COUNT(*)::int FROM rpc_vat_preview(1, ''2024-03-01'', ''2024-03-31'')',
  ARRAY[3], -- 3 different grid/direction combinations
  'VAT preview RPC should return aggregated results'
);

-- Test 14: RPC VAT preview returns correct output VAT total
SELECT results_eq(
  'SELECT vat_total FROM rpc_vat_preview(1, ''2024-03-01'', ''2024-03-31'') WHERE grid_code = ''BE_OUT_21''',
  ARRAY[210.00], -- €210 output VAT for 21% rate
  'VAT preview should return correct output VAT totals'
);

-- Test 15: RPC VAT preview returns correct input VAT total
SELECT results_eq(
  'SELECT vat_total FROM rpc_vat_preview(1, ''2024-03-01'', ''2024-03-31'') WHERE grid_code = ''BE_IN_21''',
  ARRAY[105.00], -- €105 input VAT for 21% rate
  'VAT preview should return correct input VAT totals'
);

-- Test 16: RPC CSV export works
SELECT ok(
  length(rpc_vat_export_csv(1, '2024-03-01', '2024-03-31')) > 100,
  'VAT CSV export should return formatted CSV data'
);

-- Test 17: CSV export contains proper headers
SELECT like(
  rpc_vat_export_csv(1, '2024-03-01', '2024-03-31'),
  'grid_code,direction,base_total,vat_total%',
  'CSV export should contain proper headers'
);

-- Test 18: Entity isolation - User 1 cannot see Entity 2 VAT data
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_period WHERE entity_id = 2',
  ARRAY[0], -- User 1 cannot see Entity 2 data
  'VAT data should be isolated by entity access'
);

-- Test 19: RLS prevents cross-entity access in RPC functions
SELECT results_eq(
  'SELECT COUNT(*)::int FROM rpc_vat_preview(2, ''2024-03-01'', ''2024-03-31'')',
  ARRAY[0], -- User 1 cannot access Entity 2 via RPC
  'VAT RPC functions should respect RLS'
);

-- Test 20: Switch to User 2 context
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0002"}';

-- User 2 can see their own entity but no VAT data (VAT disabled)
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_period WHERE entity_id = 2',
  ARRAY[0], -- Entity 2 has no VAT transactions with VAT codes
  'Entity without VAT codes should have no VAT period data'
);

-- Test 21: User 2 cannot access Entity 1 data
SELECT results_eq(
  'SELECT COUNT(*)::int FROM v_vat_period WHERE entity_id = 1',
  ARRAY[0], -- User 2 cannot see Entity 1 data
  'Users should only see VAT data from their own entities'
);

-- Test 22: Unmapped VAT codes show as UNMAPPED
-- Create a VAT code without grid mapping
INSERT INTO vat_codes (id, entity_id, code, name, rate, direction, label, is_purchase, is_sale, is_active)
VALUES (200, 2, 'CUSTOM', 'Custom VAT Rate', 0.1500, 'output', 'Custom 15%', false, true, true);

-- Create transaction with unmapped VAT code
INSERT INTO accounts (id, entity_id, code, name, account_type, type, normal_balance, is_active)
VALUES (20, 2, '4100', 'Other Revenue', 'revenue', 'income', 'credit', true);

INSERT INTO journals (id, entity_id, journal_type, description, transaction_date, created_by, is_balanced)
VALUES (10, 2, 'sales', 'Sale with unmapped VAT', '2024-03-20', '********-0000-0000-0000-********0002'::uuid, false);

INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
(20, 10, 6, 'Cash', 115.00, NULL, NULL),
(21, 10, 20, 'Revenue', NULL, 100.00, 200);

UPDATE journals SET is_balanced = true WHERE id = 10;

SELECT results_eq(
  'SELECT grid_code FROM v_vat_period WHERE entity_id = 2 AND vat_code_id = 200',
  ARRAY['UNMAPPED'], -- Should show as UNMAPPED
  'VAT codes without grid mapping should show as UNMAPPED'
);

-- Test 23: Period filtering works correctly
-- Switch back to User 1 for remaining tests
SET LOCAL "request.jwt.claims" TO '{"sub": "********-0000-0000-0000-********0001"}';

-- Test with date range that excludes our transactions
SELECT results_eq(
  'SELECT COUNT(*)::int FROM rpc_vat_preview(1, ''2024-02-01'', ''2024-02-28'')',
  ARRAY[0], -- February should have no transactions
  'VAT preview should respect date range filtering'
);

-- Test 24: Computed VAT when no VAT account entries exist
-- Create transaction with VAT code but no explicit VAT account entry
INSERT INTO journals (id, entity_id, journal_type, description, transaction_date, created_by, is_balanced)
VALUES (20, 1, 'sales', 'Sale with computed VAT', '2024-03-25', '********-0000-0000-0000-********0001'::uuid, false);

INSERT INTO journal_lines (id, journal_id, account_id, description, debit_amount, credit_amount, vat_code_id)
VALUES
(30, 20, 1, 'Cash', 1060.00, NULL, NULL),
(31, 20, 2, 'Revenue', NULL, 1000.00, 102); -- 6% VAT code but no VAT line

UPDATE journals SET is_balanced = true WHERE id = 20;

-- This should use computed VAT (1000 * 0.06 = 60)
SELECT results_eq(
  'SELECT vat_amount FROM v_vat_daily WHERE entity_id = 1 AND vat_code_id = 102 AND tx_date = ''2024-03-25''',
  ARRAY[60.00], -- Computed VAT amount
  'VAT calculation should compute VAT when no VAT account entry exists'
);

-- Test 25: Performance test - ensure views can handle reasonable data volume
-- The existing data should query in reasonable time
SELECT ok(
  (SELECT COUNT(*) FROM v_vat_period WHERE entity_id = 1) > 0,
  'VAT views should perform adequately with test data volume'
);

-- Clean up test data
DELETE FROM journal_lines WHERE journal_id IN (SELECT id FROM journals WHERE entity_id IN (1, 2));
DELETE FROM journals WHERE entity_id IN (1, 2);
DELETE FROM vat_grid_mapping WHERE vat_code_id >= 100;
DELETE FROM vat_codes WHERE entity_id IN (1, 2);
DELETE FROM accounts WHERE entity_id IN (1, 2);
DELETE FROM operating_modes WHERE entity_id IN (1, 2);
DELETE FROM entity_memberships WHERE entity_id IN (1, 2);
DELETE FROM entities WHERE id IN (1, 2);
DELETE FROM tenant_memberships WHERE tenant_id IN (1, 2);
DELETE FROM tenants WHERE id IN (1, 2);
DELETE FROM auth.users WHERE email LIKE '%<EMAIL>';

SELECT finish();

ROLLBACK;