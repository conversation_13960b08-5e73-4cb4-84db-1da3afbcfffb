-- Test suite for Track G - AI Suggestions (pgTAP)
-- Tests RLS policies, data integrity, and k-NN search functions

BEGIN;

SELECT plan(25);

-- Test 1-3: Basic table existence and structure
SELECT has_table('coding_events'::name, 'coding_events table exists');
SELECT has_table('invoice_embeddings'::name, 'invoice_embeddings table exists');
SELECT has_function('rpc_ai_knn_suggest', ARRAY['bigint', 'double precision[]', 'integer', 'double precision', 'double precision', 'text', 'double precision', 'integer'], 'k-NN suggest function exists');

-- Test 4-6: Index existence for performance
SELECT has_index('coding_events', 'idx_coding_events_entity_time', 'Entity time index exists on coding_events');
SELECT has_index('invoice_embeddings', 'idx_ie_entity_ann', 'ANN index exists on invoice_embeddings');
SELECT has_index('invoice_embeddings', 'unique_entity_text_hash', 'Unique constraint index exists for deduplication');

-- Test 7-10: RLS policies exist and are enabled
SELECT has_policy('coding_events', 'coding_events_entity_access', 'RLS policy exists for coding_events');
SELECT has_policy('invoice_embeddings', 'invoice_embeddings_entity_access', 'RLS policy exists for invoice_embeddings');
SELECT results_eq(
    'SELECT relname, relrowsecurity FROM pg_class WHERE relname IN (''coding_events'', ''invoice_embeddings'')',
    'VALUES (''coding_events'', true), (''invoice_embeddings'', true)',
    'RLS is enabled on both tables'
);

-- Setup test data
INSERT INTO entities (id, name, slug, created_at) VALUES 
    (9001, 'Test Entity AI 1', 'test-ai-1', NOW()),
    (9002, 'Test Entity AI 2', 'test-ai-2', NOW());

-- Setup users and memberships  
INSERT INTO auth.users (id, email, created_at) VALUES
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', NOW()),
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', NOW());

INSERT INTO entity_memberships (entity_id, user_id, role) VALUES
    (9001, '550e8400-e29b-41d4-a716-************', 'admin'),
    (9002, '550e8400-e29b-41d4-a716-************', 'admin');

-- Test 11-12: Basic data insertion works
INSERT INTO coding_events (entity_id, signature_text, account_id, vat_code_id) VALUES
    (9001, 'supplier: test corp; lines: office supplies x1 @100 21%', 6000, 1),
    (9001, 'supplier: acme inc; lines: software license x1 @500 21%', 6132, 1);

SELECT ok(
    (SELECT COUNT(*) FROM coding_events WHERE entity_id = 9001) = 2,
    'Can insert coding events'
);

-- Test embedding insertion with pgvector
INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding) VALUES
    (9001, (SELECT id FROM coding_events WHERE entity_id = 9001 LIMIT 1), 
     'abc123', '[1,0,0,0,0,0,0,0,0,0]'::vector(10)); -- Simplified 10-dim for test

SELECT ok(
    (SELECT COUNT(*) FROM invoice_embeddings WHERE entity_id = 9001) = 1,
    'Can insert embeddings with vector type'
);

-- Test 13-15: RLS security - cross-entity access prevention
-- Switch to user context
SET SESSION AUTHORIZATION 'authenticator';
SET request.jwt.claims = '{"sub": "550e8400-e29b-41d4-a716-************"}';

-- User 1 can see entity 9001 data
SELECT ok(
    (SELECT COUNT(*) FROM coding_events WHERE entity_id = 9001) > 0,
    'User can access their own entity coding events'
);

-- User 1 cannot see entity 9002 data
SELECT ok(
    (SELECT COUNT(*) FROM coding_events WHERE entity_id = 9002) = 0,
    'User cannot access other entity coding events (RLS)'
);

-- Same for embeddings
SELECT ok(
    (SELECT COUNT(*) FROM invoice_embeddings WHERE entity_id = 9002) = 0,
    'User cannot access other entity embeddings (RLS)'
);

-- Reset to superuser for remaining tests
RESET SESSION AUTHORIZATION;

-- Test 16-17: Unique constraint on (entity_id, text_hash)
INSERT INTO coding_events (entity_id, signature_text, account_id) VALUES
    (9001, 'duplicate test', 7000);

SELECT throws_ok(
    'INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding) VALUES 
     (9001, (SELECT id FROM coding_events WHERE signature_text = ''duplicate test''), 
      ''duplicate_hash'', ''[1,2,3,4,5,6,7,8,9,10]''::vector(10)),
     (9001, (SELECT id FROM coding_events WHERE signature_text = ''duplicate test''), 
      ''duplicate_hash'', ''[1,2,3,4,5,6,7,8,9,10]''::vector(10))',
    '23505',
    'Duplicate text_hash throws unique violation'
);

-- Test 18-19: k-NN RPC function basic functionality
-- Insert more test data for k-NN
INSERT INTO coding_events (entity_id, signature_text, account_id, vat_code_id) VALUES
    (9001, 'supplier: test corp; lines: office supplies x2 @200 21%', 6000, 1),
    (9001, 'supplier: test corp; lines: office furniture x1 @800 21%', 6000, 1),
    (9001, 'supplier: other corp; lines: consulting x1 @1000 21%', 6200, 1);

-- Insert corresponding embeddings (simplified vectors)
INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding) 
SELECT 9001, id, 'hash_' || id::text, ('[0.8,0.2,' || array_to_string(array_fill(0::text, ARRAY[8]), ',') || ']')::vector(10)
FROM coding_events WHERE entity_id = 9001 AND signature_text LIKE '%test corp%';

INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding)
SELECT 9001, id, 'hash_' || id::text, ('[0.1,0.9,' || array_to_string(array_fill(0::text, ARRAY[8]), ',') || ']')::vector(10)
FROM coding_events WHERE entity_id = 9001 AND signature_text LIKE '%other corp%';

-- Test k-NN function returns results
SELECT ok(
    (SELECT COUNT(*) FROM rpc_ai_knn_suggest(9001, ARRAY[0.8,0.2,0,0,0,0,0,0,0,0]::float8[], 5)) > 0,
    'k-NN RPC function returns results'
);

-- Test k-NN returns account 6000 (office supplies) as top result for similar query
SELECT ok(
    (SELECT account_id FROM rpc_ai_knn_suggest(9001, ARRAY[0.8,0.2,0,0,0,0,0,0,0,0]::float8[], 5) LIMIT 1) = 6000,
    'k-NN returns most similar account as top result'
);

-- Test 20-21: Entity isolation in k-NN queries
-- Insert data for entity 9002
INSERT INTO coding_events (entity_id, signature_text, account_id) VALUES
    (9002, 'supplier: entity2 corp; lines: different service x1 @300 21%', 8000);

INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding)
SELECT 9002, id, 'hash_entity2_' || id::text, '[0.9,0.1,0,0,0,0,0,0,0,0]'::vector(10)
FROM coding_events WHERE entity_id = 9002;

-- k-NN for entity 9001 should not return entity 9002 results
SELECT ok(
    (SELECT COUNT(*) FROM rpc_ai_knn_suggest(9001, ARRAY[0.9,0.1,0,0,0,0,0,0,0,0]::float8[], 10) 
     WHERE account_id = 8000) = 0,
    'k-NN results are entity-scoped - no cross-entity leakage'
);

-- k-NN for entity 9002 should return its own data
SELECT ok(
    (SELECT COUNT(*) FROM rpc_ai_knn_suggest(9002, ARRAY[0.9,0.1,0,0,0,0,0,0,0,0]::float8[], 10)) > 0,
    'k-NN works for different entities'
);

-- Test 22-23: Supplier VAT boost functionality
-- Add supplier-specific data
INSERT INTO coding_events (entity_id, signature_text, account_id, vat_code_id, supplier_vat) VALUES
    (9001, 'supplier: boosted corp; vat: BE123456789; lines: special service x1 @1000 21%', 6500, 1, 'BE123456789'),
    (9001, 'supplier: boosted corp; vat: BE123456789; lines: special service x2 @2000 21%', 6500, 1, 'BE123456789');

INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding)
SELECT 9001, id, 'hash_boosted_' || id::text, '[0.5,0.5,0,0,0,0,0,0,0,0]'::vector(10)
FROM coding_events WHERE entity_id = 9001 AND supplier_vat = 'BE123456789';

-- Test supplier boost increases score for matching VAT
-- Query with supplier VAT should boost matching records
SELECT ok(
    (SELECT score FROM rpc_ai_knn_suggest(
        9001, 
        ARRAY[0.5,0.5,0,0,0,0,0,0,0,0]::float8[], 
        5,
        0.15::double precision,  -- tau
        180::double precision,   -- half_life_days  
        'BE123456789'::text,     -- supplier_vat (boost)
        1.15::double precision   -- supplier_boost
    ) WHERE account_id = 6500 LIMIT 1) > 
    (SELECT score FROM rpc_ai_knn_suggest(
        9001,
        ARRAY[0.5,0.5,0,0,0,0,0,0,0,0]::float8[],
        5,
        0.15::double precision,
        180::double precision, 
        NULL::text,              -- no supplier boost
        1.15::double precision
    ) WHERE account_id = 6500 LIMIT 1),
    'Supplier VAT boost increases suggestion score'
);

-- Test 24: Helper RPC function for coding event details  
SELECT ok(
    (SELECT COUNT(*) FROM rpc_get_coding_event_details(
        9001, 
        ARRAY(SELECT id FROM coding_events WHERE entity_id = 9001 LIMIT 3)
    )) > 0,
    'Coding event details RPC function works'
);

-- Test 25: Recency weighting (recent events should score higher)
-- Insert recent and old events with same similarity
INSERT INTO coding_events (entity_id, signature_text, account_id, vat_code_id, confirmed_at) VALUES
    (9001, 'supplier: recent corp; lines: recent service x1 @100 21%', 6100, 1, NOW()),
    (9001, 'supplier: old corp; lines: old service x1 @100 21%', 6200, 1, NOW() - INTERVAL '365 days');

INSERT INTO invoice_embeddings (entity_id, coding_event_id, text_hash, embedding)
SELECT entity_id, id, 'hash_time_' || id::text, '[0.3,0.7,0,0,0,0,0,0,0,0]'::vector(10)
FROM coding_events WHERE entity_id = 9001 AND signature_text LIKE '%corp; lines:%service%';

-- Recent event should score higher than old event (with default half-life)
SELECT ok(
    (SELECT account_id FROM rpc_ai_knn_suggest(9001, ARRAY[0.3,0.7,0,0,0,0,0,0,0,0]::float8[], 5) 
     WHERE account_id IN (6100, 6200) LIMIT 1) = 6100,
    'Recent coding events score higher than old ones (recency weighting)'
);

-- Cleanup test data
DELETE FROM invoice_embeddings WHERE entity_id IN (9001, 9002);
DELETE FROM coding_events WHERE entity_id IN (9001, 9002);
DELETE FROM entity_memberships WHERE entity_id IN (9001, 9002);
DELETE FROM entities WHERE id IN (9001, 9002);

SELECT finish();

ROLLBACK;