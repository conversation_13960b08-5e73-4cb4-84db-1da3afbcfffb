BEGIN;

SELECT plan(12);

-- Test inbound_aliases table exists and has correct structure
SELECT has_table('public', 'inbound_aliases', 'inbound_aliases table should exist');
SELECT has_column('public', 'inbound_aliases', 'id', 'inbound_aliases should have id column');
SELECT has_column('public', 'inbound_aliases', 'entity_id', 'inbound_aliases should have entity_id column');
SELECT has_column('public', 'inbound_aliases', 'localpart', 'inbound_aliases should have localpart column');
SELECT has_column('public', 'inbound_aliases', 'enabled', 'inbound_aliases should have enabled column');

-- Test inbound_messages table exists and has correct structure  
SELECT has_table('public', 'inbound_messages', 'inbound_messages table should exist');
SELECT has_column('public', 'inbound_messages', 'provider', 'inbound_messages should have provider column');
SELECT has_column('public', 'inbound_messages', 'message_id', 'inbound_messages should have message_id column');
SELECT has_column('public', 'inbound_messages', 'status', 'inbound_messages should have status column');

-- Test inbound_sender_allowlist table exists
SELECT has_table('public', 'inbound_sender_allowlist', 'inbound_sender_allowlist table should exist');

-- Test RLS is enabled on all email-in tables
SELECT has_row_security('public', 'inbound_aliases', 'inbound_aliases should have row level security enabled');
SELECT has_row_security('public', 'inbound_messages', 'inbound_messages should have row level security enabled');

-- Clean up any test data
DELETE FROM inbound_sender_allowlist WHERE entity_id IN (SELECT id FROM entities WHERE name LIKE 'Test Entity%');
DELETE FROM inbound_messages WHERE entity_id IN (SELECT id FROM entities WHERE name LIKE 'Test Entity%');
DELETE FROM inbound_aliases WHERE entity_id IN (SELECT id FROM entities WHERE name LIKE 'Test Entity%');
DELETE FROM entity_memberships WHERE entity_id IN (SELECT id FROM entities WHERE name LIKE 'Test Entity%');
DELETE FROM entities WHERE name LIKE 'Test Entity%';
DELETE FROM tenant_memberships WHERE tenant_id IN (SELECT id FROM tenants WHERE name LIKE 'Test Tenant%');
DELETE FROM tenants WHERE name LIKE 'Test Tenant%';

ROLLBACK;