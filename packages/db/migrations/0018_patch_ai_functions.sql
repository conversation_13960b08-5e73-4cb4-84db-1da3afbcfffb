-- Migration 0018: Patch AI functions for PostgreSQL 17 compatibility
-- Applies changes that were made to migration 0012 but need to be applied to existing databases

-- =============================================================================
-- Replace rpc_ai_knn_suggest_core function with PostgreSQL 17 compatible version
-- =============================================================================

-- Drop the old function with 8-parameter signature from migration 0014
DROP FUNCTION IF EXISTS rpc_ai_knn_suggest_core(BIGINT, VECTOR(1536), INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT);

-- Drop and recreate the function with PostgreSQL 17 compatible array handling
CREATE OR REPLACE FUNCTION rpc_ai_knn_suggest_core(
  p_entity           BIGINT,
  p_query            VECTOR(1536),
  p_supplier_vat     TEXT DEFAULT NULL,
  p_half_life_days   NUMERIC DEFAULT 180,
  p_supplier_boost   NUMERIC DEFAULT 1.5,
  p_limit            INT DEFAULT 5
)
RETURNS TABLE(
  account_id      BIGINT,
  vat_code_id     BIGINT,
  top_examples    BIGINT[],
  votes           INT,
  avg_sim         NUMERIC,
  sum_w           NUMERIC,
  prob            NUMERIC
)
LANGUAGE SQL STABLE SECURITY INVOKER AS $$

WITH nn AS (
  SELECT 
    ie.coding_event_id,
    ce.account_id,
    ce.vat_code_id,
    ce.supplier_vat,
    1 - (p_query <=> ie.embedding) AS sim,
    EXTRACT(EPOCH FROM (NOW() - ie.created_at)) / (24 * 3600) AS age_days
  FROM invoice_embeddings ie
  JOIN coding_events ce ON ie.coding_event_id = ce.id
  WHERE ie.entity_id = p_entity
  ORDER BY p_query <=> ie.embedding
  LIMIT 50
),
w AS (
  SELECT
    coding_event_id,
    account_id,
    vat_code_id,
    supplier_vat,
    sim,
    age_days,
    sim
    * EXP( LN(0.5) * (age_days / NULLIF(p_half_life_days, 1e-9)) )
    * CASE WHEN p_supplier_vat IS NOT NULL AND supplier_vat = p_supplier_vat
           THEN COALESCE(p_supplier_boost, 1.0) ELSE 1.0 END
      AS w
  FROM nn
),
ranked AS (
  SELECT 
    account_id, vat_code_id, coding_event_id, sim, w,
    ROW_NUMBER() OVER (PARTITION BY account_id, vat_code_id ORDER BY sim DESC) AS rn
  FROM w
),
g AS (
  SELECT
    account_id,
    vat_code_id,
    ARRAY_AGG(coding_event_id) FILTER (WHERE rn <= 3) AS top_examples,
    COUNT(*)::INT                                      AS votes,
    AVG(sim)                                           AS avg_sim,
    SUM(w)                                             AS sum_w
  FROM ranked
  GROUP BY account_id, vat_code_id
),
z AS ( SELECT GREATEST(SUM(sum_w), 1e-12) AS z FROM g )
SELECT
  account_id,
  vat_code_id,
  COALESCE(top_examples, ARRAY[]::BIGINT[]) AS top_examples,
  votes,
  ROUND(avg_sim::NUMERIC, 4) AS avg_sim,
  ROUND(sum_w::NUMERIC, 6) AS sum_w,
  ROUND((sum_w / z.z)::NUMERIC, 6) AS prob
FROM g CROSS JOIN z
WHERE sum_w > 1e-9
ORDER BY prob DESC
LIMIT p_limit;

$$;

-- =============================================================================
-- Comments and documentation
-- =============================================================================

COMMENT ON FUNCTION rpc_ai_knn_suggest_core(BIGINT, VECTOR(1536), TEXT, NUMERIC, NUMERIC, INT) IS 'PostgreSQL 17 compatible AI k-NN suggestion function using proper window functions instead of array slicing';

-- Verification
DO $$
DECLARE
    function_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public' AND p.proname = 'rpc_ai_knn_suggest_core'
    ) INTO function_exists;
    
    IF function_exists THEN
        RAISE NOTICE 'Successfully verified rpc_ai_knn_suggest_core function exists with PostgreSQL 17 compatible implementation';
    ELSE
        RAISE WARNING 'rpc_ai_knn_suggest_core function is missing';
    END IF;
END $$;