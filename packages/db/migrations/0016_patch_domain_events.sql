-- Migration 0016: Patch domain_events table for PostgreSQL 17 compatibility
-- Applies changes that were made to migration 0009 but need to be applied to existing databases

-- =============================================================================
-- Add missing columns to domain_events table (from updated migration 0009)
-- =============================================================================

-- Add status column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='status') THEN
    ALTER TABLE domain_events ADD COLUMN status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed'));
    RAISE NOTICE 'Added status column to domain_events table';
  END IF;
END $$;

-- Add error_msg column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='error_msg') THEN
    ALTER TABLE domain_events ADD COLUMN error_msg TEXT;
    RAISE NOTICE 'Added error_msg column to domain_events table';
  END IF;
END $$;

-- Add updated_at column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='updated_at') THEN
    ALTER TABLE domain_events ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL;
    RAISE NOTICE 'Added updated_at column to domain_events table';
  END IF;
END $$;

-- Modify event_data to have a default if it doesn't already
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='event_data' AND column_default IS NULL) THEN
    ALTER TABLE domain_events ALTER COLUMN event_data SET DEFAULT '{}';
    RAISE NOTICE 'Set default value for event_data column';
  END IF;
END $$;

-- =============================================================================
-- Rename conflicting function (from updated migration 0009)
-- =============================================================================

-- Note: pg_try_advisory_xact_lock is a built-in PostgreSQL function, so we don't drop it
-- We only need to ensure our wrapper function exists

-- Create the properly named function
CREATE OR REPLACE FUNCTION rpc_try_advisory_xact_lock(key1 INTEGER, key2 INTEGER)
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT pg_try_advisory_xact_lock(key1, key2);
$$;

-- =============================================================================
-- Add updated_at trigger for domain_events if not exists
-- =============================================================================

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'domain_events_updated_at'
  ) THEN
    CREATE TRIGGER domain_events_updated_at
    BEFORE UPDATE ON domain_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
    RAISE NOTICE 'Created updated_at trigger for domain_events table';
  END IF;
END $$;

-- =============================================================================
-- Add index for processing domain events efficiently (from updated migration 0009)
-- =============================================================================

CREATE INDEX IF NOT EXISTS idx_domain_events_processing
ON domain_events(entity_id, status, created_at) WHERE status = 'pending';

-- =============================================================================
-- Comments and documentation
-- =============================================================================

COMMENT ON COLUMN domain_events.status IS 'Processing status: pending, processing, completed, failed';
COMMENT ON COLUMN domain_events.error_msg IS 'Error message if processing failed';
COMMENT ON COLUMN domain_events.updated_at IS 'Timestamp when record was last updated';
COMMENT ON FUNCTION rpc_try_advisory_xact_lock IS 'Wrapper for PostgreSQL advisory lock function - used for concurrency control';

-- Verification
DO $$
DECLARE
    column_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO column_count 
    FROM information_schema.columns 
    WHERE table_name = 'domain_events' 
    AND column_name IN ('status', 'error_msg', 'updated_at');
    
    IF column_count = 3 THEN
        RAISE NOTICE 'Successfully verified domain_events table has all required columns';
    ELSE
        RAISE WARNING 'domain_events table is missing some columns. Found % of 3 expected columns', column_count;
    END IF;
END $$;