-- All migrations in order
\i packages/db/migrations/0001_baseline.sql
\i packages/db/migrations/0002_rpcs.sql
\i packages/db/migrations/0003_tenant_fixes.sql
\i packages/db/migrations/0004_tenant_creation_rpc.sql
\i packages/db/migrations/0005_security_events.sql
\i packages/db/migrations/0006_inbox.sql
\i packages/db/migrations/0007_bank_track_d.sql
\i packages/db/migrations/0008_enhanced_import_rpc.sql
\i packages/db/migrations/0009_inbox_confirm_audit.sql
\i packages/db/migrations/0010_vat_core.sql
\i packages/db/migrations/0011_integrations.sql
\i packages/db/migrations/0012_ai_suggest.sql
\i packages/db/migrations/0013_email_in.sql
\i packages/db/migrations/0014_syntax_fixes.sql
\i packages/db/migrations/0015_legacy_syntax_fixes.sql
\i packages/db/migrations/0016_patch_domain_events.sql
\i packages/db/migrations/0017_patch_export_jobs.sql
\i packages/db/migrations/0018_patch_ai_functions.sql
\i packages/db/migrations/0019_safe_drop_advisory_lock.sql
\i packages/db/migrations/0020_context_aware_org_entity_ux.sql
\i packages/db/migrations/0021_storage_buckets.sql
