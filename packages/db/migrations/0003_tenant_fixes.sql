-- Migration 0003: Fix Tenant/Entity Two-Layer Model
-- Addresses missing tenant layer components for proper isolation

-- Enable citext extension for case-insensitive email handling
CREATE EXTENSION IF NOT EXISTS citext;

-- Add tenant kind column to distinguish SME vs FIRM customers
ALTER TABLE tenants ADD COLUMN kind TEXT CHECK (
  kind IN ('SME', 'FIRM')
) DEFAULT 'SME';

-- Update tenant membership roles to match specification
-- First, update existing data
UPDATE tenant_memberships SET role = 'tenant_owner'
WHERE role = 'owner';
UPDATE tenant_memberships SET role = 'tenant_admin'
WHERE role = 'admin';
UPDATE tenant_memberships SET role = 'tenant_member'
WHERE role = 'member';

-- Drop old constraint and add new one
ALTER TABLE tenant_memberships DROP CONSTRAINT tenant_memberships_role_check;
ALTER TABLE tenant_memberships ADD CONSTRAINT tenant_memberships_role_check
CHECK (
  role IN ('tenant_owner', 'tenant_admin', 'tenant_billing', 'tenant_member')
);

-- Create pending invites table for email-based invitations
CREATE TABLE pending_invites (
  token UUID PRIMARY KEY DEFAULT gen_random_uuid()
  , scope TEXT NOT NULL CHECK (scope IN ('tenant', 'entity'))
  , scope_id TEXT NOT NULL -- UUID for tenant, BIGINT as text for entity  
  , email CITEXT NOT NULL
  , role TEXT NOT NULL
  , inviter_user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE
  , expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + INTERVAL '7 days')
  , created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Enable RLS on pending invites
ALTER TABLE pending_invites ENABLE ROW LEVEL SECURITY;

-- RLS policy: Only the inviter can see their pending invites
CREATE POLICY pending_invites_access ON pending_invites FOR ALL TO authenticated
USING (inviter_user_id = auth.uid());

-- Create convenience view for user's tenants
CREATE VIEW v_user_tenants AS
SELECT
  tm.user_id
  , tm.tenant_id
  , tm.role
  , t.name AS tenant_name
  , t.kind AS tenant_kind
FROM tenant_memberships AS tm
INNER JOIN tenants AS t ON tm.tenant_id = t.id
WHERE tm.user_id = auth.uid();

-- Update tenant membership RLS policy to use new role names
DROP POLICY tenant_membership_access ON tenant_memberships;
CREATE POLICY tenant_membership_access ON tenant_memberships FOR ALL TO authenticated
USING (user_id = auth.uid() OR tenant_id IN (
  SELECT tenant_id FROM tenant_memberships
  WHERE user_id = auth.uid() AND role IN ('tenant_owner', 'tenant_admin')
));

-- Update entity access policy to properly implement tenant admin vs entity member separation
DROP POLICY entity_access ON entities;
CREATE POLICY entity_access ON entities FOR SELECT TO authenticated
USING (
  -- Entity member: can see entity
  id IN (
    SELECT entity_id FROM entity_memberships
    WHERE user_id = auth.uid()
  )
  OR
  -- Tenant admin: can LIST entities for admin purposes but NOT transactional data  
  tenant_id IN (
    SELECT tenant_id FROM tenant_memberships
    WHERE
      user_id = auth.uid()
      AND role IN ('tenant_owner', 'tenant_admin')
  )
);

-- Entity creation/modification: only tenant admins
CREATE POLICY entity_create ON entities FOR INSERT TO authenticated
WITH CHECK (
  tenant_id IN (
    SELECT tenant_id FROM tenant_memberships
    WHERE
      user_id = auth.uid()
      AND role IN ('tenant_owner', 'tenant_admin')
  )
);

CREATE POLICY entity_modify ON entities FOR UPDATE TO authenticated
USING (
  tenant_id IN (
    SELECT tenant_id FROM tenant_memberships
    WHERE
      user_id = auth.uid()
      AND role IN ('tenant_owner', 'tenant_admin')
  )
);

CREATE POLICY entity_delete ON entities FOR DELETE TO authenticated
USING (
  tenant_id IN (
    SELECT tenant_id FROM tenant_memberships
    WHERE
      user_id = auth.uid()
      AND role IN ('tenant_owner', 'tenant_admin')
  )
);

-- Indexes for performance  
CREATE INDEX idx_pending_invites_email ON pending_invites (email);
CREATE INDEX idx_pending_invites_scope ON pending_invites (scope, scope_id);
CREATE INDEX idx_pending_invites_expires ON pending_invites (expires_at);
CREATE INDEX idx_tenants_kind ON tenants (kind);

-- RPC: Grant tenant role
CREATE OR REPLACE FUNCTION rpc_grant_tenant_role(
  p_tenant_id BIGINT
  , p_target_user UUID
  , p_role TEXT
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate role
  IF p_role NOT IN ('tenant_owner', 'tenant_admin', 'tenant_billing', 'tenant_member') THEN
    RAISE EXCEPTION 'Invalid tenant role: %', p_role;
  END IF;

  -- Check caller has permission (must be tenant_owner or tenant_admin)
  IF NOT EXISTS (
    SELECT 1 FROM tenant_memberships tm
    WHERE tm.tenant_id = p_tenant_id 
    AND tm.user_id = auth.uid()
    AND tm.role IN ('tenant_owner', 'tenant_admin')
  ) THEN
    RAISE EXCEPTION 'Access denied: insufficient tenant permissions';
  END IF;

  -- Validate target user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_target_user) THEN
    RAISE EXCEPTION 'Target user does not exist';
  END IF;

  -- Insert or update membership
  INSERT INTO tenant_memberships (tenant_id, user_id, role)
  VALUES (p_tenant_id, p_target_user, p_role)
  ON CONFLICT (tenant_id, user_id) 
  DO UPDATE SET 
    role = EXCLUDED.role,
    created_at = NOW();
END;
$$;

-- RPC: Create entity (tenant admins only)
CREATE OR REPLACE FUNCTION rpc_create_entity(
  p_tenant_id BIGINT
  , p_name TEXT
  , p_currency CHAR(3) DEFAULT 'EUR'
) RETURNS BIGINT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_entity_id BIGINT;
BEGIN
  -- Check caller has permission (must be tenant_owner or tenant_admin)
  IF NOT EXISTS (
    SELECT 1 FROM tenant_memberships tm
    WHERE tm.tenant_id = p_tenant_id 
    AND tm.user_id = auth.uid()
    AND tm.role IN ('tenant_owner', 'tenant_admin')
  ) THEN
    RAISE EXCEPTION 'Access denied: insufficient tenant permissions';
  END IF;

  -- Create entity
  INSERT INTO entities (tenant_id, name, currency)
  VALUES (p_tenant_id, p_name, p_currency)
  RETURNING id INTO v_entity_id;

  -- Auto-grant caller as entity owner
  INSERT INTO entity_memberships (entity_id, user_id, role)
  VALUES (v_entity_id, auth.uid(), 'owner');

  -- Create default operating mode
  INSERT INTO operating_modes (entity_id, mode, config)
  VALUES (v_entity_id, 'ledger', '{}');

  RETURN v_entity_id;
END;
$$;

-- RPC: Create invitation
CREATE OR REPLACE FUNCTION rpc_invite(
  p_scope TEXT
  , p_scope_id TEXT
  , p_email CITEXT
  , p_role TEXT
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_token UUID;
  v_scope_id_bigint BIGINT;
BEGIN
  -- Validate scope
  IF p_scope NOT IN ('tenant', 'entity') THEN
    RAISE EXCEPTION 'Invalid scope: %', p_scope;
  END IF;

  -- Check permissions based on scope
  IF p_scope = 'tenant' THEN
    -- Tenant invite: check tenant admin permissions
    IF p_role NOT IN ('tenant_owner', 'tenant_admin', 'tenant_billing', 'tenant_member') THEN
      RAISE EXCEPTION 'Invalid tenant role: %', p_role;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1 FROM tenant_memberships tm
      WHERE tm.tenant_id = p_scope_id::BIGINT
      AND tm.user_id = auth.uid()
      AND tm.role IN ('tenant_owner', 'tenant_admin')
    ) THEN
      RAISE EXCEPTION 'Access denied: insufficient tenant permissions';
    END IF;
    
  ELSIF p_scope = 'entity' THEN
    -- Entity invite: check entity admin OR tenant admin permissions
    v_scope_id_bigint := p_scope_id::BIGINT;
    
    IF p_role NOT IN ('owner', 'admin', 'accountant', 'bookkeeper', 'viewer') THEN
      RAISE EXCEPTION 'Invalid entity role: %', p_role;  
    END IF;
    
    IF NOT EXISTS (
      -- Entity admin check
      SELECT 1 FROM entity_memberships em
      WHERE em.entity_id = v_scope_id_bigint
      AND em.user_id = auth.uid()
      AND em.role IN ('owner', 'admin')
    ) AND NOT EXISTS (
      -- Tenant admin check  
      SELECT 1 FROM entity_memberships em
      JOIN entities e ON e.id = em.entity_id
      JOIN tenant_memberships tm ON tm.tenant_id = e.tenant_id
      WHERE em.entity_id = v_scope_id_bigint
      AND tm.user_id = auth.uid()
      AND tm.role IN ('tenant_owner', 'tenant_admin')
    ) THEN
      RAISE EXCEPTION 'Access denied: insufficient entity permissions';
    END IF;
  END IF;

  -- If user already exists, grant role directly
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = p_email) THEN
    DECLARE
      v_target_user UUID;
    BEGIN
      SELECT id INTO v_target_user FROM auth.users WHERE email = p_email;
      
      IF p_scope = 'tenant' THEN
        PERFORM rpc_grant_tenant_role(p_scope_id::BIGINT, v_target_user, p_role);
      ELSE
        PERFORM rpc_grant_entity_role(p_scope_id::BIGINT, v_target_user, p_role);
      END IF;
      
      RETURN NULL; -- No invite token needed
    END;
  END IF;

  -- Create pending invite
  INSERT INTO pending_invites (scope, scope_id, email, role, inviter_user_id)
  VALUES (p_scope, p_scope_id, p_email, p_role, auth.uid())
  RETURNING token INTO v_token;

  RETURN v_token;
END;
$$;

-- RPC: Accept invitation  
CREATE OR REPLACE FUNCTION rpc_accept_invite(p_token UUID) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invite RECORD;
  v_user_email CITEXT;
BEGIN
  -- Get current user's email
  SELECT email INTO v_user_email FROM auth.users WHERE id = auth.uid();
  
  -- Get invite details
  SELECT * INTO v_invite 
  FROM pending_invites 
  WHERE token = p_token 
  AND expires_at > NOW();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation token';
  END IF;
  
  -- Check email matches
  IF v_invite.email != v_user_email THEN
    RAISE EXCEPTION 'Invitation email does not match your account';
  END IF;
  
  -- Grant appropriate role
  IF v_invite.scope = 'tenant' THEN
    PERFORM rpc_grant_tenant_role(v_invite.scope_id::BIGINT, auth.uid(), v_invite.role);
  ELSIF v_invite.scope = 'entity' THEN  
    PERFORM rpc_grant_entity_role(v_invite.scope_id::BIGINT, auth.uid(), v_invite.role);
  END IF;
  
  -- Delete the invite
  DELETE FROM pending_invites WHERE token = p_token;
END;
$$;

-- pgTAP tests for tenant/entity isolation
COMMENT ON FUNCTION rpc_grant_tenant_role IS 'Grant tenant-level role (requires tenant admin)';
COMMENT ON FUNCTION rpc_create_entity IS 'Create new entity under tenant (requires tenant admin)';
COMMENT ON FUNCTION rpc_invite IS 'Create invitation for tenant or entity access';
COMMENT ON FUNCTION rpc_accept_invite IS 'Accept pending invitation and grant access';
