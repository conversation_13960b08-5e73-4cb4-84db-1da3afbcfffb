-- Track E: VAT Core Migration
-- Creates VAT calculation views, grid mappings, and export functionality

-- 1. Schema Enhancements
-- =====================

-- Extend vat_codes with direction and label
ALTER TABLE vat_codes 
  ADD COLUMN IF NOT EXISTS direction TEXT CHECK (direction IN ('output', 'input')),
  ADD COLUMN IF NOT EXISTS label TEXT;

-- Update accounts table to include type for distinguishing account categories
ALTER TABLE accounts 
  ADD COLUMN IF NOT EXISTS type TEXT CHECK (type IN ('asset', 'liability', 'equity', 'income', 'expense', 'vat'));

-- Update existing accounts to use 'type' instead of 'account_type' (maintain backward compatibility)
UPDATE accounts SET type = account_type WHERE type IS NULL;

-- Create VAT grids table for Belgian grid mapping
CREATE TABLE IF NOT EXISTS vat_grids (
  id SERIAL PRIMARY KEY,
  grid_code TEXT NOT NULL UNIQUE,
  label TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS on vat_grids (system-wide configuration)
ALTER TABLE vat_grids ENABLE ROW LEVEL SECURITY;

-- Create VAT grid mapping table
CREATE TABLE IF NOT EXISTS vat_grid_mapping (
  vat_code_id BIGINT REFERENCES vat_codes(id) ON DELETE CASCADE,
  grid_id INTEGER REFERENCES vat_grids(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  PRIMARY KEY (vat_code_id, grid_id)
);

-- Enable RLS on vat_grid_mapping
ALTER TABLE vat_grid_mapping ENABLE ROW LEVEL SECURITY;

-- 2. VAT Calculation Views
-- ========================

-- Helper: signed amount (+debit, -credit) for easier calculations
CREATE OR REPLACE VIEW v_journal_lines_signed AS
SELECT jl.*,
       j.entity_id,
       j.transaction_date,
       (COALESCE(jl.debit_amount, 0) - COALESCE(jl.credit_amount, 0)) AS signed_amount
FROM journal_lines jl
JOIN journals j ON j.id = jl.journal_id;

-- Base amounts: income/expense lines carrying a vat_code_id
CREATE OR REPLACE VIEW v_vat_bases AS
SELECT j.entity_id,
       DATE_TRUNC('day', j.transaction_date)::DATE AS tx_date,
       jl.vat_code_id,
       ABS(SUM(signed_amount)) AS base_amount
FROM v_journal_lines_signed jl
JOIN journals j ON j.id = jl.journal_id
JOIN accounts a ON a.id = jl.account_id
WHERE jl.vat_code_id IS NOT NULL
  AND a.type IN ('income', 'expense')
GROUP BY 1, 2, 3;

-- VAT amounts from VAT accounts if present
CREATE OR REPLACE VIEW v_vat_lines AS
SELECT j.entity_id,
       DATE_TRUNC('day', j.transaction_date)::DATE AS tx_date,
       jl.vat_code_id,
       ABS(SUM(signed_amount)) AS vat_amount_booked
FROM v_journal_lines_signed jl
JOIN journals j ON j.id = jl.journal_id
JOIN accounts a ON a.id = jl.account_id
WHERE jl.vat_code_id IS NOT NULL
  AND a.type = 'vat'
GROUP BY 1, 2, 3;

-- Daily rollup using booked VAT if present, otherwise computed
CREATE OR REPLACE VIEW v_vat_daily AS
SELECT b.entity_id, 
       b.tx_date, 
       b.vat_code_id,
       b.base_amount,
       COALESCE(v.vat_amount_booked, ROUND(b.base_amount * (vc.rate), 2)) AS vat_amount,
       vc.direction
FROM v_vat_bases b
JOIN vat_codes vc ON vc.id = b.vat_code_id
LEFT JOIN v_vat_lines v ON v.entity_id = b.entity_id 
  AND v.tx_date = b.tx_date 
  AND v.vat_code_id = b.vat_code_id;

-- Period aggregation with grid mapping
CREATE OR REPLACE VIEW v_vat_period AS
SELECT d.entity_id,
       DATE_TRUNC('month', d.tx_date)::DATE AS month_start,
       d.vat_code_id,
       SUM(d.base_amount) AS base_total,
       SUM(d.vat_amount) AS vat_total,
       vc.direction,
       COALESCE(vg.grid_code, 'UNMAPPED') AS grid_code
FROM v_vat_daily d
JOIN vat_codes vc ON vc.id = d.vat_code_id
LEFT JOIN vat_grid_mapping vgm ON vgm.vat_code_id = d.vat_code_id
LEFT JOIN vat_grids vg ON vg.id = vgm.grid_id
GROUP BY 1, 2, 3, 6, 7;

-- 3. RPC Functions
-- ================

-- VAT preview function for a given entity and period
CREATE OR REPLACE FUNCTION rpc_vat_preview(p_entity BIGINT, p_start DATE, p_end DATE)
RETURNS TABLE(
  grid_code TEXT, 
  direction TEXT, 
  base_total NUMERIC, 
  vat_total NUMERIC
)
LANGUAGE SQL SECURITY INVOKER AS $$
  SELECT 
    COALESCE(vp.grid_code, 'UNMAPPED') AS grid_code,
    vp.direction,
    SUM(vp.base_total) AS base_total,
    SUM(vp.vat_total) AS vat_total
  FROM v_vat_period vp
  WHERE vp.entity_id = p_entity
    AND vp.month_start >= DATE_TRUNC('month', p_start)
    AND vp.month_start < DATE_TRUNC('month', p_end) + INTERVAL '1 month'
  GROUP BY 1, 2
  ORDER BY 1, 2;
$$;

-- CSV export function
CREATE OR REPLACE FUNCTION rpc_vat_export_csv(p_entity BIGINT, p_start DATE, p_end DATE)
RETURNS TEXT
LANGUAGE plpgsql SECURITY INVOKER AS $$
DECLARE
  rec RECORD;
  out_csv TEXT := 'grid_code,direction,base_total,vat_total' || E'\n';
BEGIN
  FOR rec IN SELECT * FROM rpc_vat_preview(p_entity, p_start, p_end) LOOP
    out_csv := out_csv || rec.grid_code || ',' || rec.direction || ',' ||
               TO_CHAR(rec.base_total, 'FM999999999990.00') || ',' ||
               TO_CHAR(rec.vat_total, 'FM999999999990.00') || E'\n';
  END LOOP;
  RETURN out_csv;
END$$;

-- 4. Performance Indexes
-- ======================

-- Index for VAT queries on journal lines
CREATE INDEX IF NOT EXISTS idx_jl_entity_date_vat 
ON journal_lines(vat_code_id, journal_id);

-- Index for journal entity and date filtering
CREATE INDEX IF NOT EXISTS idx_journals_entity_transaction_date 
ON journals(entity_id, transaction_date);

-- Index for account type filtering
CREATE INDEX IF NOT EXISTS idx_accounts_entity_type 
ON accounts(entity_id, type);

-- 5. Seed Data
-- ============

-- Seed Belgian VAT grids
INSERT INTO vat_grids (grid_code, label) VALUES
  ('BE_OUT_21', 'Belgian Output VAT 21%'),
  ('BE_OUT_12', 'Belgian Output VAT 12%'),
  ('BE_OUT_6', 'Belgian Output VAT 6%'),
  ('BE_OUT_0', 'Belgian Output VAT 0%'),
  ('BE_IN_21', 'Belgian Input VAT 21%'),
  ('BE_IN_12', 'Belgian Input VAT 12%'),
  ('BE_IN_6', 'Belgian Input VAT 6%'),
  ('BE_IN_0', 'Belgian Input VAT 0%')
ON CONFLICT (grid_code) DO NOTHING;

-- Update existing VAT codes with direction and label
UPDATE vat_codes SET 
  direction = CASE 
    WHEN code IN ('BE21', 'BE12', 'BE06', 'BE00', 'BEEX') AND (is_sale = TRUE OR entity_id IS NULL) THEN 'output'
    WHEN code IN ('BE21', 'BE12', 'BE06', 'BE00', 'BEIC') AND (is_purchase = TRUE OR entity_id IS NULL) THEN 'input'
    ELSE direction
  END,
  label = CASE 
    WHEN code = 'BE21' THEN 'Belgian VAT 21%'
    WHEN code = 'BE12' THEN 'Belgian VAT 12%'
    WHEN code = 'BE06' THEN 'Belgian VAT 6%'
    WHEN code = 'BE00' THEN 'Belgian VAT 0% (exempt)'
    WHEN code = 'BEIC' THEN 'Belgian Intra-Community'
    WHEN code = 'BEEX' THEN 'Belgian Export'
    ELSE label
  END
WHERE direction IS NULL OR label IS NULL;

-- Map system VAT codes to grids
INSERT INTO vat_grid_mapping (vat_code_id, grid_id)
SELECT 
  vc.id,
  vg.id
FROM vat_codes vc
CROSS JOIN vat_grids vg
WHERE vc.entity_id IS NULL -- Only map system defaults
  AND (
    (vc.code = 'BE21' AND vc.direction = 'output' AND vg.grid_code = 'BE_OUT_21') OR
    (vc.code = 'BE21' AND vc.direction = 'input' AND vg.grid_code = 'BE_IN_21') OR
    (vc.code = 'BE12' AND vc.direction = 'output' AND vg.grid_code = 'BE_OUT_12') OR
    (vc.code = 'BE12' AND vc.direction = 'input' AND vg.grid_code = 'BE_IN_12') OR
    (vc.code = 'BE06' AND vc.direction = 'output' AND vg.grid_code = 'BE_OUT_6') OR
    (vc.code = 'BE06' AND vc.direction = 'input' AND vg.grid_code = 'BE_IN_6') OR
    (vc.code = 'BE00' AND vc.direction = 'output' AND vg.grid_code = 'BE_OUT_0') OR
    (vc.code = 'BE00' AND vc.direction = 'input' AND vg.grid_code = 'BE_IN_0') OR
    (vc.code = 'BEEX' AND vc.direction = 'output' AND vg.grid_code = 'BE_OUT_0') OR
    (vc.code = 'BEIC' AND vc.direction = 'input' AND vg.grid_code = 'BE_IN_0')
  )
ON CONFLICT (vat_code_id, grid_id) DO NOTHING;

-- 6. RLS Policies
-- ===============

-- VAT grids are system-wide readable
CREATE POLICY vat_grid_read ON vat_grids FOR SELECT TO authenticated
USING (TRUE);

-- VAT grid mappings follow VAT code access
CREATE POLICY vat_grid_mapping_access ON vat_grid_mapping FOR SELECT TO authenticated
USING (
  vat_code_id IN (
    SELECT id FROM vat_codes 
    WHERE entity_id IS NULL OR entity_id IN (SELECT entity_id FROM v_user_entities)
  )
);

-- Entity feature flags (for future use)
-- Add VATEnabled flag to entity config when needed
-- This can be added to operating_modes.config JSONB as {"VATEnabled": true}

-- 7. View RLS (inherited from underlying tables)
-- The views will automatically respect RLS since they're based on tables with RLS policies