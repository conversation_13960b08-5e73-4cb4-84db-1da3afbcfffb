-- Track F: Integrations & Assist Mode Migration  
-- Creates export jobs, connector configs, and extends domain events for delivery tracking

-- 1. Extend Domain Events Table
-- ==============================

-- Add dedupe_key for idempotency support
ALTER TABLE domain_events 
  ADD COLUMN IF NOT EXISTS dedupe_key TEXT,
  ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Index for efficient dedupe key lookups
CREATE INDEX IF NOT EXISTS idx_domain_events_dedupe_key 
ON domain_events(entity_id, dedupe_key) WHERE dedupe_key IS NOT NULL;

-- Index for event type queries
CREATE INDEX IF NOT EXISTS idx_domain_events_type 
ON domain_events(entity_id, event_type, status);

-- 2. Export Jobs Table (execution tracking)
-- =========================================

CREATE TABLE IF NOT EXISTS export_jobs (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  event_id BIGINT NOT NULL REFERENCES domain_events(id) ON DELETE CASCADE,
  connector TEXT NOT NULL CHECK (connector IN ('winbooks_sftp', 'email')),
  status TEXT NOT NULL CHECK (status IN ('queued', 'delivering', 'delivered', 'failed', 'skipped')) DEFAULT 'queued',
  content_hash TEXT NOT NULL,
  dedupe_key TEXT,
  attempts INT NOT NULL DEFAULT 0,
  last_error TEXT,
  delivered_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  -- Ensure idempotency: one job per (entity, connector, content_hash)
  CONSTRAINT unique_entity_connector_content UNIQUE (entity_id, connector, content_hash)
);

-- Enable RLS on export_jobs
ALTER TABLE export_jobs ENABLE ROW LEVEL SECURITY;

-- Indexes for export jobs
CREATE INDEX IF NOT EXISTS idx_export_jobs_entity_status 
ON export_jobs(entity_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_export_jobs_event_id 
ON export_jobs(event_id);

CREATE INDEX IF NOT EXISTS idx_export_jobs_content_hash 
ON export_jobs(content_hash);

-- 3. Connector Configurations Table
-- ==================================

CREATE TABLE IF NOT EXISTS connector_configs (
  entity_id BIGINT PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
  connector TEXT NOT NULL CHECK (connector IN ('winbooks_sftp', 'email')),
  config JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS on connector_configs
ALTER TABLE connector_configs ENABLE ROW LEVEL SECURITY;

-- 4. RLS Policies
-- ===============

-- Domain events policy (update existing if needed)
DO $$ BEGIN
  -- Drop existing policy if it exists to recreate with proper name
  DROP POLICY IF EXISTS domain_events_select ON domain_events;
  
  -- Create comprehensive policy for domain events
  CREATE POLICY domain_events_entity_access ON domain_events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM entity_memberships em
      WHERE em.entity_id = domain_events.entity_id 
      AND em.user_id = auth.uid()
    )
  );
EXCEPTION WHEN duplicate_object THEN
  -- Policy already exists, continue
  NULL;
END $$;

-- Export jobs - entity members can see their jobs
CREATE POLICY export_jobs_entity_access ON export_jobs
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = export_jobs.entity_id 
    AND em.user_id = auth.uid()
  )
);

-- Connector configs - entity members can manage their configs
CREATE POLICY connector_configs_entity_access ON connector_configs
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = connector_configs.entity_id 
    AND em.user_id = auth.uid()
  )
);

-- 5. Helper Functions
-- ===================

-- Function to get export job status summary for an entity
CREATE OR REPLACE FUNCTION rpc_export_jobs_summary(p_entity_id BIGINT)
RETURNS TABLE(
  connector TEXT,
  queued_count BIGINT,
  delivering_count BIGINT,
  delivered_count BIGINT,
  failed_count BIGINT,
  skipped_count BIGINT,
  last_delivered_at TIMESTAMPTZ
)
LANGUAGE SQL SECURITY INVOKER AS $$
  SELECT 
    ej.connector,
    COUNT(*) FILTER (WHERE ej.status = 'queued') AS queued_count,
    COUNT(*) FILTER (WHERE ej.status = 'delivering') AS delivering_count,
    COUNT(*) FILTER (WHERE ej.status = 'delivered') AS delivered_count,
    COUNT(*) FILTER (WHERE ej.status = 'failed') AS failed_count,
    COUNT(*) FILTER (WHERE ej.status = 'skipped') AS skipped_count,
    MAX(ej.delivered_at) AS last_delivered_at
  FROM export_jobs ej
  WHERE ej.entity_id = p_entity_id
  GROUP BY ej.connector
  ORDER BY ej.connector;
$$;

-- Function to retry failed export jobs
CREATE OR REPLACE FUNCTION rpc_retry_export_job(p_job_id BIGINT)
RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY INVOKER AS $$
DECLARE
  job_entity_id BIGINT;
  current_status TEXT;
BEGIN
  -- Get job details and verify ownership
  SELECT entity_id, status INTO job_entity_id, current_status
  FROM export_jobs
  WHERE id = p_job_id;
  
  -- Verify job exists and user has access
  IF job_entity_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Verify entity membership (RLS will handle this but double-check)
  IF NOT EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = job_entity_id AND em.user_id = auth.uid()
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Only allow retry of failed jobs
  IF current_status != 'failed' THEN
    RETURN FALSE;
  END IF;
  
  -- Reset job to queued status
  UPDATE export_jobs
  SET status = 'queued',
      last_error = NULL,
      updated_at = NOW()
  WHERE id = p_job_id;
  
  RETURN TRUE;
END $$;

-- 6. Triggers for updated_at
-- ==========================

-- Trigger function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to export_jobs
DROP TRIGGER IF EXISTS update_export_jobs_updated_at ON export_jobs;
CREATE TRIGGER update_export_jobs_updated_at
  BEFORE UPDATE ON export_jobs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply trigger to connector_configs
DROP TRIGGER IF EXISTS update_connector_configs_updated_at ON connector_configs;
CREATE TRIGGER update_connector_configs_updated_at
  BEFORE UPDATE ON connector_configs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply trigger to domain_events if not already present
DROP TRIGGER IF EXISTS update_domain_events_updated_at ON domain_events;
CREATE TRIGGER update_domain_events_updated_at
  BEFORE UPDATE ON domain_events
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Comments for Documentation
-- =============================

COMMENT ON TABLE export_jobs IS 'Tracks execution status of export deliveries to external systems';
COMMENT ON COLUMN export_jobs.connector IS 'Delivery method: winbooks_sftp or email';
COMMENT ON COLUMN export_jobs.content_hash IS 'SHA-256 hash of export bundle for idempotency';
COMMENT ON COLUMN export_jobs.dedupe_key IS 'Optional idempotency key from source event';

COMMENT ON TABLE connector_configs IS 'Entity-specific configuration for export connectors';
COMMENT ON COLUMN connector_configs.config IS 'JSON configuration: SFTP details, email addresses, etc.';

COMMENT ON COLUMN domain_events.dedupe_key IS 'Idempotency key to prevent duplicate event processing';
COMMENT ON COLUMN domain_events.metadata IS 'Additional event metadata for processing context';