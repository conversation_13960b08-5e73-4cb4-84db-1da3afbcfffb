-- Migration 0020: Context-aware Org/Entity UX (SME vs Accounting Firm)
-- Implements org_type differentiation with auto-entity creation for SMEs

-- 1. Rename 'kind' column to 'org_type' and update values to lowercase
ALTER TABLE tenants RENAME COLUMN kind TO org_type;

-- Update existing values to lowercase
UPDATE tenants SET org_type = LOWER(org_type);

-- Drop old constraint and add new one with lowercase values
ALTER TABLE tenants DROP CONSTRAINT tenants_kind_check;
ALTER TABLE tenants ADD CONSTRAINT tenants_org_type_check
CHECK (org_type IN ('sme', 'firm'));

-- Update default value to lowercase
ALTER TABLE tenants ALTER COLUMN org_type SET DEFAULT 'sme';

-- 2. Add is_default column to entities table
ALTER TABLE entities ADD COLUMN is_default BOOLEAN NOT NULL DEFAULT false;

-- 3. Create function to auto-create entity for SME tenants
CREATE OR REPLACE FUNCTION auto_create_sme_entity()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_entity_id BIGINT;
BEGIN
  -- Only auto-create entity for SME tenants
  IF NEW.org_type = 'sme' THEN
    -- Create default entity
    INSERT INTO entities (tenant_id, name, is_default)
    VALUES (NEW.id, NEW.name || ' Books', true)
    RETURNING id INTO v_entity_id;

    -- Grant the tenant creator as entity owner
    -- Note: auth.uid() should be the user who created the tenant
    INSERT INTO entity_memberships (entity_id, user_id, role)
    VALUES (v_entity_id, auth.uid(), 'owner');
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger for auto-entity creation
CREATE TRIGGER trigger_auto_create_sme_entity
  AFTER INSERT ON tenants
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_sme_entity();

-- 4. Create constraint to prevent multiple entities for SME tenants
CREATE OR REPLACE FUNCTION check_sme_entity_limit()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_tenant_org_type TEXT;
  v_entity_count INTEGER;
BEGIN
  -- Get tenant org_type
  SELECT org_type INTO v_tenant_org_type
  FROM tenants
  WHERE id = NEW.tenant_id;

  -- If SME tenant, check entity count
  IF v_tenant_org_type = 'sme' THEN
    SELECT COUNT(*) INTO v_entity_count
    FROM entities
    WHERE tenant_id = NEW.tenant_id;

    -- Allow only one entity for SME tenants
    IF v_entity_count >= 1 THEN
      RAISE EXCEPTION 'SME tenants can only have one entity. Use accounting firm type for multiple entities.';
    END IF;
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger for SME entity limit
CREATE TRIGGER trigger_check_sme_entity_limit
  BEFORE INSERT ON entities
  FOR EACH ROW
  EXECUTE FUNCTION check_sme_entity_limit();

-- 5. Update tenant creation RPC to handle org_type and auto-entity creation
CREATE OR REPLACE FUNCTION rpc_create_tenant(
  p_name TEXT,
  p_org_type TEXT DEFAULT 'sme'
) RETURNS BIGINT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id BIGINT;
  v_user_id UUID;
BEGIN
  -- Get current user
  v_user_id := auth.uid();
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required';
  END IF;

  -- Validate org_type
  IF p_org_type NOT IN ('sme', 'firm') THEN
    RAISE EXCEPTION 'Invalid org_type: %. Must be sme or firm', p_org_type;
  END IF;

  -- Validate name
  IF p_name IS NULL OR LENGTH(TRIM(p_name)) < 2 THEN
    RAISE EXCEPTION 'Tenant name must be at least 2 characters';
  END IF;

  -- For firm creation, check if user is platform_admin
  -- For now, we'll allow any authenticated user to create tenants during onboarding
  -- This can be restricted later with proper platform_admin role checking

  -- Create tenant
  INSERT INTO tenants (name, org_type)
  VALUES (TRIM(p_name), p_org_type)
  RETURNING id INTO v_tenant_id;

  -- Auto-grant caller as tenant owner
  INSERT INTO tenant_memberships (tenant_id, user_id, role)
  VALUES (v_tenant_id, v_user_id, 'tenant_owner');

  -- Note: For SME tenants, the auto_create_sme_entity trigger will create the default entity

  RETURN v_tenant_id;
END;
$$;

-- Update function comment
COMMENT ON FUNCTION rpc_create_tenant IS 'Create new tenant with org_type and auto-entity for SME (for onboarding)';

-- 6. Update the user tenants view to include org_type
DROP VIEW IF EXISTS v_user_tenants;
CREATE VIEW v_user_tenants AS
SELECT
  tm.user_id,
  tm.tenant_id,
  tm.role,
  t.name AS tenant_name,
  t.org_type AS tenant_kind, -- Keep tenant_kind for backward compatibility
  t.org_type,
  t.created_at
FROM tenant_memberships AS tm
INNER JOIN tenants AS t ON tm.tenant_id = t.id
WHERE tm.user_id = auth.uid();

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenants_org_type ON tenants (org_type);
CREATE INDEX IF NOT EXISTS idx_entities_is_default ON entities (is_default) WHERE is_default = true;
CREATE INDEX IF NOT EXISTS idx_entities_tenant_default ON entities (tenant_id, is_default);

-- 8. Add comments for documentation
COMMENT ON COLUMN tenants.org_type IS 'Organization type: sme (single entity) or firm (multiple entities)';
COMMENT ON COLUMN entities.is_default IS 'Whether this is the default/auto-created entity for SME tenants';
COMMENT ON FUNCTION auto_create_sme_entity IS 'Automatically creates default entity for SME tenants';
COMMENT ON FUNCTION check_sme_entity_limit IS 'Prevents SME tenants from having more than one entity';