-- Track G: AI Categorization & Learning Migration
-- Creates coding events, embeddings tables, and k-NN search functions for intelligent account/VAT suggestions

-- 1. Ensure pgvector Extension
-- =============================

CREATE EXTENSION IF NOT EXISTS vector;

-- 2. Coding Events Table (training data)
-- =====================================

CREATE TABLE IF NOT EXISTS coding_events (
  id              BIGSERIAL PRIMARY KEY,
  entity_id       BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  document_id     BIGINT REFERENCES documents(id) ON DELETE SET NULL,
  supplier_name   TEXT,
  supplier_vat    TEXT,
  signature_text  TEXT NOT NULL,          -- compact text summary (supplier + lines)
  account_id      BIGINT NOT NULL,        -- chosen expense/revenue account
  vat_code_id     BIGINT,                 -- chosen VAT code
  journal_id      BIGINT,                 -- if posted locally
  confirmed_at    TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  created_at      TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at      TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS on coding_events
ALTER TABLE coding_events ENABLE ROW LEVEL SECURITY;

-- Indexes for coding events
CREATE INDEX IF NOT EXISTS idx_coding_events_entity_time 
ON coding_events(entity_id, confirmed_at DESC);

CREATE INDEX IF NOT EXISTS idx_coding_events_supplier_vat 
ON coding_events(entity_id, supplier_vat, account_id) WHERE supplier_vat IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_coding_events_account 
ON coding_events(entity_id, account_id, vat_code_id);

-- 3. Invoice Embeddings Table (vector storage)
-- ============================================

-- Standardize on 1536-dim embeddings (OpenAI text-embedding-3-small)
CREATE TABLE IF NOT EXISTS invoice_embeddings (
  id              BIGSERIAL PRIMARY KEY,
  entity_id       BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  coding_event_id BIGINT NOT NULL REFERENCES coding_events(id) ON DELETE CASCADE,
  text_hash       TEXT NOT NULL,          -- sha256(signature_text)
  embedding       VECTOR(1536) NOT NULL,
  created_at      TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  -- Ensure idempotency: one embedding per (entity, text_hash)
  CONSTRAINT unique_entity_text_hash UNIQUE (entity_id, text_hash)
);

-- Enable RLS on invoice_embeddings
ALTER TABLE invoice_embeddings ENABLE ROW LEVEL SECURITY;

-- ANN index for fast k-NN queries (tune lists parameter as data grows)
CREATE INDEX IF NOT EXISTS idx_ie_entity_ann
ON invoice_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Additional helpful indexes
CREATE INDEX IF NOT EXISTS idx_ie_entity_created 
ON invoice_embeddings(entity_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_ie_coding_event 
ON invoice_embeddings(coding_event_id);

-- 4. RLS Policies (membership-based access)
-- =========================================

-- Coding events - entity members can read/insert their entity's events
CREATE POLICY coding_events_entity_access ON coding_events
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = coding_events.entity_id 
    AND em.user_id = auth.uid()
  )
);

-- Invoice embeddings - entity members can read/insert their entity's embeddings  
CREATE POLICY invoice_embeddings_entity_access ON invoice_embeddings
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = invoice_embeddings.entity_id 
    AND em.user_id = auth.uid()
  )
);

-- 5. k-NN Search RPC Functions
-- ============================

-- Core RPC: vector(1536) parameter for direct vector queries
CREATE OR REPLACE FUNCTION rpc_ai_knn_suggest_core(
  p_entity           BIGINT,
  p_query            VECTOR(1536),
  p_k                INT DEFAULT 32,
  p_tau              DOUBLE PRECISION DEFAULT 0.15,
  p_half_life_days   DOUBLE PRECISION DEFAULT 180,
  p_supplier_vat     TEXT DEFAULT NULL,
  p_supplier_boost   DOUBLE PRECISION DEFAULT 1.15,
  p_since_days       INT DEFAULT 730
) RETURNS TABLE(
  account_id   BIGINT,
  vat_code_id  BIGINT,
  score        DOUBLE PRECISION,
  avg_sim      DOUBLE PRECISION,
  votes        INT,
  top_examples BIGINT[]
) LANGUAGE SQL STABLE SECURITY INVOKER AS $$
WITH nn AS (
  SELECT
    ce.account_id,
    ce.vat_code_id,
    ce.id                 AS coding_event_id,
    ce.supplier_vat,
    1 - (ie.embedding <=> p_query)   AS sim,      -- cosine similarity in [0,1]
    EXTRACT(EPOCH FROM (NOW() - COALESCE(ce.confirmed_at, NOW()))) / 86400.0 AS age_days
  FROM invoice_embeddings ie
  JOIN coding_events ce ON ce.id = ie.coding_event_id
  WHERE ie.entity_id = p_entity
    AND (p_since_days IS NULL OR ce.confirmed_at >= NOW() - (p_since_days || ' days')::INTERVAL)
  ORDER BY ie.embedding <=> p_query   -- ascending cosine distance
  LIMIT GREATEST(p_k, 1)
),
s AS (
  SELECT MAX(sim) AS smax FROM nn
),
w AS (
  SELECT
    account_id,
    vat_code_id,
    coding_event_id,
    sim,
    -- weights: softmax(sim) * recency_decay * optional supplier boost
    EXP( (sim - (SELECT smax FROM s)) / NULLIF(p_tau, 0.0) )
    * EXP( LN(0.5) * (age_days / NULLIF(p_half_life_days, 1e-9)) )
    * CASE WHEN p_supplier_vat IS NOT NULL AND supplier_vat = p_supplier_vat
           THEN COALESCE(p_supplier_boost, 1.0) ELSE 1.0 END
      AS w
  FROM nn
),
ranked AS (
  SELECT 
    account_id, vat_code_id, coding_event_id, sim, w,
    ROW_NUMBER() OVER (PARTITION BY account_id, vat_code_id ORDER BY sim DESC) AS rn
  FROM w
),
g AS (
  SELECT
    account_id,
    vat_code_id,
    ARRAY_AGG(coding_event_id) FILTER (WHERE rn <= 3) AS top_examples,
    COUNT(*)::INT                                      AS votes,
    AVG(sim)                                           AS avg_sim,
    SUM(w)                                             AS sum_w
  FROM ranked
  GROUP BY account_id, vat_code_id
),
z AS ( SELECT GREATEST(SUM(sum_w), 1e-12) AS z FROM g )
SELECT
  account_id,
  vat_code_id,
  (sum_w / (SELECT z FROM z))::DOUBLE PRECISION AS score,
  avg_sim::DOUBLE PRECISION,
  votes,
  top_examples
FROM g
ORDER BY score DESC, avg_sim DESC
LIMIT 5;
$$;

-- JSON/array-friendly wrapper RPC for Supabase clients
CREATE OR REPLACE FUNCTION rpc_ai_knn_suggest(
  p_entity           BIGINT,
  p_query            FLOAT8[],
  p_k                INT DEFAULT 32,
  p_tau              DOUBLE PRECISION DEFAULT 0.15,
  p_half_life_days   DOUBLE PRECISION DEFAULT 180,
  p_supplier_vat     TEXT DEFAULT NULL,
  p_supplier_boost   DOUBLE PRECISION DEFAULT 1.15,
  p_since_days       INT DEFAULT 730
) RETURNS TABLE(
  account_id   BIGINT,
  vat_code_id  BIGINT,
  score        DOUBLE PRECISION,
  avg_sim      DOUBLE PRECISION,
  votes        INT,
  top_examples BIGINT[]
) LANGUAGE SQL STABLE SECURITY INVOKER AS $$
  SELECT * FROM rpc_ai_knn_suggest_core(
    p_entity,
    p_query::VECTOR(1536),
    p_k, p_tau, p_half_life_days, p_supplier_vat, p_supplier_boost, p_since_days
  );
$$;

-- Helper RPC to get coding event details for explanations
CREATE OR REPLACE FUNCTION rpc_get_coding_event_details(
  p_entity BIGINT,
  p_event_ids BIGINT[]
) RETURNS TABLE(
  id BIGINT,
  supplier_name TEXT,
  account_id BIGINT,
  vat_code_id BIGINT,
  confirmed_at TIMESTAMPTZ
) LANGUAGE SQL STABLE SECURITY INVOKER AS $$
  SELECT ce.id, ce.supplier_name, ce.account_id, ce.vat_code_id, ce.confirmed_at
  FROM coding_events ce
  WHERE ce.entity_id = p_entity 
    AND ce.id = ANY(p_event_ids)
  ORDER BY ce.confirmed_at DESC;
$$;

-- 6. Triggers for updated_at
-- ==========================

-- Reuse existing trigger function or create if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to coding_events
DROP TRIGGER IF EXISTS update_coding_events_updated_at ON coding_events;
CREATE TRIGGER update_coding_events_updated_at
  BEFORE UPDATE ON coding_events
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Comments for Documentation
-- =============================

COMMENT ON TABLE coding_events IS 'Records user-confirmed account and VAT code choices for training AI suggestions';
COMMENT ON COLUMN coding_events.signature_text IS 'Normalized text signature combining supplier info and invoice line details';
COMMENT ON COLUMN coding_events.account_id IS 'User-confirmed GL account for this invoice type';
COMMENT ON COLUMN coding_events.vat_code_id IS 'User-confirmed VAT code for this invoice type';

COMMENT ON TABLE invoice_embeddings IS 'Vector embeddings of invoice signatures for k-NN similarity search';
COMMENT ON COLUMN invoice_embeddings.embedding IS '1536-dimensional vector from OpenAI text-embedding-3-small';
COMMENT ON COLUMN invoice_embeddings.text_hash IS 'SHA-256 hash of signature_text for deduplication';

COMMENT ON FUNCTION rpc_ai_knn_suggest(BIGINT, FLOAT8[], INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT) IS 'Performs k-NN search for account/VAT suggestions with softmax scoring';