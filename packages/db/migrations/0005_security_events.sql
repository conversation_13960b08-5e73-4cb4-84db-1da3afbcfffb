-- Security Events Migration
-- Creates table for persistent security event logging and monitoring

-- Security events table for audit trail and monitoring
CREATE TABLE security_events (
  id BIGSERIAL PRIMARY KEY
  , event_type TEXT NOT NULL CHECK (event_type IN (
    'AUTH_FAILED_LOGIN'
    , 'AUTH_SUCCESSFUL_LOGIN'
    , 'AUTH_PASSWORD_RESET'
    , 'RATE_LIMIT_EXCEEDED'
    , 'CSRF_VIOLATION'
    , 'SUSPICIOUS_REQUEST'
    , 'PERMISSION_DENIED'
    , 'DATA_EXPORT'
    , 'ADMIN_ACTION'
  ))
  , user_id UUID REFERENCES auth.users (id)
  , session_id TEXT
  , ip_address INET
  , user_agent TEXT
  , severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical'))
  , resource TEXT
  , action TEXT
  , details JSONB DEFAULT '{}'
  , created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Indexes for efficient querying
CREATE INDEX idx_security_events_type ON security_events (event_type);
CREATE INDEX idx_security_events_user ON security_events (user_id);
CREATE INDEX idx_security_events_ip ON security_events (ip_address);
CREATE INDEX idx_security_events_created_at ON security_events (created_at);
CREATE INDEX idx_security_events_severity ON security_events (severity);
CREATE INDEX idx_security_events_type_created ON security_events (
  event_type, created_at
);

-- Composite index for IP-based analysis queries
CREATE INDEX idx_security_events_ip_type_created ON security_events (
  ip_address, event_type, created_at
);

-- Enable RLS on security events table
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for security events
-- Admin users can see all security events
CREATE POLICY security_events_admin_access ON security_events FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM tenant_memberships AS tm
    INNER JOIN entities AS e ON tm.tenant_id = e.tenant_id
    INNER JOIN entity_memberships AS em ON e.id = em.entity_id
    WHERE
      tm.user_id = auth.uid()
      AND em.user_id = auth.uid()
      AND (tm.role = 'admin' OR em.role IN ('owner', 'admin'))
  )
);

-- Users can see their own security events
CREATE POLICY security_events_user_access ON security_events FOR SELECT TO authenticated
USING (user_id = auth.uid());

-- System can insert security events (bypass RLS for service role)
CREATE POLICY security_events_system_insert ON security_events FOR INSERT TO service_role
WITH CHECK (true);

-- Function to clean old security events (retention policy)
CREATE OR REPLACE FUNCTION CLEANUP_OLD_SECURITY_EVENTS(
  retention_days INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM security_events 
  WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get security event statistics  
CREATE OR REPLACE FUNCTION GET_SECURITY_EVENT_STATS(
  time_window_hours INTEGER DEFAULT 24
  , event_types TEXT [] DEFAULT null
)
RETURNS TABLE (
  event_type TEXT
  , event_count BIGINT
  , unique_ips BIGINT
  , unique_users BIGINT
  , severity_breakdown JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    se.event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT se.ip_address) as unique_ips,
    COUNT(DISTINCT se.user_id) as unique_users,
    jsonb_object_agg(se.severity, severity_count) as severity_breakdown
  FROM security_events se
  CROSS JOIN LATERAL (
    SELECT COUNT(*) as severity_count 
    FROM security_events se2 
    WHERE se2.event_type = se.event_type 
    AND se2.severity = se.severity
    AND se2.created_at >= NOW() - INTERVAL '1 hour' * time_window_hours
  ) severity_counts
  WHERE se.created_at >= NOW() - INTERVAL '1 hour' * time_window_hours
  AND (event_types IS NULL OR se.event_type = ANY(event_types))
  GROUP BY se.event_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to detect suspicious IP patterns
CREATE OR REPLACE FUNCTION DETECT_SUSPICIOUS_IPS(
  time_window_minutes INTEGER DEFAULT 5
  , failed_login_threshold INTEGER DEFAULT 10
  , rate_limit_threshold INTEGER DEFAULT 20
)
RETURNS TABLE (
  ip_address INET
  , failed_logins BIGINT
  , rate_limit_violations BIGINT
  , total_events BIGINT
  , risk_score INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    se.ip_address,
    COUNT(*) FILTER (WHERE se.event_type = 'AUTH_FAILED_LOGIN') as failed_logins,
    COUNT(*) FILTER (WHERE se.event_type = 'RATE_LIMIT_EXCEEDED') as rate_limit_violations,
    COUNT(*) as total_events,
    CASE 
      WHEN COUNT(*) FILTER (WHERE se.event_type = 'AUTH_FAILED_LOGIN') >= failed_login_threshold
           OR COUNT(*) FILTER (WHERE se.event_type = 'RATE_LIMIT_EXCEEDED') >= rate_limit_threshold THEN 100
      WHEN COUNT(*) FILTER (WHERE se.event_type = 'AUTH_FAILED_LOGIN') >= (failed_login_threshold / 2) THEN 75
      WHEN COUNT(*) FILTER (WHERE se.event_type = 'RATE_LIMIT_EXCEEDED') >= (rate_limit_threshold / 2) THEN 50
      ELSE 25
    END as risk_score
  FROM security_events se
  WHERE se.created_at >= NOW() - INTERVAL '1 minute' * time_window_minutes
  AND se.ip_address IS NOT NULL
  GROUP BY se.ip_address
  HAVING COUNT(*) FILTER (WHERE se.event_type IN ('AUTH_FAILED_LOGIN', 'RATE_LIMIT_EXCEEDED')) > 0
  ORDER BY risk_score DESC, total_events DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant appropriate permissions
GRANT SELECT ON security_events TO authenticated;
GRANT INSERT ON security_events TO service_role;
GRANT EXECUTE ON FUNCTION CLEANUP_OLD_SECURITY_EVENTS(INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION GET_SECURITY_EVENT_STATS(
  INTEGER, TEXT []
) TO authenticated;
GRANT EXECUTE ON FUNCTION DETECT_SUSPICIOUS_IPS(
  INTEGER, INTEGER, INTEGER
) TO authenticated;
