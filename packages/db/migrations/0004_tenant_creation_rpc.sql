-- Migration 0004: Add tenant creation RPC for onboarding
-- Allows new users to create their first tenant during onboarding

-- RPC: Create tenant (for onboarding)
CREATE OR REPLACE FUNCTION rpc_create_tenant(
  p_name TEXT
  , p_kind TEXT DEFAULT 'SME'
) R<PERSON><PERSON>NS BIGINT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id BIGINT;
BEGIN
  -- Validate kind
  IF p_kind NOT IN ('SME', 'FIRM') THEN
    RAISE EXCEPTION 'Invalid tenant kind: %. Must be SME or FIRM', p_kind;
  END IF;
  
  -- Validate name
  IF p_name IS NULL OR LENGTH(TRIM(p_name)) < 2 THEN
    RAISE EXCEPTION 'Tenant name must be at least 2 characters';
  END IF;
  
  -- Create tenant
  INSERT INTO tenants (name, kind)
  VALUES (TRIM(p_name), p_kind)
  RETURNING id INTO v_tenant_id;
  
  -- Auto-grant caller as tenant owner
  INSERT INTO tenant_memberships (tenant_id, user_id, role)
  VALUES (v_tenant_id, auth.uid(), 'tenant_owner');
  
  RETURN v_tenant_id;
END;
$$;

-- Comment for documentation
COMMENT ON FUNCTION rpc_create_tenant IS 'Create new tenant and grant caller as owner (for onboarding)';
