-- Inbox Confirm Orchestrator Audit Enhancement
-- Adds audit trail columns for document confirmation tracking

-- Add audit columns for document confirmation tracking
ALTER TABLE inbox_documents 
ADD COLUMN confirmed_at TIMESTAMPTZ,
ADD COLUMN confirmed_by UUID REFERENCES auth.users(id);

-- Add index for efficient queries by confirmed_by
CREATE INDEX IF NOT EXISTS idx_inbox_documents_confirmed_by 
ON inbox_documents(confirmed_by) WHERE confirmed_by IS NOT NULL;

-- Add index for confirmed_at range queries  
CREATE INDEX IF NOT EXISTS idx_inbox_documents_confirmed_at
ON inbox_documents(entity_id, confirmed_at DESC) WHERE confirmed_at IS NOT NULL;

-- Update comments for new audit columns
COMMENT ON COLUMN inbox_documents.confirmed_at IS 'Timestamp when document was confirmed by user (transition to posted/exported)';
COMMENT ON COLUMN inbox_documents.confirmed_by IS 'User ID who confirmed the document for posting/export';

-- Enhance existing domain_events table for assist mode export queueing
-- Add missing columns if they don't exist
DO $$ BEGIN
  -- Add status column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='status') THEN
    ALTER TABLE domain_events ADD COLUMN status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed'));
  END IF;
  
  -- Add error_msg column if it doesn't exist  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='error_msg') THEN
    ALTER TABLE domain_events ADD COLUMN error_msg TEXT;
  END IF;
  
  -- Add updated_at column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='updated_at') THEN
    ALTER TABLE domain_events ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL;
  END IF;
  
  -- Modify event_data to have a default if it doesn't already
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='domain_events' AND column_name='event_data' AND column_default IS NULL) THEN
    ALTER TABLE domain_events ALTER COLUMN event_data SET DEFAULT '{}';
  END IF;
END $$;

-- Enable RLS on domain_events if it doesn't already exist
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'domain_events' AND policyname = 'domain_events_select'
  ) THEN
    ALTER TABLE domain_events ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY domain_events_select ON domain_events
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM entity_memberships em
        WHERE em.entity_id = domain_events.entity_id 
        AND em.user_id = auth.uid()
      )
    );
    
    CREATE POLICY domain_events_insert ON domain_events
    FOR INSERT WITH CHECK (
      EXISTS (
        SELECT 1 FROM entity_memberships em
        WHERE em.entity_id = domain_events.entity_id 
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
      )
    );
  END IF;
END $$;

-- Add index for processing domain events efficiently
CREATE INDEX IF NOT EXISTS idx_domain_events_processing
ON domain_events(entity_id, status, created_at) WHERE status = 'pending';

-- Add updated_at trigger for domain_events if not exists
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'domain_events_updated_at'
  ) THEN
    CREATE TRIGGER domain_events_updated_at
    BEFORE UPDATE ON domain_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- RPC function wrapper for PostgreSQL advisory locks
-- Used for concurrency control in document confirmation
CREATE OR REPLACE FUNCTION rpc_try_advisory_xact_lock(key1 INTEGER, key2 INTEGER)
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT pg_try_advisory_xact_lock(key1, key2);
$$;

COMMENT ON TABLE domain_events IS 'Event queue for async processing of document exports and other domain events';
COMMENT ON COLUMN domain_events.event_type IS 'Type of event: export_requested, journal_posted, etc';
COMMENT ON COLUMN domain_events.event_data IS 'Event-specific payload data in JSON format';
COMMENT ON FUNCTION rpc_try_advisory_xact_lock IS 'Wrapper for PostgreSQL advisory lock function - used for concurrency control';