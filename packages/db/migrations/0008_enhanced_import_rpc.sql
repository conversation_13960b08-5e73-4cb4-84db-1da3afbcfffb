-- Enhanced import RPC with CODA v2 parser integration
-- Updates rpc_import_bank_transactions to utilize new tracking fields

-- Updated Function: Import bank transactions with enhanced tracking
CREATE OR REPLACE FUNCTION rpc_import_bank_transactions(
  p_entity bigint
  , p_bank_account_id bigint
  , p_transactions jsonb
  , p_batch_id text DEFAULT NULL -- New optional batch ID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_transaction jsonb;
  v_imported integer := 0;
  v_skipped integer := 0;
  v_deduped integer := 0;
  v_errors text[] := '{}';
  v_batch_id text;
BEGIN
  SET search_path = public;
  -- Validate entity access
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity 
    AND vue.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', p_entity;
  END IF;
  
  -- Validate bank account belongs to entity
  IF NOT EXISTS (
    SELECT 1 FROM bank_accounts ba
    WHERE ba.id = p_bank_account_id AND ba.entity_id = p_entity
  ) THEN
    RAISE EXCEPTION 'Bank account % does not belong to entity %', p_bank_account_id, p_entity;
  END IF;
  
  -- Generate batch ID if not provided
  v_batch_id := COALESCE(p_batch_id, 'import-' || extract(epoch from now()) || '-' || substr(md5(random()::text), 1, 8));
  
  -- Process each transaction
  FOR v_transaction IN SELECT * FROM jsonb_array_elements(p_transactions)
  LOOP
    BEGIN
      -- Try to insert transaction with enhanced fields
      INSERT INTO bank_transactions (
        bank_account_id,
        transaction_date,
        value_date,
        amount,
        counterparty_name,
        counterparty_account,
        description,
        reference,
        transaction_id,
        -- Enhanced fields from CODA v2 parser
        batch_id,
        dedupe_hash,
        currency,
        structured_ref,
        raw_json,
        status
      ) VALUES (
        p_bank_account_id,
        (v_transaction->>'transaction_date')::date,
        (v_transaction->>'value_date')::date,
        (v_transaction->>'amount')::numeric,
        v_transaction->>'counterparty_name',
        v_transaction->>'counterparty_account',
        v_transaction->>'description',
        v_transaction->>'reference',
        v_transaction->>'transaction_id',
        -- Enhanced values
        v_batch_id,
        v_transaction->>'dedupe_hash',
        COALESCE(v_transaction->>'currency', 'EUR'),
        v_transaction->>'structured_ref',
        v_transaction->'raw_json',
        COALESCE(v_transaction->>'status', 'unmatched')
      );
      
      v_imported := v_imported + 1;
      
    EXCEPTION 
      WHEN unique_violation THEN
        -- Check if it's duplicate by dedupe_hash (new) or transaction_id (old)
        IF SQLSTATE = '23505' AND POSITION('uq_bank_transactions_account_hash' in SQLERRM) > 0 THEN
          v_deduped := v_deduped + 1;
        ELSE
          v_skipped := v_skipped + 1;
        END IF;
      WHEN OTHERS THEN
        v_errors := v_errors || (SQLSTATE || ': ' || SQLERRM);
    END;
  END LOOP;
  
  RETURN jsonb_build_object(
    'imported', v_imported,
    'skipped', v_skipped,
    'deduped', v_deduped,
    'errors', v_errors,
    'batch_id', v_batch_id,
    'total_processed', jsonb_array_length(p_transactions)
  );
END;
$$;