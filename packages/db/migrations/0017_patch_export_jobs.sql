-- Migration 0017: Patch export_jobs table for missing dedupe_key column
-- Applies changes that were made to migration 0011 but need to be applied to existing databases

-- =============================================================================
-- Add missing dedupe_key column to export_jobs table (from updated migration 0011)
-- =============================================================================

-- Add dedupe_key column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='export_jobs' AND column_name='dedupe_key') THEN
    ALTER TABLE export_jobs ADD COLUMN dedupe_key TEXT;
    RAISE NOTICE 'Added dedupe_key column to export_jobs table';
  ELSE
    RAISE NOTICE 'dedupe_key column already exists in export_jobs table';
  END IF;
END $$;

-- =============================================================================
-- Comments and documentation
-- =============================================================================

COMMENT ON COLUMN export_jobs.dedupe_key IS 'Optional idempotency key from source event';

-- Verification
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'export_jobs' AND column_name = 'dedupe_key'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'Successfully verified export_jobs table has dedupe_key column';
    ELSE
        RAISE WARNING 'export_jobs table is missing dedupe_key column';
    END IF;
END $$;