-- Migration 0014: PostgreSQL Syntax Fixes
-- Fixes syntax compatibility issues from migrations 0012 and 0013

-- =============================================================================
-- 1. Fix AI Suggest Functions (Migration 0012 Fixes)
-- =============================================================================

-- Drop existing functions to recreate them with fixed syntax
DROP FUNCTION IF EXISTS rpc_ai_knn_suggest_core(BIGINT, VECTOR(1536), INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT);
DROP FUNCTION IF EXISTS rpc_ai_knn_suggest(BIGINT, FLOAT8[], INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT);

-- Fixed core RPC function with proper array slicing syntax for PostgreSQL 17
CREATE OR REPLACE FUNCTION rpc_ai_knn_suggest_core(
  p_entity           BIGINT,
  p_query            VECTOR(1536),
  p_k                INT DEFAULT 32,
  p_tau              DOUBLE PRECISION DEFAULT 0.15,
  p_half_life_days   DOUBLE PRECISION DEFAULT 180,
  p_supplier_vat     TEXT DEFAULT NULL,
  p_supplier_boost   DOUBLE PRECISION DEFAULT 1.15,
  p_since_days       INT DEFAULT 730
) RETURNS TABLE(
  account_id   BIGINT,
  vat_code_id  BIGINT,
  score        DOUBLE PRECISION,
  avg_sim      DOUBLE PRECISION,
  votes        INT,
  top_examples BIGINT[]
) LANGUAGE SQL STABLE SECURITY INVOKER AS $$
WITH nn AS (
  SELECT
    ce.account_id,
    ce.vat_code_id,
    ce.id                 AS coding_event_id,
    ce.supplier_vat,
    1 - (ie.embedding <=> p_query)   AS sim,      -- cosine similarity in [0,1]
    EXTRACT(EPOCH FROM (NOW() - COALESCE(ce.confirmed_at, NOW()))) / 86400.0 AS age_days
  FROM invoice_embeddings ie
  JOIN coding_events ce ON ce.id = ie.coding_event_id
  WHERE ie.entity_id = p_entity
    AND (p_since_days IS NULL OR ce.confirmed_at >= NOW() - (p_since_days || ' days')::INTERVAL)
  ORDER BY ie.embedding <=> p_query   -- ascending cosine distance
  LIMIT GREATEST(p_k, 1)
),
s AS (
  SELECT MAX(sim) AS smax FROM nn
),
w AS (
  SELECT
    account_id,
    vat_code_id,
    coding_event_id,
    sim,
    -- weights: softmax(sim) * recency_decay * optional supplier boost
    EXP( (sim - (SELECT smax FROM s)) / NULLIF(p_tau, 0.0) )
    * EXP( LN(0.5) * (age_days / NULLIF(p_half_life_days, 1e-9)) )
    * CASE WHEN p_supplier_vat IS NOT NULL AND supplier_vat = p_supplier_vat
           THEN COALESCE(p_supplier_boost, 1.0) ELSE 1.0 END
      AS w
  FROM nn
),
-- Fixed: Use ROW_NUMBER() and proper array aggregation for PostgreSQL 17 compatibility
ranked AS (
  SELECT
    account_id,
    vat_code_id,
    coding_event_id,
    sim,
    w,
    ROW_NUMBER() OVER (PARTITION BY account_id, vat_code_id ORDER BY sim DESC) AS rn
  FROM w
),
g AS (
  SELECT
    account_id,
    vat_code_id,
    -- Fixed: Proper array slicing with bounds checking
    CASE 
      WHEN COUNT(*) >= 3 THEN (ARRAY_AGG(coding_event_id ORDER BY sim DESC))[1:3]
      ELSE ARRAY_AGG(coding_event_id ORDER BY sim DESC)
    END AS top_examples,
    COUNT(*)::INT                                      AS votes,
    AVG(sim)                                           AS avg_sim,
    SUM(w)                                             AS sum_w
  FROM ranked
  GROUP BY account_id, vat_code_id
),
z AS ( SELECT GREATEST(SUM(sum_w), 1e-12) AS z FROM g )
SELECT
  account_id,
  vat_code_id,
  (sum_w / (SELECT z FROM z))::DOUBLE PRECISION AS score,
  avg_sim::DOUBLE PRECISION,
  votes,
  top_examples
FROM g
ORDER BY score DESC, avg_sim DESC
LIMIT 5;
$$;

-- Fixed wrapper function with proper type casting
CREATE OR REPLACE FUNCTION rpc_ai_knn_suggest(
  p_entity           BIGINT,
  p_query            FLOAT8[],
  p_k                INT DEFAULT 32,
  p_tau              DOUBLE PRECISION DEFAULT 0.15,
  p_half_life_days   DOUBLE PRECISION DEFAULT 180,
  p_supplier_vat     TEXT DEFAULT NULL,
  p_supplier_boost   DOUBLE PRECISION DEFAULT 1.15,
  p_since_days       INT DEFAULT 730
) RETURNS TABLE(
  account_id   BIGINT,
  vat_code_id  BIGINT,
  score        DOUBLE PRECISION,
  avg_sim      DOUBLE PRECISION,
  votes        INT,
  top_examples BIGINT[]
) LANGUAGE SQL STABLE SECURITY INVOKER AS $$
  SELECT * FROM rpc_ai_knn_suggest_core(
    p_entity,
    p_query::VECTOR(1536),
    p_k, p_tau, p_half_life_days, p_supplier_vat, p_supplier_boost, p_since_days
  );
$$;

-- =============================================================================
-- 2. Create Missing AI Suggest Config Table
-- =============================================================================

-- This table was referenced in the original 0012 migration but not created
CREATE TABLE IF NOT EXISTS ai_suggest_config (
  entity_id            BIGINT PRIMARY KEY REFERENCES entities(id) ON DELETE CASCADE,
  enabled              BOOLEAN NOT NULL DEFAULT true,
  embedding_model      TEXT NOT NULL DEFAULT 'text-embedding-3-small',
  k_neighbors          INT NOT NULL DEFAULT 32 CHECK (k_neighbors > 0 AND k_neighbors <= 100),
  tau_temperature      DOUBLE PRECISION NOT NULL DEFAULT 0.15 CHECK (tau_temperature > 0),
  half_life_days       DOUBLE PRECISION NOT NULL DEFAULT 180 CHECK (half_life_days > 0),
  supplier_boost       DOUBLE PRECISION NOT NULL DEFAULT 1.15 CHECK (supplier_boost >= 1.0),
  since_days           INT NOT NULL DEFAULT 730 CHECK (since_days > 0),
  min_confidence       DOUBLE PRECISION NOT NULL DEFAULT 0.1 CHECK (min_confidence >= 0 AND min_confidence <= 1),
  created_at           TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at           TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS on ai_suggest_config
ALTER TABLE ai_suggest_config ENABLE ROW LEVEL SECURITY;

-- RLS policy for ai_suggest_config (entity membership required)
CREATE POLICY ai_suggest_config_entity_access ON ai_suggest_config
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = ai_suggest_config.entity_id 
    AND em.user_id = auth.uid()
  )
);

-- Index for ai_suggest_config
CREATE INDEX IF NOT EXISTS idx_ai_suggest_config_entity 
ON ai_suggest_config(entity_id);

-- Updated at trigger for ai_suggest_config
CREATE TRIGGER ai_suggest_config_updated_at
  BEFORE UPDATE ON ai_suggest_config
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 3. Fix Email-In Triggers (Migration 0013 Fixes)
-- =============================================================================

-- Fix: Replace unsupported "CREATE TRIGGER IF NOT EXISTS" syntax with DROP/CREATE pattern

-- Drop existing triggers if they exist (may not exist due to syntax error)
DROP TRIGGER IF EXISTS inbound_aliases_updated_at ON inbound_aliases;
DROP TRIGGER IF EXISTS inbound_messages_updated_at ON inbound_messages;

-- Create triggers with correct PostgreSQL syntax
CREATE TRIGGER inbound_aliases_updated_at
  BEFORE UPDATE ON inbound_aliases
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER inbound_messages_updated_at
  BEFORE UPDATE ON inbound_messages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 4. Additional Verification and Comments
-- =============================================================================

-- Add helpful comments for the fixed functions
COMMENT ON FUNCTION rpc_ai_knn_suggest_core(BIGINT, VECTOR(1536), INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT) 
IS 'Core k-NN suggestion function using vector embeddings for account/VAT code recommendations. Fixed for PostgreSQL 17 compatibility.';

COMMENT ON FUNCTION rpc_ai_knn_suggest(BIGINT, FLOAT8[], INT, DOUBLE PRECISION, DOUBLE PRECISION, TEXT, DOUBLE PRECISION, INT)
IS 'JSON/array-friendly wrapper for k-NN suggestions. Accepts FLOAT8[] and converts to VECTOR(1536).';

COMMENT ON TABLE ai_suggest_config 
IS 'Configuration settings for AI-powered account and VAT code suggestions per entity';

-- Verify all tables exist with a comment
COMMENT ON TABLE coding_events 
IS 'Training data for AI suggestions - confirmed coding decisions from users. Fixed and verified in migration 0014.';

COMMENT ON TABLE invoice_embeddings 
IS 'Vector embeddings for invoice similarity search. Fixed and verified in migration 0014.';

-- Final verification: Ensure all triggers are working
COMMENT ON TRIGGER inbound_aliases_updated_at ON inbound_aliases
IS 'Auto-update updated_at timestamp on row changes. Fixed in migration 0014 for PostgreSQL 17 compatibility.';

COMMENT ON TRIGGER inbound_messages_updated_at ON inbound_messages  
IS 'Auto-update updated_at timestamp on row changes. Fixed in migration 0014 for PostgreSQL 17 compatibility.';