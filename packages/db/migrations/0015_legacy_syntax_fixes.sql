-- Migration 0015: Fix Legacy PostgreSQL Syntax Issues
-- Resolves PostgreSQL 17 compatibility issues in migration 0006_inbox.sql

-- =============================================================================
-- Fix inbox_documents policies with unsupported "IF NOT EXISTS" syntax
-- =============================================================================

-- Drop and recreate inbox_documents policies
DROP POLICY IF EXISTS inbox_documents_select ON inbox_documents;
CREATE POLICY inbox_documents_select ON inbox_documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS inbox_documents_insert ON inbox_documents;
CREATE POLICY inbox_documents_insert ON inbox_documents
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

DROP POLICY IF EXISTS inbox_documents_update ON inbox_documents;
CREATE POLICY inbox_documents_update ON inbox_documents
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

DROP POLICY IF EXISTS inbox_documents_delete ON inbox_documents;
CREATE POLICY inbox_documents_delete ON inbox_documents
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = inbox_documents.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

-- =============================================================================
-- Fix supplier_templates policies
-- =============================================================================

DROP POLICY IF EXISTS supplier_templates_insert ON supplier_templates;
CREATE POLICY supplier_templates_insert ON supplier_templates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

DROP POLICY IF EXISTS supplier_templates_update ON supplier_templates;
CREATE POLICY supplier_templates_update ON supplier_templates
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

DROP POLICY IF EXISTS supplier_templates_delete ON supplier_templates;
CREATE POLICY supplier_templates_delete ON supplier_templates
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = supplier_templates.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
    )
  );

-- =============================================================================
-- Fix feature_flags policies
-- =============================================================================

DROP POLICY IF EXISTS feature_flags_insert ON feature_flags;
CREATE POLICY feature_flags_insert ON feature_flags
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

DROP POLICY IF EXISTS feature_flags_update ON feature_flags;
CREATE POLICY feature_flags_update ON feature_flags
  FOR UPDATE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

DROP POLICY IF EXISTS feature_flags_delete ON feature_flags;
CREATE POLICY feature_flags_delete ON feature_flags
  FOR DELETE USING (
    EXISTS (
      SELECT 1
      FROM entity_memberships em
      WHERE em.entity_id = feature_flags.entity_id
        AND em.user_id = auth.uid()
        AND em.role IN ('owner', 'admin')
    )
  );

-- =============================================================================
-- Documentation and Verification
-- =============================================================================

COMMENT ON SCHEMA public IS 'Legacy syntax fixes applied in migration 0015 for PostgreSQL 17 compatibility';

-- Verify all policies exist and are working
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count 
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename IN ('inbox_documents', 'supplier_templates', 'feature_flags');
    
    IF policy_count < 10 THEN
        RAISE WARNING 'Expected at least 10 policies, found %', policy_count;
    ELSE
        RAISE NOTICE 'Successfully verified % RLS policies after syntax fixes', policy_count;
    END IF;
END $$;