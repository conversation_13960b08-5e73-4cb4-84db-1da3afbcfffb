-- Track H: Email-In Inbox & Auto-Capture Migration
-- Creates tables for inbound email processing with entity-specific aliases

-- 1) Inbound aliases (maps email localpart to entity)
CREATE TABLE IF NOT EXISTS inbound_aliases (
  id           BIGSERIAL PRIMARY KEY,
  entity_id    BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  localpart    TEXT   NOT NULL, -- e.g., 'inbox_acme_3f9a'
  enabled      BOOLEAN NOT NULL DEFAULT true,
  created_at   TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (localpart),
  UNIQUE (entity_id, localpart)
);

ALTER TABLE inbound_aliases ENABLE ROW LEVEL SECURITY;

-- 2) Inbound messages (audit + idempotency)
CREATE TABLE IF NOT EXISTS inbound_messages (
  id             BIGSERIAL PRIMARY KEY,
  entity_id      BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  provider       TEXT   NOT NULL CHECK (provider IN ('postmark','mailgun','ses','test')),
  message_id     TEXT   NOT NULL,    -- provider Message-Id or our UUID
  from_address   TEXT   NOT NULL,
  to_address     TEXT   NOT NULL,
  subject        TEXT,
  received_at    TIMESTAMPTZ DEFAULT NOW(),
  attachments    JSONB,              -- metadata array (filename, mime, size, hash)
  status         TEXT   NOT NULL CHECK (status IN ('accepted','rejected','processed','failed')) DEFAULT 'accepted',
  error_msg      TEXT,
  UNIQUE (entity_id, provider, message_id)
);

ALTER TABLE inbound_messages ENABLE ROW LEVEL SECURITY;

-- 3) Optional: sender allowlist (per entity)
CREATE TABLE IF NOT EXISTS inbound_sender_allowlist (
  entity_id   BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  email_like  TEXT NOT NULL,   -- e.g., '%@acme.com' or '<EMAIL>'
  created_at  TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (entity_id, email_like)
);

ALTER TABLE inbound_sender_allowlist ENABLE ROW LEVEL SECURITY;

-- RLS policies for inbound_aliases (membership-based for reading; writes are webhook-only using service role)
CREATE POLICY ia_read ON inbound_aliases
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_aliases.entity_id 
    AND em.user_id = auth.uid()
  ));

CREATE POLICY ia_insert ON inbound_aliases
  FOR INSERT WITH CHECK (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_aliases.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ));

CREATE POLICY ia_update ON inbound_aliases
  FOR UPDATE USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_aliases.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  )) WITH CHECK (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_aliases.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ));

CREATE POLICY ia_delete ON inbound_aliases
  FOR DELETE USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_aliases.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin')
  ));

-- RLS policies for inbound_messages
CREATE POLICY im_read ON inbound_messages
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_messages.entity_id 
    AND em.user_id = auth.uid()
  ));

-- Note: inbound_messages INSERT is handled by service role via webhooks, no user INSERT policy needed

-- RLS policies for inbound_sender_allowlist
CREATE POLICY isa_read ON inbound_sender_allowlist
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_sender_allowlist.entity_id 
    AND em.user_id = auth.uid()
  ));

CREATE POLICY isa_insert ON inbound_sender_allowlist
  FOR INSERT WITH CHECK (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_sender_allowlist.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ));

CREATE POLICY isa_update ON inbound_sender_allowlist
  FOR UPDATE USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_sender_allowlist.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  )) WITH CHECK (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_sender_allowlist.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ));

CREATE POLICY isa_delete ON inbound_sender_allowlist
  FOR DELETE USING (EXISTS (
    SELECT 1 FROM entity_memberships em 
    WHERE em.entity_id = inbound_sender_allowlist.entity_id 
    AND em.user_id = auth.uid()
    AND em.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ));

-- Add updated_at triggers
CREATE TRIGGER inbound_aliases_updated_at
  BEFORE UPDATE ON inbound_aliases
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER inbound_messages_updated_at
  BEFORE UPDATE ON inbound_messages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_inbound_aliases_entity_enabled
  ON inbound_aliases(entity_id, enabled) WHERE enabled = true;

CREATE INDEX IF NOT EXISTS idx_inbound_messages_entity_status_received
  ON inbound_messages(entity_id, status, received_at DESC);

CREATE INDEX IF NOT EXISTS idx_inbound_messages_provider_message
  ON inbound_messages(provider, message_id);

-- Comments for documentation
COMMENT ON TABLE inbound_aliases IS 'Entity-specific email aliases for inbound email processing';
COMMENT ON COLUMN inbound_aliases.localpart IS 'Local part of email address before @domain, e.g. inbox_acme_3f9a';

COMMENT ON TABLE inbound_messages IS 'Audit trail and idempotency for processed inbound emails';
COMMENT ON COLUMN inbound_messages.attachments IS 'JSON array of attachment metadata (filename, mime, size, hash)';
COMMENT ON COLUMN inbound_messages.message_id IS 'Provider-specific message ID or our generated UUID for deduplication';

COMMENT ON TABLE inbound_sender_allowlist IS 'Per-entity sender filtering rules using LIKE patterns';
COMMENT ON COLUMN inbound_sender_allowlist.email_like IS 'Pattern for LIKE matching, e.g. %@trusted-vendor.com';