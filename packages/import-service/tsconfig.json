{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "declaration": true, "composite": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "references": [{"path": "../domain-bank"}], "exclude": ["node_modules", "dist", "**/*.test.ts"]}