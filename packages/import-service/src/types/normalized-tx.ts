// Normalized transaction type for database insertion
export interface NormalizedTx {
  transaction_date: string     // YYYY-MM-DD format
  value_date: string          // YYYY-MM-DD format  
  amount: number              // Decimal amount (positive for credit, negative for debit)
  counterparty_name?: string
  counterparty_account?: string
  description: string
  reference?: string          // Structured reference if validated
  transaction_id: string      // Unique identifier for this transaction
  dedupe_hash: string        // Hash for deduplication
  currency?: string
  structured_ref?: string    // Original structured reference
  raw_json: Record<string, unknown> // Original parsed data for audit trail
}

// Import service response type
export interface ImportResult {
  batchId: string
  summary: {
    imported: number
    skipped: number
    deduped: number
  }
  entries: NormalizedTx[]
  accountIban?: string
  openingBalance?: string
  closingBalance?: string
  warnings: string[]
}