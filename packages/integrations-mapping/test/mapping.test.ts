import { describe, it, expect } from 'vitest'
import {
  createDefaultWinBooksMapping,
  createDefaultEmailMapping,
  validateMapping,
  resolveMapping,
  createAccountLookup,
  createVATLookup,
  validateResolvedMapping,
  mergeMappings
} from '../src'
import type { IntegrationMapping } from '../src'

describe('Mapping Schema', () => {
  it('should create valid default WinBooks mapping', () => {
    const mapping = createDefaultWinBooksMapping()
    const result = validateMapping(mapping)
    
    if (!result.success) {
      console.log('Validation errors:', result.errors)
    }
    
    expect(result.success).toBe(true)
    expect(result.mapping?.connector).toBe('winbooks_sftp')
    expect(result.mapping?.version).toBe('1.0')
    expect(result.mapping?.accounts.length).toBeGreaterThan(0)
    expect(result.mapping?.vat_codes.length).toBeGreaterThan(0)
  })
  
  it('should create valid default email mapping', () => {
    const mapping = createDefaultEmailMapping()
    const result = validateMapping(mapping)
    
    expect(result.success).toBe(true)
    expect(result.mapping?.connector).toBe('email')
  })
  
  it('should validate control accounts are present', () => {
    const mapping = createDefaultWinBooksMapping()
    
    expect(mapping.control_accounts.supplier).toBe('440000')
    expect(mapping.control_accounts.customer).toBe('400000')
    expect(mapping.control_accounts.vat_payable).toBeDefined()
    expect(mapping.control_accounts.vat_receivable).toBeDefined()
  })
  
  it('should reject mapping with duplicate internal IDs', () => {
    const mapping = createDefaultWinBooksMapping()
    
    // Add duplicate account ID
    mapping.accounts.push({
      internal_id: mapping.accounts[0].internal_id,
      external_code: '999999',
      description: 'Duplicate account'
    })
    
    const result = validateMapping(mapping)
    
    expect(result.success).toBe(false)
    expect(result.errors).toContain(`Duplicate account internal IDs: ${mapping.accounts[0].internal_id}`)
  })
  
  it('should reject mapping with duplicate external codes', () => {
    const mapping = createDefaultWinBooksMapping()
    
    // Add duplicate external code
    mapping.accounts.push({
      internal_id: 999,
      external_code: mapping.accounts[0].external_code,
      description: 'Duplicate code'
    })
    
    const result = validateMapping(mapping)
    
    expect(result.success).toBe(false)
    expect(result.errors?.[0]).toContain('Duplicate account external codes')
  })
})

describe('Mapping Resolution', () => {
  it('should resolve default mapping without overrides', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    const resolved = resolveMapping(defaultMapping)
    
    expect(Object.keys(resolved.accounts).length).toBe(defaultMapping.accounts.length)
    expect(Object.keys(resolved.vat_codes).length).toBe(defaultMapping.vat_codes.length)
    expect(resolved.control_accounts).toEqual(defaultMapping.control_accounts)
  })
  
  it('should apply entity overrides to default mapping', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    
    const entityOverride: IntegrationMapping = {
      entity_id: 123,
      connector: 'winbooks_sftp',
      version: '1.0',
      accounts: [
        { internal_id: 1, external_code: 'CUSTOM_100', description: 'Custom cash account' },
        { internal_id: 999, external_code: 'NEW_ACCOUNT', description: 'New account' }
      ],
      vat_codes: [
        { internal_id: 1, external_code: 'CUSTOM_BE21', description: 'Custom VAT 21%', rate: 21 }
      ],
      control_accounts: {
        supplier: 'CUSTOM_SUPPLIERS',
        customer: 'CUSTOM_CUSTOMERS'
      }
    }
    
    const resolved = resolveMapping(defaultMapping, entityOverride)
    
    // Check account override
    expect(resolved.accounts['1']).toBe('CUSTOM_100')
    expect(resolved.accounts['999']).toBe('NEW_ACCOUNT')
    
    // Check VAT code override
    expect(resolved.vat_codes['1']).toBe('CUSTOM_BE21')
    
    // Check control account override
    expect(resolved.control_accounts.supplier).toBe('CUSTOM_SUPPLIERS')
    expect(resolved.control_accounts.customer).toBe('CUSTOM_CUSTOMERS')
  })
  
  it('should create working account lookup function', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    const resolved = resolveMapping(defaultMapping)
    const lookupAccount = createAccountLookup(resolved)
    
    const firstAccount = defaultMapping.accounts[0]
    expect(lookupAccount(firstAccount.internal_id)).toBe(firstAccount.external_code)
    
    // Test fallback for unknown ID
    expect(lookupAccount(99999)).toBe('ACC_099999')
  })
  
  it('should create working VAT lookup function', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    const resolved = resolveMapping(defaultMapping)
    const lookupVAT = createVATLookup(resolved)
    
    const firstVAT = defaultMapping.vat_codes[0]
    expect(lookupVAT(firstVAT.internal_id)).toBe(firstVAT.external_code)
    
    // Test fallback for unknown ID
    expect(lookupVAT(99999)).toBe('VAT_UNMAPPED')
  })
})

describe('Mapping Validation', () => {
  it('should validate complete resolved mapping', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    const resolved = resolveMapping(defaultMapping)
    
    const requiredAccounts = [1, 2, 3]
    const requiredVATs = [1, 2]
    
    const validation = validateResolvedMapping(resolved, requiredAccounts, requiredVATs)
    
    expect(validation.isComplete).toBe(true)
    expect(validation.missingAccounts).toEqual([])
    expect(validation.missingVATCodes).toEqual([])
  })
  
  it('should detect missing mappings', () => {
    const defaultMapping = createDefaultWinBooksMapping()
    const resolved = resolveMapping(defaultMapping)
    
    const requiredAccounts = [1, 2, 99999] // 99999 doesn't exist
    const requiredVATs = [1, 88888] // 88888 doesn't exist
    
    const validation = validateResolvedMapping(resolved, requiredAccounts, requiredVATs)
    
    expect(validation.isComplete).toBe(false)
    expect(validation.missingAccounts).toContain(99999)
    expect(validation.missingVATCodes).toContain(88888)
  })
  
  it('should generate warnings for missing control accounts', () => {
    const mapping = createDefaultWinBooksMapping()
    mapping.control_accounts.supplier = ''
    
    const resolved = resolveMapping(mapping)
    const validation = validateResolvedMapping(resolved, [], [])
    
    expect(validation.warnings).toContain('Supplier control account not defined')
  })
})

describe('Mapping Merging', () => {
  it('should merge multiple mappings in priority order', () => {
    const base = createDefaultWinBooksMapping()
    
    const override1: IntegrationMapping = {
      connector: 'winbooks_sftp',
      version: '1.0',
      accounts: [
        { internal_id: 1, external_code: 'OVERRIDE1_100', description: 'Override 1' }
      ],
      vat_codes: [],
      control_accounts: {
        supplier: 'OVERRIDE1_SUPPLIER',
        customer: 'OVERRIDE1_CUSTOMER'
      }
    }
    
    const override2: IntegrationMapping = {
      connector: 'winbooks_sftp',
      version: '1.0',
      accounts: [
        { internal_id: 1, external_code: 'OVERRIDE2_100', description: 'Override 2' }
      ],
      vat_codes: [],
      control_accounts: {
        supplier: 'OVERRIDE2_SUPPLIER',
        customer: 'OVERRIDE2_CUSTOMER'
      }
    }
    
    const merged = mergeMappings(base, override1, override2)
    
    // Last override should win
    const account1 = merged.accounts.find(a => a.internal_id === 1)
    expect(account1?.external_code).toBe('OVERRIDE2_100')
    expect(merged.control_accounts.supplier).toBe('OVERRIDE2_SUPPLIER')
  })
  
  it('should preserve all accounts when merging', () => {
    const base = createDefaultWinBooksMapping()
    const originalAccountCount = base.accounts.length
    
    const override: IntegrationMapping = {
      connector: 'winbooks_sftp',
      version: '1.0',
      accounts: [
        { internal_id: 999, external_code: 'NEW_ACCOUNT', description: 'New account' }
      ],
      vat_codes: [],
      control_accounts: {
        supplier: '440000',
        customer: '400000'
      }
    }
    
    const merged = mergeMappings(base, override)
    
    // Should have all original accounts plus the new one
    expect(merged.accounts.length).toBe(originalAccountCount + 1)
    
    const newAccount = merged.accounts.find(a => a.internal_id === 999)
    expect(newAccount?.external_code).toBe('NEW_ACCOUNT')
  })
  
  it('should throw error when merging with no mappings', () => {
    expect(() => mergeMappings()).toThrow('At least one mapping is required')
  })
})

describe('End-to-End Integration', () => {
  it('should work with complete mapping workflow', () => {
    // Create base mapping
    const defaultMapping = createDefaultWinBooksMapping()
    
    // Create entity-specific override
    const entityOverride: IntegrationMapping = {
      entity_id: 123,
      connector: 'winbooks_sftp',
      version: '1.0',
      accounts: [
        { internal_id: 1, external_code: 'CUSTOM_CASH', description: 'Custom cash account' }
      ],
      vat_codes: [
        { internal_id: 1, external_code: 'CUSTOM_VAT21', description: 'Custom VAT 21%', rate: 21 }
      ],
      control_accounts: {
        supplier: 'CUSTOM_SUPPLIERS',
        customer: 'CUSTOM_CUSTOMERS'
      }
    }
    
    // Resolve final mapping
    const resolved = resolveMapping(defaultMapping, entityOverride)
    
    // Create lookup functions
    const lookupAccount = createAccountLookup(resolved)
    const lookupVAT = createVATLookup(resolved)
    
    // Test lookups
    expect(lookupAccount(1)).toBe('CUSTOM_CASH')
    expect(lookupVAT(1)).toBe('CUSTOM_VAT21')
    expect(resolved.control_accounts.supplier).toBe('CUSTOM_SUPPLIERS')
    
    // Validate completeness
    const validation = validateResolvedMapping(resolved, [1, 2], [1, 2])
    expect(validation.isComplete).toBe(true)
  })
})