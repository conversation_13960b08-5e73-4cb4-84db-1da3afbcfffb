"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var vitest_1 = require("vitest");
var src_1 = require("../src");
(0, vitest_1.describe)('Mapping Schema', function () {
    (0, vitest_1.it)('should create valid default WinBooks mapping', function () {
        var _a, _b, _c, _d;
        var mapping = (0, src_1.createDefaultWinBooksMapping)();
        var result = (0, src_1.validateMapping)(mapping);
        if (!result.success) {
            console.log('Validation errors:', result.errors);
        }
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)((_a = result.mapping) === null || _a === void 0 ? void 0 : _a.connector).toBe('winbooks_sftp');
        (0, vitest_1.expect)((_b = result.mapping) === null || _b === void 0 ? void 0 : _b.version).toBe('1.0');
        (0, vitest_1.expect)((_c = result.mapping) === null || _c === void 0 ? void 0 : _c.accounts.length).toBeGreaterThan(0);
        (0, vitest_1.expect)((_d = result.mapping) === null || _d === void 0 ? void 0 : _d.vat_codes.length).toBeGreaterThan(0);
    });
    (0, vitest_1.it)('should create valid default email mapping', function () {
        var _a;
        var mapping = (0, src_1.createDefaultEmailMapping)();
        var result = (0, src_1.validateMapping)(mapping);
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)((_a = result.mapping) === null || _a === void 0 ? void 0 : _a.connector).toBe('email');
    });
    (0, vitest_1.it)('should validate control accounts are present', function () {
        var mapping = (0, src_1.createDefaultWinBooksMapping)();
        (0, vitest_1.expect)(mapping.control_accounts.supplier).toBe('440000');
        (0, vitest_1.expect)(mapping.control_accounts.customer).toBe('400000');
        (0, vitest_1.expect)(mapping.control_accounts.vat_payable).toBeDefined();
        (0, vitest_1.expect)(mapping.control_accounts.vat_receivable).toBeDefined();
    });
    (0, vitest_1.it)('should reject mapping with duplicate internal IDs', function () {
        var mapping = (0, src_1.createDefaultWinBooksMapping)();
        // Add duplicate account ID
        mapping.accounts.push({
            internal_id: mapping.accounts[0].internal_id,
            external_code: '999999',
            description: 'Duplicate account'
        });
        var result = (0, src_1.validateMapping)(mapping);
        (0, vitest_1.expect)(result.success).toBe(false);
        (0, vitest_1.expect)(result.errors).toContain("Duplicate account internal IDs: ".concat(mapping.accounts[0].internal_id));
    });
    (0, vitest_1.it)('should reject mapping with duplicate external codes', function () {
        var _a;
        var mapping = (0, src_1.createDefaultWinBooksMapping)();
        // Add duplicate external code
        mapping.accounts.push({
            internal_id: 999,
            external_code: mapping.accounts[0].external_code,
            description: 'Duplicate code'
        });
        var result = (0, src_1.validateMapping)(mapping);
        (0, vitest_1.expect)(result.success).toBe(false);
        (0, vitest_1.expect)((_a = result.errors) === null || _a === void 0 ? void 0 : _a[0]).toContain('Duplicate account external codes');
    });
});
(0, vitest_1.describe)('Mapping Resolution', function () {
    (0, vitest_1.it)('should resolve default mapping without overrides', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var resolved = (0, src_1.resolveMapping)(defaultMapping);
        (0, vitest_1.expect)(Object.keys(resolved.accounts).length).toBe(defaultMapping.accounts.length);
        (0, vitest_1.expect)(Object.keys(resolved.vat_codes).length).toBe(defaultMapping.vat_codes.length);
        (0, vitest_1.expect)(resolved.control_accounts).toEqual(defaultMapping.control_accounts);
    });
    (0, vitest_1.it)('should apply entity overrides to default mapping', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var entityOverride = {
            entity_id: 123,
            connector: 'winbooks_sftp',
            version: '1.0',
            accounts: [
                { internal_id: 1, external_code: 'CUSTOM_100', description: 'Custom cash account' },
                { internal_id: 999, external_code: 'NEW_ACCOUNT', description: 'New account' }
            ],
            vat_codes: [
                { internal_id: 1, external_code: 'CUSTOM_BE21', description: 'Custom VAT 21%', rate: 21 }
            ],
            control_accounts: {
                supplier: 'CUSTOM_SUPPLIERS',
                customer: 'CUSTOM_CUSTOMERS'
            }
        };
        var resolved = (0, src_1.resolveMapping)(defaultMapping, entityOverride);
        // Check account override
        (0, vitest_1.expect)(resolved.accounts['1']).toBe('CUSTOM_100');
        (0, vitest_1.expect)(resolved.accounts['999']).toBe('NEW_ACCOUNT');
        // Check VAT code override
        (0, vitest_1.expect)(resolved.vat_codes['1']).toBe('CUSTOM_BE21');
        // Check control account override
        (0, vitest_1.expect)(resolved.control_accounts.supplier).toBe('CUSTOM_SUPPLIERS');
        (0, vitest_1.expect)(resolved.control_accounts.customer).toBe('CUSTOM_CUSTOMERS');
    });
    (0, vitest_1.it)('should create working account lookup function', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var resolved = (0, src_1.resolveMapping)(defaultMapping);
        var lookupAccount = (0, src_1.createAccountLookup)(resolved);
        var firstAccount = defaultMapping.accounts[0];
        (0, vitest_1.expect)(lookupAccount(firstAccount.internal_id)).toBe(firstAccount.external_code);
        // Test fallback for unknown ID
        (0, vitest_1.expect)(lookupAccount(99999)).toBe('ACC_099999');
    });
    (0, vitest_1.it)('should create working VAT lookup function', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var resolved = (0, src_1.resolveMapping)(defaultMapping);
        var lookupVAT = (0, src_1.createVATLookup)(resolved);
        var firstVAT = defaultMapping.vat_codes[0];
        (0, vitest_1.expect)(lookupVAT(firstVAT.internal_id)).toBe(firstVAT.external_code);
        // Test fallback for unknown ID
        (0, vitest_1.expect)(lookupVAT(99999)).toBe('VAT_UNMAPPED');
    });
});
(0, vitest_1.describe)('Mapping Validation', function () {
    (0, vitest_1.it)('should validate complete resolved mapping', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var resolved = (0, src_1.resolveMapping)(defaultMapping);
        var requiredAccounts = [1, 2, 3];
        var requiredVATs = [1, 2];
        var validation = (0, src_1.validateResolvedMapping)(resolved, requiredAccounts, requiredVATs);
        (0, vitest_1.expect)(validation.isComplete).toBe(true);
        (0, vitest_1.expect)(validation.missingAccounts).toEqual([]);
        (0, vitest_1.expect)(validation.missingVATCodes).toEqual([]);
    });
    (0, vitest_1.it)('should detect missing mappings', function () {
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        var resolved = (0, src_1.resolveMapping)(defaultMapping);
        var requiredAccounts = [1, 2, 99999]; // 99999 doesn't exist
        var requiredVATs = [1, 88888]; // 88888 doesn't exist
        var validation = (0, src_1.validateResolvedMapping)(resolved, requiredAccounts, requiredVATs);
        (0, vitest_1.expect)(validation.isComplete).toBe(false);
        (0, vitest_1.expect)(validation.missingAccounts).toContain(99999);
        (0, vitest_1.expect)(validation.missingVATCodes).toContain(88888);
    });
    (0, vitest_1.it)('should generate warnings for missing control accounts', function () {
        var mapping = (0, src_1.createDefaultWinBooksMapping)();
        mapping.control_accounts.supplier = '';
        var resolved = (0, src_1.resolveMapping)(mapping);
        var validation = (0, src_1.validateResolvedMapping)(resolved, [], []);
        (0, vitest_1.expect)(validation.warnings).toContain('Supplier control account not defined');
    });
});
(0, vitest_1.describe)('Mapping Merging', function () {
    (0, vitest_1.it)('should merge multiple mappings in priority order', function () {
        var base = (0, src_1.createDefaultWinBooksMapping)();
        var override1 = {
            connector: 'winbooks_sftp',
            version: '1.0',
            accounts: [
                { internal_id: 1, external_code: 'OVERRIDE1_100', description: 'Override 1' }
            ],
            vat_codes: [],
            control_accounts: {
                supplier: 'OVERRIDE1_SUPPLIER',
                customer: 'OVERRIDE1_CUSTOMER'
            }
        };
        var override2 = {
            connector: 'winbooks_sftp',
            version: '1.0',
            accounts: [
                { internal_id: 1, external_code: 'OVERRIDE2_100', description: 'Override 2' }
            ],
            vat_codes: [],
            control_accounts: {
                supplier: 'OVERRIDE2_SUPPLIER',
                customer: 'OVERRIDE2_CUSTOMER'
            }
        };
        var merged = (0, src_1.mergeMappings)(base, override1, override2);
        // Last override should win
        var account1 = merged.accounts.find(function (a) { return a.internal_id === 1; });
        (0, vitest_1.expect)(account1 === null || account1 === void 0 ? void 0 : account1.external_code).toBe('OVERRIDE2_100');
        (0, vitest_1.expect)(merged.control_accounts.supplier).toBe('OVERRIDE2_SUPPLIER');
    });
    (0, vitest_1.it)('should preserve all accounts when merging', function () {
        var base = (0, src_1.createDefaultWinBooksMapping)();
        var originalAccountCount = base.accounts.length;
        var override = {
            connector: 'winbooks_sftp',
            version: '1.0',
            accounts: [
                { internal_id: 999, external_code: 'NEW_ACCOUNT', description: 'New account' }
            ],
            vat_codes: [],
            control_accounts: {
                supplier: '440000',
                customer: '400000'
            }
        };
        var merged = (0, src_1.mergeMappings)(base, override);
        // Should have all original accounts plus the new one
        (0, vitest_1.expect)(merged.accounts.length).toBe(originalAccountCount + 1);
        var newAccount = merged.accounts.find(function (a) { return a.internal_id === 999; });
        (0, vitest_1.expect)(newAccount === null || newAccount === void 0 ? void 0 : newAccount.external_code).toBe('NEW_ACCOUNT');
    });
    (0, vitest_1.it)('should throw error when merging with no mappings', function () {
        (0, vitest_1.expect)(function () { return (0, src_1.mergeMappings)(); }).toThrow('At least one mapping is required');
    });
});
(0, vitest_1.describe)('End-to-End Integration', function () {
    (0, vitest_1.it)('should work with complete mapping workflow', function () {
        // Create base mapping
        var defaultMapping = (0, src_1.createDefaultWinBooksMapping)();
        // Create entity-specific override
        var entityOverride = {
            entity_id: 123,
            connector: 'winbooks_sftp',
            version: '1.0',
            accounts: [
                { internal_id: 1, external_code: 'CUSTOM_CASH', description: 'Custom cash account' }
            ],
            vat_codes: [
                { internal_id: 1, external_code: 'CUSTOM_VAT21', description: 'Custom VAT 21%', rate: 21 }
            ],
            control_accounts: {
                supplier: 'CUSTOM_SUPPLIERS',
                customer: 'CUSTOM_CUSTOMERS'
            }
        };
        // Resolve final mapping
        var resolved = (0, src_1.resolveMapping)(defaultMapping, entityOverride);
        // Create lookup functions
        var lookupAccount = (0, src_1.createAccountLookup)(resolved);
        var lookupVAT = (0, src_1.createVATLookup)(resolved);
        // Test lookups
        (0, vitest_1.expect)(lookupAccount(1)).toBe('CUSTOM_CASH');
        (0, vitest_1.expect)(lookupVAT(1)).toBe('CUSTOM_VAT21');
        (0, vitest_1.expect)(resolved.control_accounts.supplier).toBe('CUSTOM_SUPPLIERS');
        // Validate completeness
        var validation = (0, src_1.validateResolvedMapping)(resolved, [1, 2], [1, 2]);
        (0, vitest_1.expect)(validation.isComplete).toBe(true);
    });
});
//# sourceMappingURL=mapping.test.js.map