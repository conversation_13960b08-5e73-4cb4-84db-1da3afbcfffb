{"name": "@belbooks/integrations-mapping", "version": "1.0.0", "description": "Account and VAT code mapping layer for external system integrations", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "test": "vitest", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "typescript": "^5.9.2", "vitest": "^2.1.8"}, "files": ["dist", "README.md"], "keywords": ["mapping", "integration", "accounting", "chart-of-accounts", "vat-codes"], "author": "Ledgerly Team", "license": "MIT"}