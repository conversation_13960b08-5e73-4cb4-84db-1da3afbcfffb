import { z } from 'zod'

/**
 * Integration Mapping Schema
 * 
 * Defines how internal account IDs and VAT code IDs map to external system codes.
 * Supports default mappings with per-entity overrides.
 */

// =============================================================================
// CORE MAPPING TYPES
// =============================================================================

export const AccountMapping = z.object({
  internal_id: z.number().int().positive(),
  external_code: z.string().min(1, 'External account code is required'),
  description: z.string().optional(),
  is_control_account: z.boolean().default(false)
})

export const VATMapping = z.object({
  internal_id: z.number().int().positive(),
  external_code: z.string().min(1, 'External VAT code is required'),
  description: z.string().optional(),
  rate: z.number().min(0).max(100).optional()
})

export const ControlAccounts = z.object({
  supplier: z.string().min(1, 'Supplier control account code is required'),
  customer: z.string().min(1, 'Customer control account code is required'),
  vat_payable: z.string().optional(),
  vat_receivable: z.string().optional()
})

export const IntegrationMapping = z.object({
  // Metadata
  entity_id: z.number().int().positive().optional(), // null for default mappings
  connector: z.enum(['winbooks_sftp', 'email']),
  version: z.string().default('1.0'),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
  
  // Core mappings
  accounts: z.array(AccountMapping),
  vat_codes: z.array(VATMapping),
  control_accounts: ControlAccounts,
  
  // Optional metadata
  metadata: z.record(z.unknown()).optional()
})

// =============================================================================
// MAPPING RESOLUTION TYPES
// =============================================================================

export const ResolvedMapping = z.object({
  accounts: z.record(z.string()), // internal_id -> external_code
  vat_codes: z.record(z.string()), // internal_id -> external_code
  control_accounts: ControlAccounts
})

// =============================================================================
// TYPE EXPORTS
// =============================================================================

export type AccountMapping = z.infer<typeof AccountMapping>
export type VATMapping = z.infer<typeof VATMapping>
export type ControlAccounts = z.infer<typeof ControlAccounts>
export type IntegrationMapping = z.infer<typeof IntegrationMapping>
export type ResolvedMapping = z.infer<typeof ResolvedMapping>

// =============================================================================
// DEFAULT MAPPINGS
// =============================================================================

/**
 * Default Belgian WinBooks mapping
 * Based on standard Belgian chart of accounts
 */
export function createDefaultWinBooksMapping(): IntegrationMapping {
  return {
    connector: 'winbooks_sftp',
    version: '1.0',
    accounts: [
      // Assets
      { internal_id: 1, external_code: '100000', description: 'Cash' },
      { internal_id: 2, external_code: '110000', description: 'Bank accounts' },
      { internal_id: 3, external_code: '120000', description: 'Accounts receivable' },
      { internal_id: 4, external_code: '130000', description: 'Inventory' },
      { internal_id: 5, external_code: '140000', description: 'Prepaid expenses' },
      
      // Liabilities  
      { internal_id: 10, external_code: '200000', description: 'Accounts payable' },
      { internal_id: 11, external_code: '210000', description: 'VAT payable' },
      { internal_id: 12, external_code: '220000', description: 'Social security payable' },
      { internal_id: 13, external_code: '230000', description: 'Income tax payable' },
      
      // Equity
      { internal_id: 20, external_code: '300000', description: 'Share capital' },
      { internal_id: 21, external_code: '310000', description: 'Retained earnings' },
      
      // Income
      { internal_id: 30, external_code: '700000', description: 'Sales revenue' },
      { internal_id: 31, external_code: '710000', description: 'Service revenue' },
      { internal_id: 32, external_code: '720000', description: 'Other income' },
      
      // Expenses
      { internal_id: 40, external_code: '600000', description: 'Cost of goods sold' },
      { internal_id: 41, external_code: '610000', description: 'Office expenses' },
      { internal_id: 42, external_code: '620000', description: 'Professional services' },
      { internal_id: 43, external_code: '630000', description: 'Travel expenses' },
      
      // Control accounts
      { internal_id: 100, external_code: '400000', description: 'Customers control', is_control_account: true },
      { internal_id: 101, external_code: '440000', description: 'Suppliers control', is_control_account: true }
    ],
    vat_codes: [
      // Belgian standard VAT rates
      { internal_id: 1, external_code: 'BE21', description: 'Belgian VAT 21%', rate: 21 },
      { internal_id: 2, external_code: 'BE12', description: 'Belgian VAT 12%', rate: 12 },
      { internal_id: 3, external_code: 'BE06', description: 'Belgian VAT 6%', rate: 6 },
      { internal_id: 4, external_code: 'BE00', description: 'Belgian VAT 0%', rate: 0 },
      { internal_id: 5, external_code: 'BEIC', description: 'Belgian Intra-Community', rate: 0 },
      { internal_id: 6, external_code: 'BEEX', description: 'Belgian Export', rate: 0 }
    ],
    control_accounts: {
      supplier: '440000',
      customer: '400000',
      vat_payable: '451000',
      vat_receivable: '411000'
    }
  }
}

/**
 * Create default email mapping (simplified structure)
 */
export function createDefaultEmailMapping(): IntegrationMapping {
  const winbooksMapping = createDefaultWinBooksMapping()
  return {
    ...winbooksMapping,
    connector: 'email'
  }
}

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Validate integration mapping
 */
export function validateMapping(data: unknown): {
  success: boolean
  mapping?: IntegrationMapping
  errors?: string[]
} {
  try {
    const mapping = IntegrationMapping.parse(data)
    
    // Additional business logic validation
    const errors: string[] = []
    
    // Check for duplicate internal IDs in accounts
    const accountIds = mapping.accounts.map(a => a.internal_id)
    const duplicateAccountIds = accountIds.filter((id, index) => accountIds.indexOf(id) !== index)
    if (duplicateAccountIds.length > 0) {
      errors.push(`Duplicate account internal IDs: ${duplicateAccountIds.join(', ')}`)
    }
    
    // Check for duplicate internal IDs in VAT codes
    const vatIds = mapping.vat_codes.map(v => v.internal_id)
    const duplicateVatIds = vatIds.filter((id, index) => vatIds.indexOf(id) !== index)
    if (duplicateVatIds.length > 0) {
      errors.push(`Duplicate VAT code internal IDs: ${duplicateVatIds.join(', ')}`)
    }
    
    // Check for duplicate external codes in accounts
    const accountCodes = mapping.accounts.map(a => a.external_code)
    const duplicateAccountCodes = accountCodes.filter((code, index) => accountCodes.indexOf(code) !== index)
    if (duplicateAccountCodes.length > 0) {
      errors.push(`Duplicate account external codes: ${duplicateAccountCodes.join(', ')}`)
    }
    
    // Check for duplicate external codes in VAT codes
    const vatCodes = mapping.vat_codes.map(v => v.external_code)
    const duplicateVatCodes = vatCodes.filter((code, index) => vatCodes.indexOf(code) !== index)
    if (duplicateVatCodes.length > 0) {
      errors.push(`Duplicate VAT external codes: ${duplicateVatCodes.join(', ')}`)
    }
    
    if (errors.length > 0) {
      return { success: false, errors }
    }
    
    return { success: true, mapping }
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    
    return { success: false, errors: [`Validation failed: ${String(error)}`] }
  }
}