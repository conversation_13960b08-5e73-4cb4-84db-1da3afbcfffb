import type { IntegrationMapping, ResolvedMapping } from './schema'

/**
 * Mapping Resolution Engine
 * 
 * Resolves final mappings by merging default mappings with entity-specific overrides.
 * Provides efficient lookup for translation during export generation.
 */

export interface MappingResolver {
  resolveMapping(
    defaultMapping: IntegrationMapping,
    entityOverride?: IntegrationMapping
  ): ResolvedMapping
  
  lookupAccount(internalId: number): string | null
  lookupVATCode(internalId: number): string | null
  getControlAccounts(): IntegrationMapping['control_accounts']
}

/**
 * Create mapping resolver instance
 */
export function createMappingResolver(resolvedMapping: ResolvedMapping): MappingResolver {
  return new MappingResolverImpl(resolvedMapping)
}

class MappingResolverImpl implements MappingResolver {
  constructor(private resolved: ResolvedMapping) {}
  
  resolveMapping(
    defaultMapping: IntegrationMapping,
    entityOverride?: IntegrationMapping
  ): ResolvedMapping {
    // Start with default mappings
    const accounts: Record<string, string> = {}
    const vatCodes: Record<string, string> = {}
    
    // Process default account mappings
    defaultMapping.accounts.forEach(account => {
      accounts[account.internal_id.toString()] = account.external_code
    })
    
    // Process default VAT code mappings
    defaultMapping.vat_codes.forEach(vatCode => {
      vatCodes[vatCode.internal_id.toString()] = vatCode.external_code
    })
    
    let controlAccounts = defaultMapping.control_accounts
    
    // Apply entity overrides if provided
    if (entityOverride) {
      // Override account mappings
      entityOverride.accounts.forEach(account => {
        accounts[account.internal_id.toString()] = account.external_code
      })
      
      // Override VAT code mappings
      entityOverride.vat_codes.forEach(vatCode => {
        vatCodes[vatCode.internal_id.toString()] = vatCode.external_code
      })
      
      // Override control accounts
      controlAccounts = {
        ...controlAccounts,
        ...entityOverride.control_accounts
      }
    }
    
    return {
      accounts,
      vat_codes: vatCodes,
      control_accounts: controlAccounts
    }
  }
  
  lookupAccount(internalId: number): string | null {
    const key = internalId.toString()
    // eslint-disable-next-line security/detect-object-injection
    return this.resolved.accounts[key] || null
  }
  
  lookupVATCode(internalId: number): string | null {
    const key = internalId.toString()
    // eslint-disable-next-line security/detect-object-injection
    return this.resolved.vat_codes[key] || null
  }
  
  getControlAccounts(): IntegrationMapping['control_accounts'] {
    return this.resolved.control_accounts
  }
}

/**
 * Resolve mapping with fallback handling
 */
export function resolveMapping(
  defaultMapping: IntegrationMapping,
  entityOverride?: IntegrationMapping
): ResolvedMapping {
  const resolver = new MappingResolverImpl({
    accounts: {},
    vat_codes: {},
    control_accounts: defaultMapping.control_accounts
  })
  
  return resolver.resolveMapping(defaultMapping, entityOverride)
}

/**
 * Create account code lookup function
 */
export function createAccountLookup(
  resolvedMapping: ResolvedMapping
): (internalId: number) => string {
  return (internalId: number): string => {
    const key = internalId.toString()
    // eslint-disable-next-line security/detect-object-injection
    const externalCode = resolvedMapping.accounts[key]
    
    if (!externalCode) {
      // Fallback: generate a code based on internal ID
      return `ACC_${internalId.toString().padStart(6, '0')}`
    }
    
    return externalCode
  }
}

/**
 * Create VAT code lookup function
 */
export function createVATLookup(
  resolvedMapping: ResolvedMapping
): (internalId: number) => string {
  return (internalId: number): string => {
    const key = internalId.toString()
    // eslint-disable-next-line security/detect-object-injection
    const externalCode = resolvedMapping.vat_codes[key]
    
    if (!externalCode) {
      // Fallback: use generic VAT code
      return 'VAT_UNMAPPED'
    }
    
    return externalCode
  }
}

/**
 * Validate resolved mapping for completeness
 */
export function validateResolvedMapping(
  resolved: ResolvedMapping,
  requiredAccountIds: number[],
  requiredVATIds: number[]
): {
  isComplete: boolean
  missingAccounts: number[]
  missingVATCodes: number[]
  warnings: string[]
} {
  const missingAccounts: number[] = []
  const missingVATCodes: number[] = []
  const warnings: string[] = []
  
  // Check for missing account mappings
  requiredAccountIds.forEach(id => {
    if (!resolved.accounts[id.toString()]) {
      missingAccounts.push(id)
    }
  })
  
  // Check for missing VAT code mappings
  requiredVATIds.forEach(id => {
    if (!resolved.vat_codes[id.toString()]) {
      missingVATCodes.push(id)
    }
  })
  
  // Check control accounts are defined
  if (!resolved.control_accounts.supplier) {
    warnings.push('Supplier control account not defined')
  }
  
  if (!resolved.control_accounts.customer) {
    warnings.push('Customer control account not defined')
  }
  
  const isComplete = missingAccounts.length === 0 && missingVATCodes.length === 0
  
  return {
    isComplete,
    missingAccounts,
    missingVATCodes,
    warnings
  }
}

/**
 * Merge multiple mapping overrides in order of priority
 */
export function mergeMappings(...mappings: IntegrationMapping[]): IntegrationMapping {
  if (mappings.length === 0) {
    throw new Error('At least one mapping is required')
  }
  
  if (mappings.length === 1) {
    return mappings[0]
  }
  
  const [base, ...overrides] = mappings
  // eslint-disable-next-line prefer-const -- result is modified in the forEach loop below
  let result: IntegrationMapping = {
    ...base,
    accounts: [...base.accounts],
    vat_codes: [...base.vat_codes],
    control_accounts: { ...base.control_accounts }
  }
  
  overrides.forEach(override => {
    // Merge accounts: replace existing, add new
    const accountMap = new Map(result.accounts.map(a => [a.internal_id, a]))
    
    override.accounts.forEach(account => {
      accountMap.set(account.internal_id, account)
    })
    
    result.accounts = Array.from(accountMap.values())
    
    // Merge VAT codes: replace existing, add new
    const vatMap = new Map(result.vat_codes.map(v => [v.internal_id, v]))
    
    override.vat_codes.forEach(vatCode => {
      vatMap.set(vatCode.internal_id, vatCode)
    })
    
    result.vat_codes = Array.from(vatMap.values())
    
    // Merge control accounts
    result.control_accounts = {
      ...result.control_accounts,
      ...override.control_accounts
    }
    
    // Update metadata
    result.entity_id = override.entity_id || result.entity_id
    result.connector = override.connector
    result.version = override.version || result.version
    result.updated_at = override.updated_at || result.updated_at
    result.metadata = {
      ...result.metadata,
      ...override.metadata
    }
  })
  
  return result
}