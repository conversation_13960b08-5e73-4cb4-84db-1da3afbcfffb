/**
 * @belbooks/integrations-mapping
 *
 * Account and VAT code mapping layer for external system integrations.
 * Provides schema, resolution, and lookup utilities for translating internal
 * IDs to external system codes (e.g., WinBooks chart of accounts).
 *
 * @version 1.0.0
 */

// Schema exports
export {
  AccountMapping,
  VATMapping,
  ControlAccounts,
  IntegrationMapping,
  ResolvedMapping,
  createDefaultWinBooksMapping,
  createDefaultEmailMapping,
  validateMapping
} from './schema'

export type {
  AccountMapping as AccountMappingType,
  VATMapping as VATMappingType,
  ControlAccounts as ControlAccountsType,
  IntegrationMapping as IntegrationMappingType,
  ResolvedMapping as ResolvedMappingType
} from './schema'

// Resolver exports
export {
  createMappingResolver,
  resolveMapping,
  createAccountLookup,
  createVATLookup,
  validateResolvedMapping,
  mergeMappings
} from './resolver'

export type {
  MappingResolver
} from './resolver'

// Version constant
export const MAPPING_VERSION = '1.0'