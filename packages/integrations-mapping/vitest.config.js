"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var config_1 = require("vitest/config");
var path_1 = require("path");
exports.default = (0, config_1.defineConfig)({
    resolve: {
        alias: {
            '@': path_1.default.resolve(__dirname, 'src')
        }
    },
    test: {
        environment: 'node',
        globals: true,
        testTimeout: 5000,
        coverage: {
            reporter: ['text', 'html'],
            exclude: [
                'dist/**',
                'test/**',
                '**/*.d.ts',
                '**/*.test.ts'
            ]
        }
    }
});
//# sourceMappingURL=vitest.config.js.map