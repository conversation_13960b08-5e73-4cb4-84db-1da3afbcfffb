import { PostJournal, JournalLine } from '@belbooks/types'

// Safe property access utilities for DatabaseJournalLine fields
function safeCurrencyAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): number {
  if (!line || typeof line !== 'object' || !(field in line)) return 0
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' ? value : 0
}

function safeVatCodeId(line: unknown): number {
  if (!line || typeof line !== 'object' || !('vat_code_id' in line)) return 0
  const value = (line as Record<string, unknown>).vat_code_id
  return typeof value === 'number' ? value : 0
}

function safeDescription(line: unknown): string {
  if (!line || typeof line !== 'object' || !('description' in line)) return ''
  const value = (line as Record<string, unknown>).description
  return typeof value === 'string' ? value : ''
}

function hasPositiveAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): boolean {
  if (!line || typeof line !== 'object' || !(field in line)) return false
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' && value > 0
}

// Use proper type assertions for database types
interface DatabaseJournalLine {
  account_id: number
  debit_amount?: number | null
  credit_amount?: number | null
  vat_code_id?: number | null
  description?: string | null
}

interface DatabaseJournal {
  id: number
  entity_id: number
  journal_type: string
  reference?: string | null
  description: string
  journal_lines: DatabaseJournalLine[]
}

// Type guards and safe access utilities
function isValidDatabaseJournalLine(
  line: unknown
): line is DatabaseJournalLine {
  return (
    typeof line === 'object' &&
    line !== null &&
    'account_id' in line &&
    'debit_amount' in line &&
    'credit_amount' in line
  )
}

export interface ReversalOptions {
  newTransactionDate?: string
  newReference?: string
  newDescription?: string
  reverseIndividualLines?: boolean
}

export class ReversalService {
  static generateReversalJournal(
    originalJournal: DatabaseJournal,
    options: ReversalOptions = {}
  ): PostJournal {
    const reversalDate =
      options.newTransactionDate || new Date().toISOString().split('T')[0]
    const reversalRef =
      options.newReference ||
      `REV-${originalJournal.reference || originalJournal.id}`
    const reversalDesc =
      options.newDescription || `Reversal of: ${originalJournal.description}`

    const reversedLines: JournalLine[] = originalJournal.journal_lines
      .filter(isValidDatabaseJournalLine)
      .map((line: DatabaseJournalLine) => {
        const accountId = line.account_id
        const debitAmount = safeCurrencyAmount(line, 'debit_amount')
        const creditAmount = safeCurrencyAmount(line, 'credit_amount')
        const vatCodeId = safeVatCodeId(line)
        const lineDescription = safeDescription(line)
        const journalDescription = originalJournal.description || ''

        const reversedLine: JournalLine = {
          journal_id: 0, // Will be set by database
          account_id: accountId,
          description: options.reverseIndividualLines
            ? `Reversal: ${lineDescription || journalDescription}`
            : lineDescription || journalDescription,
        }

        // Flip debit/credit amounts with safe access
        if (hasPositiveAmount(line, 'debit_amount')) {
          reversedLine.credit_amount = debitAmount
        } else if (hasPositiveAmount(line, 'credit_amount')) {
          reversedLine.debit_amount = creditAmount
        }

        // Copy VAT code if present
        if (vatCodeId > 0) {
          reversedLine.vat_code_id = vatCodeId
        }

        return reversedLine
      })

    return {
      entity_id: originalJournal.entity_id,
      journal_type: `${originalJournal.journal_type || 'general'}_reversal`,
      reference: reversalRef,
      description: reversalDesc,
      transaction_date: reversalDate,
      lines: reversedLines,
    }
  }

  static createCorrectingEntry(
    originalJournal: DatabaseJournal,
    correctedLines: JournalLine[],
    correctionDate: string,
    correctionReason: string
  ): PostJournal[] {
    const journals: PostJournal[] = []

    // First, create reversal of original
    const originalRef = originalJournal.reference || ''
    const originalId = originalJournal.id
    const originalDesc = originalJournal.description || ''
    const originalType = originalJournal.journal_type || 'general'

    const reversalJournal = this.generateReversalJournal(originalJournal, {
      newTransactionDate: correctionDate,
      newReference: `CORR-REV-${originalRef || originalId}`,
      newDescription: `Correction reversal: ${correctionReason}`,
    })

    journals.push(reversalJournal)

    // Then, create corrected journal
    const correctedJournal: PostJournal = {
      entity_id: originalJournal.entity_id,
      journal_type: `${originalType}_corrected`,
      reference: `CORR-${originalRef || originalId}`,
      description: `Corrected: ${originalDesc} - ${correctionReason}`,
      transaction_date: correctionDate,
      lines: correctedLines,
    }

    journals.push(correctedJournal)

    return journals
  }

  static createAdjustingEntry(
    entityId: number,
    description: string,
    transactionDate: string,
    adjustments: Array<{
      accountId: number
      currentAmount: number
      targetAmount: number
      isDebitAccount: boolean
    }>,
    reference?: string
  ): PostJournal | null {
    const adjustmentLines: JournalLine[] = []

    adjustments.forEach(adj => {
      const difference = adj.targetAmount - adj.currentAmount

      if (Math.abs(difference) < 0.01) {
        return // No adjustment needed
      }

      const line: JournalLine = {
        journal_id: 0,
        account_id: adj.accountId,
        description: `Adjustment: ${description}`,
      }

      // For debit balance accounts (assets, expenses):
      // - Increase = debit, Decrease = credit
      // For credit balance accounts (liabilities, equity, revenue):
      // - Increase = credit, Decrease = debit
      if (adj.isDebitAccount) {
        if (difference > 0) {
          line.debit_amount = Math.abs(difference)
        } else {
          line.credit_amount = Math.abs(difference)
        }
      } else {
        if (difference > 0) {
          line.credit_amount = Math.abs(difference)
        } else {
          line.debit_amount = Math.abs(difference)
        }
      }

      adjustmentLines.push(line)
    })

    if (adjustmentLines.length === 0) {
      return null // No adjustments needed
    }

    // Ensure the adjustment journal balances with safe property access
    const totalDebits = adjustmentLines.reduce((sum, line) => {
      const debitAmount = line.debit_amount
      return sum + (typeof debitAmount === 'number' ? debitAmount : 0)
    }, 0)
    const totalCredits = adjustmentLines.reduce((sum, line) => {
      const creditAmount = line.credit_amount
      return sum + (typeof creditAmount === 'number' ? creditAmount : 0)
    }, 0)
    const imbalance = totalDebits - totalCredits

    if (Math.abs(imbalance) > 0.01) {
      throw new Error(
        `Adjusting entry is not balanced. Imbalance: ${imbalance}`
      )
    }

    return {
      entity_id: entityId,
      journal_type: 'adjusting',
      reference: reference || `ADJ-${Date.now()}`,
      description: `Adjusting Entry: ${description}`,
      transaction_date: transactionDate,
      lines: adjustmentLines,
    }
  }
}
