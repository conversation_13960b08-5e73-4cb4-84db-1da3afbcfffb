import { JournalLine } from '@belbooks/types'
import type { Database } from '@belbooks/types'

type Account = Database['public']['Tables']['accounts']['Row']

// Safe property access utilities for JournalLine fields
function safeCurrencyAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): number {
  if (!line || typeof line !== 'object' || !(field in line)) return 0
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' ? value : 0
}

function hasPositiveAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): boolean {
  if (!line || typeof line !== 'object' || !(field in line)) return false
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' && value > 0
}

// Type guards for safe database object access
function isValidAccount(account: unknown): account is Account {
  return (
    typeof account === 'object' &&
    account !== null &&
    'id' in account &&
    'code' in account &&
    'account_type' in account &&
    'normal_balance' in account
  )
}

function isValidJournalLine(line: unknown): line is JournalLine {
  return (
    typeof line === 'object' &&
    line !== null &&
    'journal_id' in line &&
    'account_id' in line
  )
}

function isValidJournal(
  journal: unknown
): journal is {
  id: number
  is_balanced: boolean
  journal_lines?: JournalLine[]
} {
  return (
    typeof journal === 'object' &&
    journal !== null &&
    'id' in journal &&
    'is_balanced' in journal
  )
}

function safeNumericAccess(value: unknown, fallback = 0): number {
  if (typeof value === 'number' && !isNaN(value)) {
    return value
  }
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? fallback : parsed
  }
  return fallback
}

function safeBooleanAccess(value: unknown, fallback = false): boolean {
  return typeof value === 'boolean' ? value : fallback
}

function safeStringAccess(value: unknown, fallback = ''): string {
  return typeof value === 'string' ? value : fallback
}

export interface InvariantCheck {
  isValid: boolean
  violations: string[]
}

export interface TrialBalanceEntry {
  account_id: number
  balance: number
}

export class LedgerInvariants {
  static checkJournalBalance(lines: JournalLine[]): InvariantCheck {
    const violations: string[] = []

    if (lines.length < 2) {
      violations.push('Journal must have at least 2 lines')
    }

    const totalDebits = lines
      .filter(
        line =>
          isValidJournalLine(line) && hasPositiveAmount(line, 'debit_amount')
      )
      .reduce((sum, line) => sum + safeCurrencyAmount(line, 'debit_amount'), 0)

    const totalCredits = lines
      .filter(
        line =>
          isValidJournalLine(line) && hasPositiveAmount(line, 'credit_amount')
      )
      .reduce((sum, line) => sum + safeCurrencyAmount(line, 'credit_amount'), 0)

    const difference = Math.abs(totalDebits - totalCredits)

    if (difference > 0.01) {
      violations.push(
        `Journal is not balanced: debits=${totalDebits}, credits=${totalCredits}, difference=${difference}`
      )
    }

    // Check that each line has exactly one of debit or credit
    lines.forEach((line, index) => {
      if (!isValidJournalLine(line)) {
        violations.push(`Line ${index + 1}: Invalid journal line structure`)
        return
      }

      const debitAmount = safeCurrencyAmount(line, 'debit_amount')
      const creditAmount = safeCurrencyAmount(line, 'credit_amount')
      const hasDebit = hasPositiveAmount(line, 'debit_amount')
      const hasCredit = hasPositiveAmount(line, 'credit_amount')

      if (!hasDebit && !hasCredit) {
        violations.push(
          `Line ${index + 1}: Must have either debit_amount or credit_amount`
        )
      }

      if (hasDebit && hasCredit) {
        violations.push(
          `Line ${index + 1}: Cannot have both debit_amount and credit_amount`
        )
      }

      if (hasDebit && debitAmount <= 0) {
        violations.push(`Line ${index + 1}: Debit amount must be positive`)
      }

      if (hasCredit && creditAmount <= 0) {
        violations.push(`Line ${index + 1}: Credit amount must be positive`)
      }
    })

    return {
      isValid: violations.length === 0,
      violations,
    }
  }

  static checkTrialBalance(
    trialBalanceEntries: TrialBalanceEntry[]
  ): InvariantCheck {
    const violations: string[] = []

    let totalDebits = 0
    let totalCredits = 0

    trialBalanceEntries.forEach(entry => {
      if (!entry || typeof entry !== 'object' || !('balance' in entry)) {
        violations.push('Invalid trial balance entry structure')
        return
      }

      const balance = safeNumericAccess(entry.balance, 0)

      // For trial balance, positive balances are debits, negative are credits
      if (balance >= 0) {
        totalDebits += balance
      } else {
        totalCredits += Math.abs(balance)
      }
    })

    const difference = Math.abs(totalDebits - totalCredits)

    if (difference > 0.01) {
      violations.push(
        `Trial balance does not balance: total debits=${totalDebits}, total credits=${totalCredits}, difference=${difference}`
      )
    }

    return {
      isValid: violations.length === 0,
      violations,
    }
  }

  static validateAccountTypes(accounts: unknown[]): InvariantCheck {
    const violations: string[] = []
    const validTypes = ['asset', 'liability', 'equity', 'revenue', 'expense']
    const validBalances = ['debit', 'credit']

    // Expected normal balances for each account type
    const expectedNormalBalances: Record<string, string> = {
      asset: 'debit',
      expense: 'debit',
      liability: 'credit',
      equity: 'credit',
      revenue: 'credit',
    }

    accounts.forEach(account => {
      if (!isValidAccount(account)) {
        violations.push('Invalid account structure encountered')
        return
      }

      const accountType = safeStringAccess(account.account_type)
      const normalBalance = safeStringAccess(account.normal_balance)
      const code = safeStringAccess(account.code)

      if (!validTypes.includes(accountType)) {
        violations.push(`Account ${code} has invalid type: ${accountType}`)
      }

      if (!validBalances.includes(normalBalance)) {
        violations.push(
          `Account ${code} has invalid normal balance: ${normalBalance}`
        )
      }

      // eslint-disable-next-line security/detect-object-injection
      const expectedBalance = expectedNormalBalances[accountType]
      if (expectedBalance && normalBalance !== expectedBalance) {
        violations.push(
          `Account ${code} (${accountType}) should have normal balance '${expectedBalance}' but has '${normalBalance}'`
        )
      }

      // Check account code format (basic validation)
      if (!code || code.trim() === '') {
        violations.push(`Account has invalid code: '${code}'`)
      }
    })

    return {
      isValid: violations.length === 0,
      violations,
    }
  }

  static checkEntityDataIntegrity(entityData: {
    accounts: unknown[]
    journals: unknown[]
    trialBalance: unknown[]
  }): InvariantCheck {
    const allViolations: string[] = []

    // Check account types
    const accountCheck = this.validateAccountTypes(entityData.accounts)
    allViolations.push(...accountCheck.violations)

    // Check trial balance
    const trialBalanceCheck = this.checkTrialBalance(
      entityData.trialBalance as TrialBalanceEntry[]
    )
    allViolations.push(...trialBalanceCheck.violations)

    // Check that all accounts in trial balance exist in chart of accounts
    const accountIds = new Set(
      entityData.accounts
        .filter(isValidAccount)
        .map(a => safeNumericAccess(a.id))
    )
    entityData.trialBalance.forEach(tb => {
      if (!tb || typeof tb !== 'object' || !('account_id' in tb)) {
        allViolations.push('Invalid trial balance entry structure')
        return
      }

      const accountId = safeNumericAccess(tb.account_id)
      if (!accountIds.has(accountId)) {
        allViolations.push(
          `Trial balance references non-existent account ID: ${accountId}`
        )
      }
    })

    // Check journal integrity
    entityData.journals.forEach((journal, journalIndex) => {
      if (!isValidJournal(journal)) {
        allViolations.push(`Invalid journal structure at index ${journalIndex}`)
        return
      }

      const journalId = safeNumericAccess(journal.id)
      const isBalanced = safeBooleanAccess(journal.is_balanced, false)

      if (!isBalanced) {
        allViolations.push(
          `Journal ${journalId} at index ${journalIndex} is marked as unbalanced`
        )
      }

      if (
        journal.journal_lines &&
        Array.isArray(journal.journal_lines) &&
        journal.journal_lines.length > 0
      ) {
        const validLines = journal.journal_lines.filter(
          (line): line is JournalLine => isValidJournalLine(line)
        )
        const balanceCheck = this.checkJournalBalance(validLines)
        balanceCheck.violations.forEach(violation => {
          allViolations.push(`Journal ${journalId}: ${violation}`)
        })
      }
    })

    return {
      isValid: allViolations.length === 0,
      violations: allViolations,
    }
  }
}
