{"name": "@belbooks/domain-ledger", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "typecheck": "tsc -p tsconfig.typecheck.json", "lint": "eslint src --ext .ts", "test": "vitest run --passWithNoTests", "test:coverage": "vitest run --coverage --passWithNoTests", "test:watch": "vitest --passWithNoTests"}, "dependencies": {"@belbooks/types": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.0", "@vitest/coverage-v8": "^2.1.9", "typescript": "^5.3.3", "vitest": "^2.1.9"}}