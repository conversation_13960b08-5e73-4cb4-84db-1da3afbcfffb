import { z } from 'zod'

/**
 * Track F: Canonical Export Contract Schema
 * 
 * This defines the versioned, stable contract for export bundles
 * that get delivered to external accounting systems like WinBooks.
 * 
 * Version 1.0 supports:
 * - AP (Accounts Payable) supplier invoices
 * - AR (Accounts Receivable) customer invoices  
 * - Belgian VAT handling
 * - Multiple delivery formats (CSV, UBL, email attachments)
 */

// =============================================================================
// CORE SCHEMAS
// =============================================================================

export const ExportLine = z.object({
  account_code: z.string().min(1, 'Account code is required'),
  vat_code: z.string().optional(),
  net: z.string().regex(/^\d+\.\d{2}$/, 'Net amount must be in format 0.00'),
  vat: z.string().regex(/^\d+\.\d{2}$/, 'VAT amount must be in format 0.00').default('0.00'),
  gross: z.string().regex(/^\d+\.\d{2}$/, 'Gross amount must be in format 0.00'),
  memo: z.string().optional(),
  line_number: z.number().int().positive().optional()
})

export const ExportPartner = z.object({
  name: z.string().min(1, 'Partner name is required'),
  vat: z.string().optional(),
  iban: z.string().optional(),
  address: z.string().optional(),
  country_code: z.string().length(2).optional() // ISO 3166-1 alpha-2
})

export const ExportTotals = z.object({
  base: z.string().regex(/^\d+\.\d{2}$/, 'Base total must be in format 0.00'),
  vat: z.string().regex(/^\d+\.\d{2}$/, 'VAT total must be in format 0.00'), 
  gross: z.string().regex(/^\d+\.\d{2}$/, 'Gross total must be in format 0.00')
})

export const ExportAttachment = z.object({
  path: z.string().min(1, 'Attachment path is required'),
  hash: z.string().min(1, 'Attachment hash is required'),
  mime: z.string().min(1, 'MIME type is required'),
  size: z.number().int().positive().optional()
})

export const ExportBundle = z.object({
  // Contract version for backward compatibility
  version: z.literal('1.0'),
  
  // Document classification
  source: z.enum(['AP', 'AR'], { description: 'AP = Accounts Payable (supplier), AR = Accounts Receivable (customer)' }),
  
  // Core identifiers
  entity_id: z.number().int().positive(),
  document_id: z.number().int().positive().nullable(), // AP doc id, null for AR if not applicable
  doc_number: z.string().min(1, 'Document number is required'),
  
  // Dates in ISO format
  issue_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Issue date must be YYYY-MM-DD'),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Due date must be YYYY-MM-DD').optional(),
  
  // Currency (Belgian focus for v1.0)
  currency: z.literal('EUR'),
  
  // Business partner details
  partner: ExportPartner,
  
  // Financial totals
  totals: ExportTotals,
  
  // Line items
  lines: z.array(ExportLine).min(1, 'At least one line item is required'),
  
  // Optional attachments (PDFs, UBL files)
  attachments: z.array(ExportAttachment).optional(),
  
  // Optional metadata for processing context
  metadata: z.record(z.unknown()).optional()
})

// =============================================================================
// TYPE EXPORTS
// =============================================================================

export type ExportLine = z.infer<typeof ExportLine>
export type ExportPartner = z.infer<typeof ExportPartner>
export type ExportTotals = z.infer<typeof ExportTotals>
export type ExportAttachment = z.infer<typeof ExportAttachment>
export type ExportBundle = z.infer<typeof ExportBundle>

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Validates an export bundle and returns detailed error information
 */
export function validateExportBundle(data: unknown): { 
  success: boolean
  bundle?: ExportBundle
  errors?: string[]
} {
  try {
    const bundle = ExportBundle.parse(data)
    
    // Additional business logic validation
    const errors: string[] = []
    
    // Validate totals consistency
    const calculatedBase = bundle.lines.reduce((sum, line) => sum + parseFloat(line.net), 0)
    const calculatedVat = bundle.lines.reduce((sum, line) => sum + parseFloat(line.vat), 0)
    const calculatedGross = bundle.lines.reduce((sum, line) => sum + parseFloat(line.gross), 0)
    
    const declaredBase = parseFloat(bundle.totals.base)
    const declaredVat = parseFloat(bundle.totals.vat)
    const declaredGross = parseFloat(bundle.totals.gross)
    
    // Allow for small rounding differences (1 cent)
    const tolerance = 0.01
    
    if (Math.abs(calculatedBase - declaredBase) > tolerance) {
      errors.push(`Base total mismatch: calculated ${calculatedBase.toFixed(2)}, declared ${bundle.totals.base}`)
    }
    
    if (Math.abs(calculatedVat - declaredVat) > tolerance) {
      errors.push(`VAT total mismatch: calculated ${calculatedVat.toFixed(2)}, declared ${bundle.totals.vat}`)
    }
    
    if (Math.abs(calculatedGross - declaredGross) > tolerance) {
      errors.push(`Gross total mismatch: calculated ${calculatedGross.toFixed(2)}, declared ${bundle.totals.gross}`)
    }
    
    if (errors.length > 0) {
      return { success: false, errors }
    }
    
    return { success: true, bundle }
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    
    return { success: false, errors: [`Validation failed: ${String(error)}`] }
  }
}

/**
 * Creates a sample export bundle for testing
 */
export function createSampleBundle(source: 'AP' | 'AR' = 'AP'): ExportBundle {
  return {
    version: '1.0',
    source,
    entity_id: 1,
    document_id: source === 'AP' ? 123 : null,
    doc_number: source === 'AP' ? 'INV-2024-001' : 'SALE-2024-001',
    issue_date: '2024-12-01',
    due_date: '2024-12-31',
    currency: 'EUR',
    partner: {
      name: source === 'AP' ? 'ACME Supplies BVBA' : 'Best Customer SA',
      vat: 'BE0123456789',
      iban: '****************',
      country_code: 'BE'
    },
    totals: {
      base: '100.00',
      vat: '21.00', 
      gross: '121.00'
    },
    lines: [
      {
        account_code: source === 'AP' ? '600000' : '400000',
        vat_code: 'BE21',
        net: '100.00',
        vat: '21.00',
        gross: '121.00',
        memo: `Sample ${source} transaction`,
        line_number: 1
      }
    ]
  }
}