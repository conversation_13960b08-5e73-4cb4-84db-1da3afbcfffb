/**
 * @belbooks/integrations-contract
 *
 * Canonical export contract and writers for external system integrations.
 * Provides versioned, stable schemas and format writers for delivery to
 * accounting systems like WinBooks.
 *
 * @version 1.0.0
 */

// Schema exports
export {
  ExportLine,
  ExportPartner,
  ExportTotals,
  ExportAttachment,
  ExportBundle,
  validateExportBundle,
  createSampleBundle
} from './schema'

export type {
  ExportLine as ExportLineType,
  ExportPartner as ExportPartnerType,
  ExportTotals as ExportTotalsType,
  ExportAttachment as ExportAttachmentType,
  ExportBundle as ExportBundleType
} from './schema'

// WinBooks writer exports
export {
  generateWinBooksCSV,
  generateWinBooksFilename,
  validateWinBooksCSV
} from './writers/winbooks'

export type {
  WinBooksCSVOptions,
  WinBooksCSVLine
} from './writers/winbooks'

// Manifest writer exports
export {
  generateContentHash,
  generateFileHash,
  calculateFileSize,
  generateManifest,
  generateManifestJSON,
  validateManifest,
  manifestsEqual,
  generateManifestFilename
} from './writers/manifest'

export type {
  ExportManifest,
  ManifestFile
} from './writers/manifest'

// Version constant
export const CONTRACT_VERSION = '1.0'