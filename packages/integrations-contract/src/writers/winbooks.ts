import type { ExportBundle } from '../schema'

/**
 * <PERSON><PERSON><PERSON>s CSV GL Writer
 * 
 * Generates CSV files compatible with WinBooks accounting software
 * using the standard Belgian GL import format.
 * 
 * Format: UTF-8, semicolon-separated
 * Columns: date;doc_number;account_code;description;debit;credit;vat_code;partner_vat;partner_name;ext_ref
 */

export interface WinBooksCSVOptions {
  includeTotalsLine?: boolean
  useControlAccounts?: boolean
  controlAccounts?: {
    supplier: string // e.g., '440000' - Suppliers control account
    customer: string // e.g., '400000' - Customers control account  
  }
}

export interface WinBooksCSVLine {
  date: string            // YYYY-MM-DD
  doc_number: string      // Invoice/document number
  account_code: string    // GL account code
  description: string     // Line description/memo
  debit: string          // Debit amount (0.00 format)
  credit: string         // Credit amount (0.00 format)
  vat_code: string       // VAT code (e.g., 'BE21', 'BE00')
  partner_vat: string    // Partner VAT number
  partner_name: string   // Partner name
  ext_ref: string        // External reference
}

/**
 * Converts ExportBundle to WinBooks CSV format
 */
export function generateWinBooksCSV(
  bundle: ExportBundle,
  options: WinBooksCSVOptions = {}
): string {
  const lines: WinBooksCSVLine[] = []
  const opts = {
    includeTotalsLine: false,
    useControlAccounts: true,
    controlAccounts: {
      supplier: '440000',
      customer: '400000'
    },
    ...options
  }

  // Determine if this is a supplier or customer transaction
  const isAP = bundle.source === 'AP'
  const controlAccount = isAP ? opts.controlAccounts.supplier : opts.controlAccounts.customer

  // Generate expense/income lines
  bundle.lines.forEach((line, index) => {
    // For AP: Debit expense accounts, credit control account
    // For AR: Debit control account, credit income accounts
    const debitAmount = isAP ? line.gross : '0.00'
    const creditAmount = isAP ? '0.00' : line.gross

    lines.push({
      date: bundle.issue_date,
      doc_number: bundle.doc_number,
      account_code: line.account_code,
      description: line.memo || `${bundle.partner.name} - Line ${index + 1}`,
      debit: debitAmount,
      credit: creditAmount,
      vat_code: line.vat_code || '',
      partner_vat: bundle.partner.vat || '',
      partner_name: bundle.partner.name,
      ext_ref: bundle.document_id ? bundle.document_id.toString() : ''
    })
  })

  // Add control account line if using control accounts
  if (opts.useControlAccounts) {
    // For AP: Credit supplier control account for gross total
    // For AR: Debit customer control account for gross total
    const controlDebit = isAP ? '0.00' : bundle.totals.gross
    const controlCredit = isAP ? bundle.totals.gross : '0.00'

    lines.push({
      date: bundle.issue_date,
      doc_number: bundle.doc_number,
      account_code: controlAccount,
      description: `${isAP ? 'Supplier' : 'Customer'}: ${bundle.partner.name}`,
      debit: controlDebit,
      credit: controlCredit,
      vat_code: '',
      partner_vat: bundle.partner.vat || '',
      partner_name: bundle.partner.name,
      ext_ref: bundle.document_id ? bundle.document_id.toString() : ''
    })
  }

  // Generate CSV content
  const header = 'date;doc_number;account_code;description;debit;credit;vat_code;partner_vat;partner_name;ext_ref'
  const csvRows = lines.map(line => 
    [
      line.date,
      escapeCSVField(line.doc_number),
      line.account_code,
      escapeCSVField(line.description),
      line.debit,
      line.credit,
      line.vat_code,
      line.partner_vat,
      escapeCSVField(line.partner_name),
      line.ext_ref
    ].join(';')
  )

  return [header, ...csvRows].join('\n') + '\n'
}

/**
 * Escape CSV field for semicolon-separated format
 */
function escapeCSVField(field: string): string {
  if (field.includes(';') || field.includes('"') || field.includes('\n')) {
    return `"${field.replace(/"/g, '""')}"`
  }
  return field
}

/**
 * Generate WinBooks import-ready filename
 */
export function generateWinBooksFilename(bundle: ExportBundle): string {
  const date = bundle.issue_date.replace(/-/g, '')
  const prefix = bundle.source === 'AP' ? 'AP' : 'AR'
  const entityId = bundle.entity_id.toString().padStart(3, '0')
  const docNumber = bundle.doc_number.replace(/[^a-zA-Z0-9]/g, '')
  
  return `${prefix}_${date}_${entityId}_${docNumber}.csv`
}

/**
 * Validate WinBooks CSV content for common issues
 */
export function validateWinBooksCSV(csvContent: string): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  const lines = csvContent.trim().split('\n')

  if (lines.length < 2) {
    errors.push('CSV must have at least header and one data line')
    return { isValid: false, errors, warnings }
  }

  const expectedHeader = 'date;doc_number;account_code;description;debit;credit;vat_code;partner_vat;partner_name;ext_ref'
  if (lines[0] !== expectedHeader) {
    errors.push('CSV header does not match expected WinBooks format')
  }

  let totalDebit = 0
  let totalCredit = 0

  // Validate each data line
  for (let i = 1; i < lines.length; i++) {
    // eslint-disable-next-line security/detect-object-injection
    const line = lines[i]
    const fields = line.split(';')
    
    if (fields.length !== 10) {
      errors.push(`Line ${i + 1}: Expected 10 fields, got ${fields.length}`)
      continue
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [date, docNumber, accountCode, description, debit, credit, _vatCode, _partnerVat, partnerName, _extRef] = fields

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      errors.push(`Line ${i + 1}: Invalid date format: ${date}`)
    }

    // Validate amounts
    if (!/^\d+\.\d{2}$/.test(debit)) {
      errors.push(`Line ${i + 1}: Invalid debit amount format: ${debit}`)
    } else {
      totalDebit += parseFloat(debit)
    }

    if (!/^\d+\.\d{2}$/.test(credit)) {
      errors.push(`Line ${i + 1}: Invalid credit amount format: ${credit}`)
    } else {
      totalCredit += parseFloat(credit)
    }

    // Validate required fields
    if (!docNumber.trim()) {
      errors.push(`Line ${i + 1}: Document number is required`)
    }

    if (!accountCode.trim()) {
      errors.push(`Line ${i + 1}: Account code is required`)
    }

    if (!description.trim()) {
      warnings.push(`Line ${i + 1}: Description is empty`)
    }

    if (!partnerName.trim()) {
      warnings.push(`Line ${i + 1}: Partner name is empty`)
    }
  }

  // Check debit/credit balance
  const difference = Math.abs(totalDebit - totalCredit)
  if (difference > 0.01) { // Allow 1 cent tolerance for rounding
    errors.push(`Debit/Credit imbalance: ${difference.toFixed(2)} (${totalDebit.toFixed(2)} vs ${totalCredit.toFixed(2)})`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}