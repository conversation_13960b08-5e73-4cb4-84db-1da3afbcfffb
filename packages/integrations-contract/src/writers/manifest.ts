import { createHash } from 'crypto'
import type { ExportBundle } from '../schema'

/**
 * Export Bundle Manifest Generator
 * 
 * Creates manifest.json with metadata and deterministic content hash
 * for idempotency and delivery tracking.
 */

export interface ExportManifest {
  version: string
  bundle_hash: string
  created_at: string
  entity_id: number
  document_id: number | null
  source: 'AP' | 'AR'
  doc_number: string
  partner: {
    name: string
    vat?: string
  }
  totals: {
    base: string
    vat: string
    gross: string
    currency: string
  }
  files: ManifestFile[]
  metadata?: Record<string, unknown>
}

export interface ManifestFile {
  name: string
  type: 'csv' | 'ubl' | 'pdf' | 'json'
  size: number
  hash: string
  mime_type: string
}

/**
 * Generate deterministic content hash for export bundle
 * Used for idempotency - same content always produces same hash
 */
export function generateContentHash(bundle: ExportBundle, files?: ManifestFile[]): string {
  const hashInput = {
    // Core bundle data that affects export content
    version: bundle.version,
    source: bundle.source,
    entity_id: bundle.entity_id,
    document_id: bundle.document_id,
    doc_number: bundle.doc_number,
    issue_date: bundle.issue_date,
    currency: bundle.currency,
    
    // Partner details
    partner: {
      name: bundle.partner.name,
      vat: bundle.partner.vat || null,
      iban: bundle.partner.iban || null
    },
    
    // Financial totals
    totals: bundle.totals,
    
    // Line items (sorted by account_code for consistency)
    lines: bundle.lines
      .map(line => ({
        account_code: line.account_code,
        vat_code: line.vat_code || null,
        net: line.net,
        vat: line.vat,
        gross: line.gross,
        memo: line.memo || null,
        line_number: line.line_number || null
      }))
      .sort((a, b) => a.account_code.localeCompare(b.account_code)),
    
    // File hashes if provided (for complete bundle hash)
    files: files ? files.map(f => ({ name: f.name, hash: f.hash })).sort((a, b) => a.name.localeCompare(b.name)) : []
  }
  
  const jsonString = JSON.stringify(hashInput, null, 0) // No pretty-printing for consistency
  return createHash('sha256').update(jsonString, 'utf8').digest('hex')
}

/**
 * Generate SHA-256 hash for file content
 */
export function generateFileHash(content: string | Buffer): string {
  return createHash('sha256').update(content).digest('hex')
}

/**
 * Calculate file size in bytes
 */
export function calculateFileSize(content: string | Buffer): number {
  return Buffer.byteLength(content, 'utf8')
}

/**
 * Generate complete export manifest
 */
export function generateManifest(
  bundle: ExportBundle,
  files: Array<{
    name: string
    content: string | Buffer
    type: 'csv' | 'ubl' | 'pdf' | 'json'
    mimeType: string
  }>
): ExportManifest {
  // Generate manifest files with hashes
  const manifestFiles: ManifestFile[] = files.map(file => ({
    name: file.name,
    type: file.type,
    size: calculateFileSize(file.content),
    hash: generateFileHash(file.content),
    mime_type: file.mimeType
  }))
  
  // Generate bundle content hash including file hashes
  const bundleHash = generateContentHash(bundle, manifestFiles)
  
  const manifest: ExportManifest = {
    version: bundle.version,
    bundle_hash: bundleHash,
    created_at: new Date().toISOString(),
    entity_id: bundle.entity_id,
    document_id: bundle.document_id,
    source: bundle.source,
    doc_number: bundle.doc_number,
    partner: {
      name: bundle.partner.name,
      vat: bundle.partner.vat
    },
    totals: {
      base: bundle.totals.base,
      vat: bundle.totals.vat,
      gross: bundle.totals.gross,
      currency: bundle.currency
    },
    files: manifestFiles,
    metadata: bundle.metadata
  }
  
  return manifest
}

/**
 * Generate manifest JSON string
 */
export function generateManifestJSON(manifest: ExportManifest): string {
  return JSON.stringify(manifest, null, 2)
}

/**
 * Validate manifest structure
 */
export function validateManifest(manifestData: unknown): {
  isValid: boolean
  manifest?: ExportManifest
  errors: string[]
} {
  const errors: string[] = []
  
  if (!manifestData || typeof manifestData !== 'object') {
    return { isValid: false, errors: ['Manifest must be an object'] }
  }
  
  const manifest = manifestData as Record<string, unknown>
  
  // Required fields validation
  const requiredFields = ['version', 'bundle_hash', 'created_at', 'entity_id', 'source', 'doc_number', 'partner', 'totals', 'files']
  
  for (const field of requiredFields) {
    if (!(field in manifest)) {
      errors.push(`Missing required field: ${field}`)
    }
  }
  
  // Version validation
  if (manifest.version !== '1.0') {
    errors.push(`Unsupported manifest version: ${String(manifest.version)}`)
  }
  
  // Hash validation
  if (typeof manifest.bundle_hash !== 'string' || !/^[a-f0-9]{64}$/.test(manifest.bundle_hash)) {
    errors.push('bundle_hash must be a valid SHA-256 hex string')
  }
  
  // Files validation
  if (!Array.isArray(manifest.files)) {
    errors.push('files must be an array')
  } else {
    manifest.files.forEach((file, index) => {
      if (typeof file !== 'object' || file === null) {
        errors.push(`files[${index}] must be an object`)
        return
      }
      
      const f = file as Record<string, unknown>
      if (typeof f.name !== 'string' || !f.name) {
        errors.push(`files[${index}].name must be a non-empty string`)
      }
      
      if (typeof f.hash !== 'string' || !/^[a-f0-9]{64}$/.test(f.hash)) {
        errors.push(`files[${index}].hash must be a valid SHA-256 hex string`)
      }
      
      if (typeof f.size !== 'number' || f.size < 0) {
        errors.push(`files[${index}].size must be a non-negative number`)
      }
    })
  }
  
  if (errors.length === 0) {
    return { isValid: true, manifest: manifest as unknown as ExportManifest, errors: [] }
  }
  
  return { isValid: false, errors }
}

/**
 * Compare two manifests for equality
 */
export function manifestsEqual(manifest1: ExportManifest, manifest2: ExportManifest): boolean {
  return manifest1.bundle_hash === manifest2.bundle_hash
}

/**
 * Generate standard manifest filename
 */
export function generateManifestFilename(bundle: ExportBundle): string {
  const date = bundle.issue_date.replace(/-/g, '')
  const prefix = bundle.source === 'AP' ? 'AP' : 'AR'
  const entityId = bundle.entity_id.toString().padStart(3, '0')
  
  return `${prefix}_${date}_${entityId}_manifest.json`
}