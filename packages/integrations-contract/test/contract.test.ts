import { describe, it, expect } from 'vitest'
import { readFileSync } from 'fs'
import { join } from 'path'
import {
  validateExportBundle,
  createSampleBundle,
  generateWinBooksCSV,
  generateWinBooksFilename,
  validateWinBooksCSV,
  generateContentHash,
  generateManifest,
  generateManifestJSON,
  validateManifest
} from '../src'
import type { ExportBundle } from '../src'

describe('Export Bundle Schema', () => {
  it('should validate a valid AP bundle', () => {
    const bundle = createSampleBundle('AP')
    const result = validateExportBundle(bundle)
    
    expect(result.success).toBe(true)
    expect(result.bundle).toBeDefined()
    expect(result.errors).toBeUndefined()
  })
  
  it('should validate a valid AR bundle', () => {
    const bundle = createSampleBundle('AR')
    const result = validateExportBundle(bundle)
    
    expect(result.success).toBe(true)
    expect(result.bundle).toBeDefined()
    expect(result.errors).toBeUndefined()
  })
  
  it('should reject bundle with invalid totals', () => {
    const bundle = createSampleBundle('AP')
    bundle.totals.base = '50.00' // Wrong total
    
    const result = validateExportBundle(bundle)
    
    expect(result.success).toBe(false)
    expect(result.errors).toContain('Base total mismatch: calculated 100.00, declared 50.00')
  })
  
  it('should reject bundle with invalid date format', () => {
    const bundle = createSampleBundle('AP')
    bundle.issue_date = '2024/12/01' // Wrong format
    
    const result = validateExportBundle(bundle)
    
    expect(result.success).toBe(false)
    expect(result.errors?.[0]).toContain('Issue date must be YYYY-MM-DD')
  })
  
  it('should reject bundle with empty lines', () => {
    const bundle = createSampleBundle('AP')
    bundle.lines = []
    
    const result = validateExportBundle(bundle)
    
    expect(result.success).toBe(false)
    expect(result.errors?.[0]).toContain('At least one line item is required')
  })
})

describe('Golden Fixtures', () => {
  it('should validate AP 21% golden fixture', () => {
    const goldenPath = join(__dirname, 'golden/ap-21-percent/bundle.json')
    const goldenContent = readFileSync(goldenPath, 'utf-8')
    const goldenBundle = JSON.parse(goldenContent)
    
    const result = validateExportBundle(goldenBundle)
    
    expect(result.success).toBe(true)
    expect(result.bundle?.source).toBe('AP')
    expect(result.bundle?.totals.gross).toBe('1000.00')
  })
  
  it('should validate AR 21% golden fixture', () => {
    const goldenPath = join(__dirname, 'golden/ar-21-percent/bundle.json')
    const goldenContent = readFileSync(goldenPath, 'utf-8')
    const goldenBundle = JSON.parse(goldenContent)
    
    const result = validateExportBundle(goldenBundle)
    
    expect(result.success).toBe(true)
    expect(result.bundle?.source).toBe('AR')
    expect(result.bundle?.totals.gross).toBe('2500.01')
  })
  
  it('should match golden CSV output for AP', () => {
    const goldenBundlePath = join(__dirname, 'golden/ap-21-percent/bundle.json')
    const goldenCSVPath = join(__dirname, 'golden/ap-21-percent/lines.csv')
    
    const bundleContent = readFileSync(goldenBundlePath, 'utf-8')
    const expectedCSV = readFileSync(goldenCSVPath, 'utf-8')
    
    const bundle = JSON.parse(bundleContent) as ExportBundle
    const generatedCSV = generateWinBooksCSV(bundle)
    
    // Normalize line endings for comparison
    expect(generatedCSV.replace(/\r\n/g, '\n')).toBe(expectedCSV.replace(/\r\n/g, '\n'))
  })
  
  it('should match golden CSV output for AR', () => {
    const goldenBundlePath = join(__dirname, 'golden/ar-21-percent/bundle.json')
    const goldenCSVPath = join(__dirname, 'golden/ar-21-percent/lines.csv')
    
    const bundleContent = readFileSync(goldenBundlePath, 'utf-8')
    const expectedCSV = readFileSync(goldenCSVPath, 'utf-8')
    
    const bundle = JSON.parse(bundleContent) as ExportBundle
    const generatedCSV = generateWinBooksCSV(bundle)
    
    // Normalize line endings for comparison
    expect(generatedCSV.replace(/\r\n/g, '\n')).toBe(expectedCSV.replace(/\r\n/g, '\n'))
  })
})

describe('WinBooks CSV Writer', () => {
  it('should generate valid CSV for AP bundle', () => {
    const bundle = createSampleBundle('AP')
    const csv = generateWinBooksCSV(bundle)
    
    const validation = validateWinBooksCSV(csv)
    
    expect(validation.isValid).toBe(true)
    expect(validation.errors).toEqual([])
    expect(csv).toContain('date;doc_number;account_code')
    expect(csv).toContain('2024-12-01;INV-2024-001')
    expect(csv).toContain('440000') // Supplier control account
  })
  
  it('should generate valid CSV for AR bundle', () => {
    const bundle = createSampleBundle('AR')
    const csv = generateWinBooksCSV(bundle)
    
    const validation = validateWinBooksCSV(csv)
    
    expect(validation.isValid).toBe(true)
    expect(validation.errors).toEqual([])
    expect(csv).toContain('400000') // Customer control account
  })
  
  it('should generate correct filename for AP', () => {
    const bundle = createSampleBundle('AP')
    const filename = generateWinBooksFilename(bundle)
    
    expect(filename).toBe('AP_20241201_001_INV2024001.csv')
  })
  
  it('should generate correct filename for AR', () => {
    const bundle = createSampleBundle('AR')
    const filename = generateWinBooksFilename(bundle)
    
    expect(filename).toBe('AR_20241201_001_SALE2024001.csv')
  })
  
  it('should escape CSV fields with semicolons', () => {
    const bundle = createSampleBundle('AP')
    bundle.partner.name = 'Company; with semicolon'
    bundle.lines[0].memo = 'Description; with; semicolons'
    
    const csv = generateWinBooksCSV(bundle)
    
    expect(csv).toContain('"Company; with semicolon"')
    expect(csv).toContain('"Description; with; semicolons"')
  })
  
  it('should balance debits and credits', () => {
    const bundle = createSampleBundle('AP')
    const csv = generateWinBooksCSV(bundle)
    
    const lines = csv.split('\n').slice(1, -1) // Remove header and empty last line
    let totalDebits = 0
    let totalCredits = 0
    
    lines.forEach(line => {
      const fields = line.split(';')
      totalDebits += parseFloat(fields[4])
      totalCredits += parseFloat(fields[5])
    })
    
    expect(Math.abs(totalDebits - totalCredits)).toBeLessThan(0.01)
  })
})

describe('Manifest Generator', () => {
  it('should generate deterministic content hash', () => {
    const bundle1 = createSampleBundle('AP')
    const bundle2 = createSampleBundle('AP')
    
    const hash1 = generateContentHash(bundle1)
    const hash2 = generateContentHash(bundle2)
    
    expect(hash1).toBe(hash2)
    expect(hash1).toMatch(/^[a-f0-9]{64}$/)
  })
  
  it('should generate different hashes for different content', () => {
    const bundle1 = createSampleBundle('AP')
    const bundle2 = createSampleBundle('AP')
    bundle2.lines[0].net = '200.00'
    
    const hash1 = generateContentHash(bundle1)
    const hash2 = generateContentHash(bundle2)
    
    expect(hash1).not.toBe(hash2)
  })
  
  it('should generate complete manifest', () => {
    const bundle = createSampleBundle('AP')
    const csv = generateWinBooksCSV(bundle)
    
    const manifest = generateManifest(bundle, [
      {
        name: 'bundle.json',
        content: JSON.stringify(bundle),
        type: 'json',
        mimeType: 'application/json'
      },
      {
        name: 'lines.csv',
        content: csv,
        type: 'csv', 
        mimeType: 'text/csv'
      }
    ])
    
    expect(manifest.version).toBe('1.0')
    expect(manifest.entity_id).toBe(bundle.entity_id)
    expect(manifest.source).toBe(bundle.source)
    expect(manifest.files).toHaveLength(2)
    expect(manifest.bundle_hash).toMatch(/^[a-f0-9]{64}$/)
  })
  
  it('should validate manifest structure', () => {
    const bundle = createSampleBundle('AP')
    const manifest = generateManifest(bundle, [])
    
    const validation = validateManifest(manifest)
    
    expect(validation.isValid).toBe(true)
    expect(validation.manifest).toEqual(manifest)
    expect(validation.errors).toEqual([])
  })
  
  it('should reject invalid manifest', () => {
    const invalidManifest = {
      version: '2.0', // Invalid version
      bundle_hash: 'invalid-hash',
      // Missing required fields
    }
    
    const validation = validateManifest(invalidManifest)
    
    expect(validation.isValid).toBe(false)
    expect(validation.errors.length).toBeGreaterThan(0)
    expect(validation.errors).toContain('Unsupported manifest version: 2.0')
  })
})

describe('End-to-End Integration', () => {
  it('should create complete export bundle from AP data', () => {
    const bundle = createSampleBundle('AP')
    
    // Generate all export files
    const csv = generateWinBooksCSV(bundle)
    const bundleJSON = JSON.stringify(bundle, null, 2)
    
    // Generate manifest
    const manifest = generateManifest(bundle, [
      {
        name: 'bundle.json',
        content: bundleJSON,
        type: 'json',
        mimeType: 'application/json'
      },
      {
        name: 'lines.csv',
        content: csv,
        type: 'csv',
        mimeType: 'text/csv'
      }
    ])
    
    const manifestJSON = generateManifestJSON(manifest)
    
    // Validate everything
    expect(validateExportBundle(bundle).success).toBe(true)
    expect(validateWinBooksCSV(csv).isValid).toBe(true)
    expect(validateManifest(manifest).isValid).toBe(true)
    expect(() => JSON.parse(manifestJSON)).not.toThrow()
    
    // Check file structure
    expect(manifest.files).toHaveLength(2)
    expect(manifest.files.find(f => f.name === 'bundle.json')).toBeDefined()
    expect(manifest.files.find(f => f.name === 'lines.csv')).toBeDefined()
  })
})