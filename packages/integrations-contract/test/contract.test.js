"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var vitest_1 = require("vitest");
var fs_1 = require("fs");
var path_1 = require("path");
var src_1 = require("../src");
(0, vitest_1.describe)('Export Bundle Schema', function () {
    (0, vitest_1.it)('should validate a valid AP bundle', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var result = (0, src_1.validateExportBundle)(bundle);
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)(result.bundle).toBeDefined();
        (0, vitest_1.expect)(result.errors).toBeUndefined();
    });
    (0, vitest_1.it)('should validate a valid AR bundle', function () {
        var bundle = (0, src_1.createSampleBundle)('AR');
        var result = (0, src_1.validateExportBundle)(bundle);
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)(result.bundle).toBeDefined();
        (0, vitest_1.expect)(result.errors).toBeUndefined();
    });
    (0, vitest_1.it)('should reject bundle with invalid totals', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        bundle.totals.base = '50.00'; // Wrong total
        var result = (0, src_1.validateExportBundle)(bundle);
        (0, vitest_1.expect)(result.success).toBe(false);
        (0, vitest_1.expect)(result.errors).toContain('Base total mismatch: calculated 100.00, declared 50.00');
    });
    (0, vitest_1.it)('should reject bundle with invalid date format', function () {
        var _a;
        var bundle = (0, src_1.createSampleBundle)('AP');
        bundle.issue_date = '2024/12/01'; // Wrong format
        var result = (0, src_1.validateExportBundle)(bundle);
        (0, vitest_1.expect)(result.success).toBe(false);
        (0, vitest_1.expect)((_a = result.errors) === null || _a === void 0 ? void 0 : _a[0]).toContain('Issue date must be YYYY-MM-DD');
    });
    (0, vitest_1.it)('should reject bundle with empty lines', function () {
        var _a;
        var bundle = (0, src_1.createSampleBundle)('AP');
        bundle.lines = [];
        var result = (0, src_1.validateExportBundle)(bundle);
        (0, vitest_1.expect)(result.success).toBe(false);
        (0, vitest_1.expect)((_a = result.errors) === null || _a === void 0 ? void 0 : _a[0]).toContain('At least one line item is required');
    });
});
(0, vitest_1.describe)('Golden Fixtures', function () {
    (0, vitest_1.it)('should validate AP 21% golden fixture', function () {
        var _a, _b;
        var goldenPath = (0, path_1.join)(__dirname, 'golden/ap-21-percent/bundle.json');
        var goldenContent = (0, fs_1.readFileSync)(goldenPath, 'utf-8');
        var goldenBundle = JSON.parse(goldenContent);
        var result = (0, src_1.validateExportBundle)(goldenBundle);
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)((_a = result.bundle) === null || _a === void 0 ? void 0 : _a.source).toBe('AP');
        (0, vitest_1.expect)((_b = result.bundle) === null || _b === void 0 ? void 0 : _b.totals.gross).toBe('1000.00');
    });
    (0, vitest_1.it)('should validate AR 21% golden fixture', function () {
        var _a, _b;
        var goldenPath = (0, path_1.join)(__dirname, 'golden/ar-21-percent/bundle.json');
        var goldenContent = (0, fs_1.readFileSync)(goldenPath, 'utf-8');
        var goldenBundle = JSON.parse(goldenContent);
        var result = (0, src_1.validateExportBundle)(goldenBundle);
        (0, vitest_1.expect)(result.success).toBe(true);
        (0, vitest_1.expect)((_a = result.bundle) === null || _a === void 0 ? void 0 : _a.source).toBe('AR');
        (0, vitest_1.expect)((_b = result.bundle) === null || _b === void 0 ? void 0 : _b.totals.gross).toBe('2500.01');
    });
    (0, vitest_1.it)('should match golden CSV output for AP', function () {
        var goldenBundlePath = (0, path_1.join)(__dirname, 'golden/ap-21-percent/bundle.json');
        var goldenCSVPath = (0, path_1.join)(__dirname, 'golden/ap-21-percent/lines.csv');
        var bundleContent = (0, fs_1.readFileSync)(goldenBundlePath, 'utf-8');
        var expectedCSV = (0, fs_1.readFileSync)(goldenCSVPath, 'utf-8');
        var bundle = JSON.parse(bundleContent);
        var generatedCSV = (0, src_1.generateWinBooksCSV)(bundle);
        // Normalize line endings for comparison
        (0, vitest_1.expect)(generatedCSV.replace(/\r\n/g, '\n')).toBe(expectedCSV.replace(/\r\n/g, '\n'));
    });
    (0, vitest_1.it)('should match golden CSV output for AR', function () {
        var goldenBundlePath = (0, path_1.join)(__dirname, 'golden/ar-21-percent/bundle.json');
        var goldenCSVPath = (0, path_1.join)(__dirname, 'golden/ar-21-percent/lines.csv');
        var bundleContent = (0, fs_1.readFileSync)(goldenBundlePath, 'utf-8');
        var expectedCSV = (0, fs_1.readFileSync)(goldenCSVPath, 'utf-8');
        var bundle = JSON.parse(bundleContent);
        var generatedCSV = (0, src_1.generateWinBooksCSV)(bundle);
        // Normalize line endings for comparison
        (0, vitest_1.expect)(generatedCSV.replace(/\r\n/g, '\n')).toBe(expectedCSV.replace(/\r\n/g, '\n'));
    });
});
(0, vitest_1.describe)('WinBooks CSV Writer', function () {
    (0, vitest_1.it)('should generate valid CSV for AP bundle', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        var validation = (0, src_1.validateWinBooksCSV)(csv);
        (0, vitest_1.expect)(validation.isValid).toBe(true);
        (0, vitest_1.expect)(validation.errors).toEqual([]);
        (0, vitest_1.expect)(csv).toContain('date;doc_number;account_code');
        (0, vitest_1.expect)(csv).toContain('2024-12-01;INV-2024-001');
        (0, vitest_1.expect)(csv).toContain('440000'); // Supplier control account
    });
    (0, vitest_1.it)('should generate valid CSV for AR bundle', function () {
        var bundle = (0, src_1.createSampleBundle)('AR');
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        var validation = (0, src_1.validateWinBooksCSV)(csv);
        (0, vitest_1.expect)(validation.isValid).toBe(true);
        (0, vitest_1.expect)(validation.errors).toEqual([]);
        (0, vitest_1.expect)(csv).toContain('400000'); // Customer control account
    });
    (0, vitest_1.it)('should generate correct filename for AP', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var filename = (0, src_1.generateWinBooksFilename)(bundle);
        (0, vitest_1.expect)(filename).toBe('AP_20241201_001_INV2024001.csv');
    });
    (0, vitest_1.it)('should generate correct filename for AR', function () {
        var bundle = (0, src_1.createSampleBundle)('AR');
        var filename = (0, src_1.generateWinBooksFilename)(bundle);
        (0, vitest_1.expect)(filename).toBe('AR_20241201_001_SALE2024001.csv');
    });
    (0, vitest_1.it)('should escape CSV fields with semicolons', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        bundle.partner.name = 'Company; with semicolon';
        bundle.lines[0].memo = 'Description; with; semicolons';
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        (0, vitest_1.expect)(csv).toContain('"Company; with semicolon"');
        (0, vitest_1.expect)(csv).toContain('"Description; with; semicolons"');
    });
    (0, vitest_1.it)('should balance debits and credits', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        var lines = csv.split('\n').slice(1, -1); // Remove header and empty last line
        var totalDebits = 0;
        var totalCredits = 0;
        lines.forEach(function (line) {
            var fields = line.split(';');
            totalDebits += parseFloat(fields[4]);
            totalCredits += parseFloat(fields[5]);
        });
        (0, vitest_1.expect)(Math.abs(totalDebits - totalCredits)).toBeLessThan(0.01);
    });
});
(0, vitest_1.describe)('Manifest Generator', function () {
    (0, vitest_1.it)('should generate deterministic content hash', function () {
        var bundle1 = (0, src_1.createSampleBundle)('AP');
        var bundle2 = (0, src_1.createSampleBundle)('AP');
        var hash1 = (0, src_1.generateContentHash)(bundle1);
        var hash2 = (0, src_1.generateContentHash)(bundle2);
        (0, vitest_1.expect)(hash1).toBe(hash2);
        (0, vitest_1.expect)(hash1).toMatch(/^[a-f0-9]{64}$/);
    });
    (0, vitest_1.it)('should generate different hashes for different content', function () {
        var bundle1 = (0, src_1.createSampleBundle)('AP');
        var bundle2 = (0, src_1.createSampleBundle)('AP');
        bundle2.lines[0].net = '200.00';
        var hash1 = (0, src_1.generateContentHash)(bundle1);
        var hash2 = (0, src_1.generateContentHash)(bundle2);
        (0, vitest_1.expect)(hash1).not.toBe(hash2);
    });
    (0, vitest_1.it)('should generate complete manifest', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        var manifest = (0, src_1.generateManifest)(bundle, [
            {
                name: 'bundle.json',
                content: JSON.stringify(bundle),
                type: 'json',
                mimeType: 'application/json'
            },
            {
                name: 'lines.csv',
                content: csv,
                type: 'csv',
                mimeType: 'text/csv'
            }
        ]);
        (0, vitest_1.expect)(manifest.version).toBe('1.0');
        (0, vitest_1.expect)(manifest.entity_id).toBe(bundle.entity_id);
        (0, vitest_1.expect)(manifest.source).toBe(bundle.source);
        (0, vitest_1.expect)(manifest.files).toHaveLength(2);
        (0, vitest_1.expect)(manifest.bundle_hash).toMatch(/^[a-f0-9]{64}$/);
    });
    (0, vitest_1.it)('should validate manifest structure', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        var manifest = (0, src_1.generateManifest)(bundle, []);
        var validation = (0, src_1.validateManifest)(manifest);
        (0, vitest_1.expect)(validation.isValid).toBe(true);
        (0, vitest_1.expect)(validation.manifest).toEqual(manifest);
        (0, vitest_1.expect)(validation.errors).toEqual([]);
    });
    (0, vitest_1.it)('should reject invalid manifest', function () {
        var invalidManifest = {
            version: '2.0', // Invalid version
            bundle_hash: 'invalid-hash',
            // Missing required fields
        };
        var validation = (0, src_1.validateManifest)(invalidManifest);
        (0, vitest_1.expect)(validation.isValid).toBe(false);
        (0, vitest_1.expect)(validation.errors.length).toBeGreaterThan(0);
        (0, vitest_1.expect)(validation.errors).toContain('Unsupported manifest version: 2.0');
    });
});
(0, vitest_1.describe)('End-to-End Integration', function () {
    (0, vitest_1.it)('should create complete export bundle from AP data', function () {
        var bundle = (0, src_1.createSampleBundle)('AP');
        // Generate all export files
        var csv = (0, src_1.generateWinBooksCSV)(bundle);
        var bundleJSON = JSON.stringify(bundle, null, 2);
        // Generate manifest
        var manifest = (0, src_1.generateManifest)(bundle, [
            {
                name: 'bundle.json',
                content: bundleJSON,
                type: 'json',
                mimeType: 'application/json'
            },
            {
                name: 'lines.csv',
                content: csv,
                type: 'csv',
                mimeType: 'text/csv'
            }
        ]);
        var manifestJSON = (0, src_1.generateManifestJSON)(manifest);
        // Validate everything
        (0, vitest_1.expect)((0, src_1.validateExportBundle)(bundle).success).toBe(true);
        (0, vitest_1.expect)((0, src_1.validateWinBooksCSV)(csv).isValid).toBe(true);
        (0, vitest_1.expect)((0, src_1.validateManifest)(manifest).isValid).toBe(true);
        (0, vitest_1.expect)(function () { return JSON.parse(manifestJSON); }).not.toThrow();
        // Check file structure
        (0, vitest_1.expect)(manifest.files).toHaveLength(2);
        (0, vitest_1.expect)(manifest.files.find(function (f) { return f.name === 'bundle.json'; })).toBeDefined();
        (0, vitest_1.expect)(manifest.files.find(function (f) { return f.name === 'lines.csv'; })).toBeDefined();
    });
});
//# sourceMappingURL=contract.test.js.map