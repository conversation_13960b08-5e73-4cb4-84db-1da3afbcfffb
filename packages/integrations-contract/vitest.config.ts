import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  test: {
    environment: 'node',
    globals: true,
    testTimeout: 10000,
    coverage: {
      reporter: ['text', 'html'],
      exclude: [
        'dist/**',
        'test/**',
        '**/*.d.ts',
        '**/*.test.ts'
      ]
    }
  }
})