{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["DOM", "ES2020"], "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitReturns": true, "noImplicitThis": true, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}