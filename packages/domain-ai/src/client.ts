/**
 * AI Suggestions Client Utilities
 * 
 * Provides client-side utilities for interacting with AI suggestion APIs
 * and formatting responses for UI consumption.
 */

import { blendNeighbors, generateExplanation, scoreToConfidence } from './blend.js';

// API Request/Response Types
export interface SuggestionRequest {
  extraction?: {
    supplier?: {
      name?: string;
      vat?: string;
    };
    lines?: Array<{
      description?: string;
      quantity?: number;
      unit_price?: number;
      vat_rate?: {
        value: number;
      };
    }>;
    invoice?: {
      number?: string;
      issue_date?: string;
      gross?: number;
    };
  };
  override_supplier?: {
    name?: string;
    vat?: string;
  };
}

export interface SuggestionResponse {
  success: boolean;
  data?: {
    suggestion: {
      account_id: number;
      vat_code_id: number | null;
      confidence: 'high' | 'medium' | 'low';
      score: number;
    } | null;
    explanation: string;
    alternatives: Array<{
      account_id: number;
      vat_code_id: number | null;
      confidence: 'high' | 'medium' | 'low';
      score: number;
    }>;
    metadata: {
      total_suggestions: number;
      entity_id: number;
      examples_count: number;
    };
  };
  error?: string;
}

// Additional response interfaces for type safety
export interface HealthCheckResponse {
  success: boolean;
  data?: {
    status: string;
  };
}

export interface FeedbackResponse {
  success: boolean;
  error?: string;
}

// API neighbor format from k-NN service
export interface APINeighbor {
  coding_event_id?: number;
  account_id: number;
  vat_code_id: number | null;
  similarity?: number;
  sim?: number;
  confirmed_at?: string | null;
  supplier_vat?: string | null;
}

export interface FeedbackRequest {
  selected_account_id: number;
  selected_vat_code_id?: number | null;
  suggestion_score?: number;
  user_comment?: string;
}

// UI-friendly suggestion format
export interface UISuggestion {
  accountId: number;
  vatCodeId: number | null;
  confidence: 'high' | 'medium' | 'low';
  confidenceScore: number;
  explanation: string;
  isRecommended: boolean;
  votes: number;
  avgSimilarity: number;
}

/**
 * Client for interacting with AI suggestion APIs.
 */
export class SuggestionClient {
  constructor(
    private baseUrl: string,
    private authToken?: string
  ) {}

  /**
   * Get AI suggestions for a document.
   */
  async getSuggestions(
    documentId: number, 
    request?: SuggestionRequest
  ): Promise<SuggestionResponse> {
    const url = `${this.baseUrl}/documents/${documentId}/suggest`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.authToken && { Authorization: `Bearer ${this.authToken}` })
      },
      body: JSON.stringify(request || {})
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Suggestion API error: ${response.status} - ${error}`);
    }

    return await response.json() as SuggestionResponse;
  }

  /**
   * Submit feedback on AI suggestions.
   */
  async submitFeedback(
    documentId: number, 
    feedback: FeedbackRequest
  ): Promise<FeedbackResponse> {
    const url = `${this.baseUrl}/documents/${documentId}/suggest-feedback`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.authToken && { Authorization: `Bearer ${this.authToken}` })
      },
      body: JSON.stringify(feedback)
    });

    return await response.json() as FeedbackResponse;
  }

  /**
   * Check health of AI suggestion service.
   */
  async checkHealth(): Promise<{ status: string; healthy: boolean }> {
    const url = `${this.baseUrl}/ai-suggest/health`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        ...(this.authToken && { Authorization: `Bearer ${this.authToken}` })
      }
    });

    const result = await response.json() as HealthCheckResponse;
    return {
      status: result.data?.status || 'unknown',
      healthy: result.success || false
    };
  }
}

/**
 * Factory function to create a suggestion client.
 */
export function createSuggestionClient(
  baseUrl: string,
  authToken?: string
): SuggestionClient {
  return new SuggestionClient(baseUrl, authToken);
}

/**
 * Format API suggestions for UI consumption.
 * 
 * @param apiResponse Raw API response
 * @param supplierName Optional supplier name for context
 * @returns Array of UI-friendly suggestions
 */
export function formatSuggestionsForUI(
  apiResponse: SuggestionResponse,
  _supplierName?: string
): UISuggestion[] {
  if (!apiResponse.success || !apiResponse.data) {
    return [];
  }

  const { suggestion, alternatives } = apiResponse.data;
  const allSuggestions = [
    ...(suggestion ? [suggestion] : []),
    ...alternatives
  ];

  return allSuggestions.map((s, index) => ({
    accountId: s.account_id,
    vatCodeId: s.vat_code_id,
    confidence: s.confidence,
    confidenceScore: s.score,
    explanation: index === 0 && apiResponse.data!.explanation ? 
      apiResponse.data!.explanation : 
      `Alternative suggestion (${s.confidence} confidence)`,
    isRecommended: index === 0,
    votes: 0, // Not provided by API, could be enhanced
    avgSimilarity: 0 // Not provided by API, could be enhanced
  }));
}

/**
 * Merge client-side blending with API results for enhanced suggestions.
 * 
 * @param apiNeighbors Raw k-NN neighbors from API
 * @param supplierVat Current supplier VAT for boost
 * @param supplierName Supplier name for explanations
 * @returns Enhanced UI suggestions
 */
export function enhanceSuggestionsWithBlending(
  apiNeighbors: APINeighbor[],
  supplierVat?: string,
  _supplierName?: string
): UISuggestion[] {
  // Convert API neighbors to internal format
  const neighbors = apiNeighbors.map((n: APINeighbor) => ({
    ...(n.coding_event_id !== undefined && { codingEventId: n.coding_event_id }),
    accountId: n.account_id,
    vatCodeId: n.vat_code_id,
    sim: n.similarity || n.sim || 0,
    ...(n.confirmed_at !== undefined && { confirmedAt: n.confirmed_at }),
    ...(n.supplier_vat !== undefined && { supplierVat: n.supplier_vat })
  }));

  // Apply client-side blending
  const blended = blendNeighbors(neighbors, {
    supplierVat: supplierVat || null,
    supplierBoost: 1.2, // Slightly higher boost for client-side
    tau: 0.12, // Slightly sharper for better discrimination
    halfLifeDays: 120 // Shorter half-life for more recency bias
  });

  // Convert to UI format
  return blended.map((s, index) => {
    const supplierMatch = neighbors.some(n => 
      n.accountId === s.accountId && 
      n.vatCodeId === s.vatCodeId &&
      n.supplierVat === supplierVat
    );

    return {
      accountId: s.accountId,
      vatCodeId: s.vatCodeId,
      confidence: scoreToConfidence(s.score),
      confidenceScore: s.score,
      explanation: generateExplanation(s, _supplierName, supplierMatch),
      isRecommended: index === 0,
      votes: s.votes,
      avgSimilarity: s.avgSim
    };
  });
}

/**
 * Calculate suggestion accuracy metrics for monitoring.
 * 
 * @param suggestions Array of suggestions
 * @param selectedAccountId User's actual selection
 * @param selectedVatCodeId User's actual VAT selection
 * @returns Accuracy metrics
 */
export function calculateAccuracyMetrics(
  suggestions: UISuggestion[],
  selectedAccountId: number,
  selectedVatCodeId: number | null
): {
  topSuggestionMatch: boolean;
  suggestionRank: number;
  topConfidenceScore: number;
  totalSuggestions: number;
} {
  const topSuggestion = suggestions[0];
  const topMatch = topSuggestion?.accountId === selectedAccountId && 
                  topSuggestion?.vatCodeId === selectedVatCodeId;

  // Find rank of selected suggestion (1-indexed, 0 if not found)
  const matchRank = suggestions.findIndex(s => 
    s.accountId === selectedAccountId && s.vatCodeId === selectedVatCodeId
  );

  return {
    topSuggestionMatch: topMatch,
    suggestionRank: matchRank >= 0 ? matchRank + 1 : 0,
    topConfidenceScore: topSuggestion?.confidenceScore || 0,
    totalSuggestions: suggestions.length
  };
}