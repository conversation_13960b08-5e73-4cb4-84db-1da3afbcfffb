/**
 * AI Suggestion Blending Utilities
 * 
 * Provides functions to blend k-NN neighbors into ranked suggestions using softmax,
 * recency decay, and supplier matching boosts for intelligent account/VAT code recommendations.
 */

// Types for k-NN neighbors and suggestions
export interface Neighbor {
  codingEventId?: number;
  accountId: number;
  vatCodeId: number | null;
  sim: number;                   // cosine similarity in [0,1]
  confirmedAt?: string | null;   // ISO timestamp
  supplierVat?: string | null;
}

export interface Suggestion {
  accountId: number;
  vatCodeId: number | null;
  score: number;          // softmax probability in [0,1]
  avgSim: number;         // average similarity of neighbors in this bucket
  votes: number;          // number of neighbors in this bucket
  topExampleIds: number[];// up to 3 coding_event_ids (if provided)
}

export interface BlendOptions {
  tau?: number;                 // softmax temperature (lower = sharper)
  halfLifeDays?: number;        // recency half-life in days
  supplierVat?: string | null;  // current doc supplier VAT (optional)
  supplierBoost?: number;       // multiplicative boost if supplier matches
}

/**
 * Blend k-NN neighbors into ranked suggestions using softmax scoring.
 * 
 * Algorithm:
 * 1. Apply softmax over similarity scores (with temperature tau)
 * 2. Apply recency decay (exponential with configurable half-life)
 * 3. Apply supplier boost for matching supplier VAT
 * 4. Group by (account_id, vat_code_id) and sum weights
 * 5. Normalize to probability distribution
 * 6. Sort by score descending
 * 
 * @param neighbors Array of k-NN neighbors from similarity search
 * @param opts Blending options (temperature, recency, supplier boost)
 * @returns Array of ranked suggestions with scores and metadata
 */
export function blendNeighbors(
  neighbors: Neighbor[],
  opts?: BlendOptions
): Suggestion[] {
  if (!neighbors.length) return [];
  
  const tau = opts?.tau ?? 0.15;
  const halfLife = opts?.halfLifeDays ?? 180;
  const suppVat = opts?.supplierVat ?? null;
  const suppBoost = opts?.supplierBoost ?? 1.15;

  // Numeric stability: center around the max similarity
  const sMax = Math.max(...neighbors.map(n => n.sim), 0);

  const weighted = neighbors.map(n => {
    const ageDays = n.confirmedAt ? Math.max(0,
      (Date.now() - new Date(n.confirmedAt).getTime()) / 86_400_000
    ) : 0;

    // Recency decay: weight halves every halfLife days
    const recency = Math.exp(Math.log(0.5) * (ageDays / Math.max(halfLife, 1e-9)));

    // Softmax over similarity (temperature tau)
    const simTerm = Math.exp((n.sim - sMax) / Math.max(tau, 1e-9));

    // Optional supplier boost
    const boost = (suppVat && n.supplierVat && n.supplierVat === suppVat) ? suppBoost : 1.0;

    const w = simTerm * recency * boost;
    const key = `${n.accountId}:${n.vatCodeId ?? ''}`;
    return { key, w, ...n };
  });

  // Group by (accountId, vatCodeId)
  const groups = new Map<string, {
    accountId: number; 
    vatCodeId: number|null; 
    sumW: number; 
    votes: number; 
    simSum: number; 
    ex: number[]
  }>();
  
  for (const r of weighted) {
    let g = groups.get(r.key);
    if (!g) {
      g = { 
        accountId: r.accountId, 
        vatCodeId: r.vatCodeId ?? null, 
        sumW: 0, 
        votes: 0, 
        simSum: 0, 
        ex: [] 
      };
      groups.set(r.key, g);
    }
    g.sumW += r.w;
    g.votes += 1;
    g.simSum += r.sim;
    if (r.codingEventId && g.ex.length < 3) g.ex.push(r.codingEventId);
  }

  // Normalize weights to probability distribution
  const Z = Array.from(groups.values()).reduce((acc, g) => acc + g.sumW, 0) || 1e-12;

  const suggestions: Suggestion[] = Array.from(groups.values()).map(g => ({
    accountId: g.accountId,
    vatCodeId: g.vatCodeId,
    score: g.sumW / Z,
    avgSim: g.simSum / g.votes,
    votes: g.votes,
    topExampleIds: g.ex
  }));

  // Sort by score descending, then by average similarity
  suggestions.sort((a, b) => (b.score - a.score) || (b.avgSim - a.avgSim));
  
  return suggestions;
}

/**
 * Generate human-readable explanation for a suggestion.
 * 
 * @param suggestion The suggestion to explain
 * @param supplierName Optional supplier name for context
 * @param supplierMatch Whether this suggestion has supplier-specific examples
 * @returns Human-readable explanation string
 */
export function generateExplanation(
  suggestion: Suggestion,
  supplierName?: string,
  supplierMatch?: boolean
): string {
  const confidenceLevel = suggestion.score > 0.6 ? "High" : 
                         suggestion.score > 0.3 ? "Medium" : "Low";
  
  let explanation = `Based on ${suggestion.votes} similar invoice${suggestion.votes > 1 ? 's' : ''}`;
  
  if (supplierMatch && supplierName) {
    explanation += ` from ${supplierName}`;
  }
  
  explanation += ` (confidence: ${confidenceLevel})`;
  
  return explanation;
}

/**
 * Convert raw similarity score to confidence level.
 * 
 * @param score Blended score from 0 to 1
 * @returns Confidence level as string
 */
export function scoreToConfidence(score: number): 'high' | 'medium' | 'low' {
  if (score >= 0.6) return 'high';
  if (score >= 0.3) return 'medium';
  return 'low';
}

/**
 * Validate neighbor data structure.
 * 
 * @param neighbor Neighbor object to validate
 * @returns True if valid, false otherwise
 */
export function isValidNeighbor(neighbor: unknown): neighbor is Neighbor {
  if (typeof neighbor !== 'object' || neighbor === null) {
    return false;
  }
  
  const n = neighbor as Record<string, unknown>;
  
  return (
    'accountId' in n &&
    typeof n.accountId === 'number' &&
    'vatCodeId' in n &&
    (n.vatCodeId === null || typeof n.vatCodeId === 'number') &&
    'sim' in n &&
    typeof n.sim === 'number' &&
    n.sim >= 0 && n.sim <= 1 &&
    (!('confirmedAt' in n) || n.confirmedAt === null || n.confirmedAt === undefined || typeof n.confirmedAt === 'string') &&
    (!('supplierVat' in n) || n.supplierVat === null || n.supplierVat === undefined || typeof n.supplierVat === 'string')
  );
}

/**
 * Filter and validate neighbors before blending.
 * 
 * @param neighbors Raw neighbor array 
 * @param minSimilarity Minimum similarity threshold (default: 0.1)
 * @returns Filtered and validated neighbors
 */
export function prepareNeighbors(
  neighbors: unknown[], 
  minSimilarity: number = 0.1
): Neighbor[] {
  return neighbors
    .filter((n): n is Neighbor => isValidNeighbor(n) && n.sim >= minSimilarity)
    .sort((a, b) => b.sim - a.sim); // Sort by similarity descending
}

/**
 * Create a summary of blending results for logging/debugging.
 * 
 * @param neighbors Input neighbors
 * @param suggestions Output suggestions  
 * @param opts Blending options used
 * @returns Summary object
 */
export function createBlendingSummary(
  neighbors: Neighbor[],
  suggestions: Suggestion[],
  opts?: BlendOptions
): {
  inputCount: number;
  outputCount: number;
  topScore: number;
  avgSimilarity: number;
  supplierMatches: number;
  options: BlendOptions;
} {
  const supplierMatches = neighbors.filter(n => 
    opts?.supplierVat && n.supplierVat === opts.supplierVat
  ).length;
  
  const avgSim = neighbors.length > 0 ? 
    neighbors.reduce((sum, n) => sum + n.sim, 0) / neighbors.length : 0;
  
  return {
    inputCount: neighbors.length,
    outputCount: suggestions.length,
    topScore: suggestions.length > 0 ? suggestions[0].score : 0,
    avgSimilarity: avgSim,
    supplierMatches,
    options: {
      tau: opts?.tau ?? 0.15,
      halfLifeDays: opts?.halfLifeDays ?? 180,
      supplierVat: opts?.supplierVat ?? null,
      supplierBoost: opts?.supplierBoost ?? 1.15
    }
  };
}