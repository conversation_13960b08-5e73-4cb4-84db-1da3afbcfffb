import { describe, it, expect } from 'vitest';
import { 
  blendNeighbors, 
  generateExplanation,
  scoreToConfidence,
  isValidNeighbor,
  prepareNeighbors,
  createBlendingSummary,
  type Neighbor 
} from './blend.js';

describe('blendNeighbors', () => {
  const mockNeighbors: Neighbor[] = [
    {
      codingEventId: 1,
      accountId: 6000,
      vatCodeId: 1,
      sim: 0.9,
      confirmedAt: '2024-01-01T00:00:00Z',
      supplierVat: 'BE123456789'
    },
    {
      codingEventId: 2,
      accountId: 6000,
      vatCodeId: 1,
      sim: 0.8,
      confirmedAt: '2024-01-15T00:00:00Z',
      supplierVat: 'BE123456789'
    },
    {
      codingEventId: 3,
      accountId: 6132,
      vatCodeId: 1,
      sim: 0.7,
      confirmedAt: '2023-06-01T00:00:00Z',
      supplierVat: 'BE987654321'
    }
  ];

  it('should return empty array for empty neighbors', () => {
    const result = blendNeighbors([]);
    expect(result).toEqual([]);
  });

  it('should blend neighbors and return sorted suggestions', () => {
    const result = blendNeighbors(mockNeighbors);
    
    expect(result).toHaveLength(2); // 2 unique (account, vat) combinations
    expect(result[0].accountId).toBe(6000); // Higher similarity group should be first
    expect(result[0].votes).toBe(2); // Two neighbors for account 6000
    expect(result[1].accountId).toBe(6132);
    expect(result[1].votes).toBe(1);
  });

  it('should apply supplier boost when supplier VAT matches', () => {
    const withBoost = blendNeighbors(mockNeighbors, { supplierVat: 'BE123456789' });
    const withoutBoost = blendNeighbors(mockNeighbors, { supplierVat: 'DIFFERENT' });
    
    expect(withBoost[0].score).toBeGreaterThan(withoutBoost[0].score);
  });

  it('should apply recency decay', () => {
    const recentNeighbors: Neighbor[] = [
      { accountId: 6000, vatCodeId: 1, sim: 0.8, confirmedAt: new Date().toISOString() },
      { accountId: 6132, vatCodeId: 1, sim: 0.8, confirmedAt: '2020-01-01T00:00:00Z' }
    ];
    
    const result = blendNeighbors(recentNeighbors);
    expect(result[0].accountId).toBe(6000); // Recent should win despite same similarity
  });

  it('should handle null VAT codes', () => {
    const neighborsWithNullVat: Neighbor[] = [
      { accountId: 6000, vatCodeId: null, sim: 0.9 },
      { accountId: 6000, vatCodeId: 1, sim: 0.8 }
    ];
    
    const result = blendNeighbors(neighborsWithNullVat);
    expect(result).toHaveLength(2); // Different VAT codes = different groups
  });

  it('should limit top examples to 3', () => {
    const manyNeighbors: Neighbor[] = Array.from({ length: 5 }, (_, i) => ({
      codingEventId: i + 1,
      accountId: 6000,
      vatCodeId: 1,
      sim: 0.9 - (i * 0.1)
    }));
    
    const result = blendNeighbors(manyNeighbors);
    expect(result[0].topExampleIds).toHaveLength(3);
  });

  it('should be numerically stable with extreme similarities', () => {
    const extremeNeighbors: Neighbor[] = [
      { accountId: 6000, vatCodeId: 1, sim: 1.0 },
      { accountId: 6132, vatCodeId: 1, sim: 0.001 }
    ];
    
    const result = blendNeighbors(extremeNeighbors);
    expect(result).toHaveLength(2);
    expect(result[0].score + result[1].score).toBeCloseTo(1, 5); // Should sum to 1
  });

  it('should respect temperature parameter', () => {
    const lowTemp = blendNeighbors(mockNeighbors, { tau: 0.05 });
    const highTemp = blendNeighbors(mockNeighbors, { tau: 0.5 });
    
    // Lower temperature should create more extreme scores (higher top, lower bottom)
    expect(lowTemp[0].score).toBeGreaterThan(highTemp[0].score);
  });
});

describe('generateExplanation', () => {
  it('should generate basic explanation', () => {
    const suggestion = { 
      accountId: 6000, 
      vatCodeId: 1, 
      score: 0.8, 
      avgSim: 0.7, 
      votes: 3,
      topExampleIds: [1, 2, 3]
    };
    
    const explanation = generateExplanation(suggestion);
    expect(explanation).toContain('Based on 3 similar invoices');
    expect(explanation).toContain('confidence: High');
  });

  it('should include supplier name when provided', () => {
    const suggestion = { 
      accountId: 6000, 
      vatCodeId: 1, 
      score: 0.5, 
      avgSim: 0.6, 
      votes: 2,
      topExampleIds: [1, 2]
    };
    
    const explanation = generateExplanation(suggestion, 'ACME Corp', true);
    expect(explanation).toContain('from ACME Corp');
  });

  it('should handle singular vs plural invoices', () => {
    const single = { 
      accountId: 6000, 
      vatCodeId: 1, 
      score: 0.7, 
      avgSim: 0.6, 
      votes: 1,
      topExampleIds: [1]
    };
    
    const explanation = generateExplanation(single);
    expect(explanation).toContain('1 similar invoice'); // Singular
  });
});

describe('scoreToConfidence', () => {
  it('should map scores to confidence levels', () => {
    expect(scoreToConfidence(0.8)).toBe('high');
    expect(scoreToConfidence(0.5)).toBe('medium');
    expect(scoreToConfidence(0.2)).toBe('low');
    expect(scoreToConfidence(0.6)).toBe('high'); // Boundary case
    expect(scoreToConfidence(0.3)).toBe('medium'); // Boundary case
  });
});

describe('isValidNeighbor', () => {
  it('should validate correct neighbor structure', () => {
    const validNeighbor = {
      accountId: 6000,
      vatCodeId: 1,
      sim: 0.8,
      confirmedAt: '2024-01-01T00:00:00Z',
      supplierVat: 'BE123456789'
    };
    
    expect(isValidNeighbor(validNeighbor)).toBe(true);
  });

  it('should handle null optional fields', () => {
    const neighbor = {
      accountId: 6000,
      vatCodeId: null,
      sim: 0.8,
      confirmedAt: null,
      supplierVat: null
    };
    
    expect(isValidNeighbor(neighbor)).toBe(true);
  });

  it('should reject invalid structures', () => {
    expect(isValidNeighbor({})).toBe(false);
    expect(isValidNeighbor({ accountId: 'invalid' })).toBe(false);
    expect(isValidNeighbor({ accountId: 6000, sim: -1 })).toBe(false); // Invalid similarity
    expect(isValidNeighbor({ accountId: 6000, sim: 1.5 })).toBe(false); // Invalid similarity
  });
});

describe('prepareNeighbors', () => {
  it('should filter and sort neighbors', () => {
    const rawNeighbors = [
      { accountId: 6000, vatCodeId: 1, sim: 0.05 }, // Below threshold
      { accountId: 'invalid', vatCodeId: 1, sim: 0.8 }, // Invalid
      { accountId: 6132, vatCodeId: 1, sim: 0.7 },
      { accountId: 6000, vatCodeId: 1, sim: 0.9 }
    ];
    
    const prepared = prepareNeighbors(rawNeighbors, 0.1);
    expect(prepared).toHaveLength(2); // Only valid above-threshold neighbors
    expect(prepared[0].sim).toBe(0.9); // Sorted by similarity descending
    expect(prepared[1].sim).toBe(0.7);
  });
});

describe('createBlendingSummary', () => {
  it('should create comprehensive summary', () => {
    const neighbors: Neighbor[] = [
      { accountId: 6000, vatCodeId: 1, sim: 0.9, supplierVat: 'BE123456789' },
      { accountId: 6000, vatCodeId: 1, sim: 0.8, supplierVat: 'BE123456789' },
      { accountId: 6132, vatCodeId: 1, sim: 0.7, supplierVat: 'BE987654321' }
    ];
    
    const suggestions = blendNeighbors(neighbors);
    const options = { supplierVat: 'BE123456789' };
    const summary = createBlendingSummary(neighbors, suggestions, options);
    
    expect(summary.inputCount).toBe(3);
    expect(summary.outputCount).toBe(2);
    expect(summary.supplierMatches).toBe(2);
    expect(summary.avgSimilarity).toBeCloseTo(0.8, 1);
    expect(summary.topScore).toBeGreaterThan(0);
    expect(summary.options.supplierVat).toBe('BE123456789');
  });
});

describe('integration tests', () => {
  it('should handle real-world scenario', () => {
    // Simulate real API response neighbors
    const apiNeighbors: Neighbor[] = [
      { 
        codingEventId: 101,
        accountId: 6000, 
        vatCodeId: 21, 
        sim: 0.94, 
        confirmedAt: '2024-06-02T10:00:00Z', 
        supplierVat: 'BE0123456789' 
      },
      { 
        codingEventId: 102,
        accountId: 6000, 
        vatCodeId: 21, 
        sim: 0.90, 
        confirmedAt: '2024-04-10T10:00:00Z', 
        supplierVat: 'BE0123456789' 
      },
      { 
        codingEventId: 205,
        accountId: 6132, 
        vatCodeId: 21, 
        sim: 0.82, 
        confirmedAt: '2024-12-01T10:00:00Z', 
        supplierVat: 'BE0999999999' 
      }
    ];
    
    const blended = blendNeighbors(apiNeighbors, {
      tau: 0.15,
      halfLifeDays: 180,
      supplierVat: 'BE0123456789',
      supplierBoost: 1.15
    });
    
    expect(blended).toHaveLength(2);
    expect(blended[0].accountId).toBe(6000); // Should win due to supplier boost + higher sim
    expect(blended[0].votes).toBe(2);
    expect(blended[0].topExampleIds).toContain(101);
    expect(blended[0].topExampleIds).toContain(102);
    
    // Create explanation
    const explanation = generateExplanation(blended[0], 'ACME Corp', true);
    expect(explanation).toContain('ACME Corp');
    expect(explanation).toContain('confidence: High');
  });

  it('should handle edge case: no high-similarity neighbors', () => {
    const lowSimNeighbors: Neighbor[] = [
      { accountId: 6000, vatCodeId: 1, sim: 0.3 },
      { accountId: 6132, vatCodeId: 1, sim: 0.2 },
      { accountId: 6200, vatCodeId: 2, sim: 0.25 }
    ];
    
    const blended = blendNeighbors(lowSimNeighbors);
    expect(blended).toHaveLength(3);
    expect(scoreToConfidence(blended[0].score)).toBe('medium'); // Top gets medium confidence after softmax normalization
  });

  it('should handle identical similarities with recency tie-breaking', () => {
    const identicalSim: Neighbor[] = [
      { 
        accountId: 6000, 
        vatCodeId: 1, 
        sim: 0.8, 
        confirmedAt: '2024-01-01T00:00:00Z' 
      },
      { 
        accountId: 6132, 
        vatCodeId: 1, 
        sim: 0.8, 
        confirmedAt: new Date().toISOString() // Very recent
      }
    ];
    
    const blended = blendNeighbors(identicalSim);
    expect(blended[0].accountId).toBe(6132); // Recent should win
  });
});