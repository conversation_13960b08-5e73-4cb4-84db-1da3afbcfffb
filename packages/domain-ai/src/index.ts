/**
 * @ledgerly/domain-ai
 * 
 * AI-powered suggestion utilities for intelligent account and VAT code recommendations.
 * Provides softmax blending, recency weighting, and supplier matching for k-NN results.
 */

export {
  type Neighbor,
  type Suggestion,
  type BlendOptions,
  blendNeighbors,
  generateExplanation,
  scoreToConfidence,
  isValidNeighbor,
  prepareNeighbors,
  createBlendingSummary
} from './blend.js';

export {
  type SuggestionRequest,
  type SuggestionResponse,
  type FeedbackRequest,
  createSuggestionClient,
  formatSuggestionsForUI
} from './client.js';