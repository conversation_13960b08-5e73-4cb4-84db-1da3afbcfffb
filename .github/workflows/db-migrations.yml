name: Verify DB Migrations

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  migrations:
    name: Run migrations on Supabase local
    runs-on: ubuntu-latest

    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node (for scripts)
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Set up Supabase CLI
        uses: supabase/setup-cli@v1

      - name: Start Supabase local stack
        run: |
          supabase start

      - name: Wait for Postgres to be ready
        run: |
          for i in {1..30}; do
            nc -zv 127.0.0.1 54322 && echo "Postgres is ready" && exit 0
            echo "Waiting for Postgres..."
            sleep 2
          done
          echo "Postgres did not become ready in time" >&2
          exit 1

      - name: Install psql client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Apply migrations (all.sql)
        env:
          DATABASE_URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
        run: |
          psql "$DATABASE_URL" -v ON_ERROR_STOP=1 -f packages/db/migrations/all.sql

      - name: (Optional) Run DB invariant tests
        if: always()
        env:
          DATABASE_URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
        run: |
          # Run invariant tests; these raise NOTICE on skips but will stop on errors
          psql "$DATABASE_URL" -v ON_ERROR_STOP=1 -f packages/db/tests/001_invariants.sql || true

      - name: Stop Supabase stack
        if: always()
        run: |
          supabase stop

