# Contributing to Ledgerly

## Development Setup

### Prerequisites
- Node.js 18+ 
- pnpm package manager
- PostgreSQL with Supabase instance
- Docker (for local development database)

### Getting Started

1. Clone the repository and install dependencies:
```bash
git clone <repository-url>
cd ledgerly
pnpm install
```

2. Set up environment variables by copying `.env.example` files in each package/app.

3. Start the development database:
```bash
# Start Supabase locally (optional)
supabase start
```

## Project Structure

This is a monorepo using pnpm workspaces with the following structure:

```
belbooks/
├── apps/
│   ├── web/              # Next.js frontend application
│   └── bff/              # Fastify Backend-for-Frontend API
├── packages/
│   ├── types/            # Shared TypeScript types
│   ├── dal/              # Data Access Layer (Supabase client wrappers)
│   ├── db/               # Database migrations and tests
│   ├── domain-*/         # Domain-specific business logic
│   └── import-service/   # File import processing
```

## Development Workflow

### Running the Development Environment

```bash
# Install dependencies
pnpm install

# Start all development servers
pnpm dev

# Or start individual services:
pnpm --filter @belbooks/web dev      # Frontend (http://localhost:3000)
pnpm --filter @belbooks/bff dev      # BFF API (http://localhost:3001)
```

### Code Quality

We enforce strict code quality standards:

```bash
# Type checking (must pass with zero errors)
pnpm typecheck

# Linting (must pass with zero errors) 
pnpm lint

# Run all tests
pnpm test
```

### Testing

#### Database Tests
Database functionality is tested using pgTAP:

```bash
# Run database tests
cd packages/db
psql -d <test-database> -f tests/010_vat_core.sql
```

#### Application Tests
- **BFF Tests**: Jest with ts-jest for API endpoint testing
- **Web Tests**: Next.js built-in test runner (when configured)
- **DAL Tests**: Jest for data access layer functions

```bash
# Run BFF tests
pnpm --filter @ledgerly/bff test

# Run DAL tests  
pnpm --filter @ledgerly/dal test
```

## Type Safety Standards

### No `any` Types Policy
- All new code MUST be fully typed
- Use overlay types for external APIs until proper types are generated
- See `packages/types/src/vat.ts` for overlay type pattern
- Use typed wrappers in DAL for Supabase RPC functions

### Type Pattern Example
```typescript
// ✅ Good - Use overlay types
export interface VATPreviewRow {
  grid_code: string
  direction: 'input' | 'output'
  base_total: number
  vat_total: number
}

// ✅ Good - Typed wrapper functions
export async function rpcVatPreview(
  client: SupabaseClient<Database>,
  params: VATPreviewParams
): Promise<VATPreviewResponse>

// ❌ Bad - Avoid any types
const { data } = await (supabase as any).rpc('some_function')
```

## Database Development

### Migrations
- Place migrations in `packages/db/migrations/`
- Use sequential numbering: `0001_initial.sql`, `0002_add_feature.sql`
- Include both UP and DOWN migration paths
- Add corresponding pgTAP tests

### Row Level Security (RLS)
- All tables must have RLS enabled
- Use entity/tenant-based isolation patterns
- Test RLS policies thoroughly in pgTAP tests

Example RLS pattern:
```sql
-- Enable RLS
ALTER TABLE some_table ENABLE ROW LEVEL SECURITY;

-- Entity-based policy
CREATE POLICY "Entity members can access their data" ON some_table
  FOR ALL TO authenticated
  USING (entity_id IN (
    SELECT entity_id FROM user_entity_access WHERE user_id = auth.uid()
  ));
```

## API Development

### BFF API Patterns
- Use Fastify with TypeScript
- Implement authentication middleware
- Use Zod for request validation
- Include comprehensive error handling
- Add feature flag checks where applicable

Example route pattern:
```typescript
fastify.get('/entities/:id/resource', async (request, reply) => {
  const { id: entityId } = EntityParamsSchema.parse(request.params)
  const supabaseClient = request.userSupabase || fastify.supabase
  
  // Feature flag check if needed
  const isEnabled = await checkFeatureEnabled(supabaseClient, entityId)
  if (!isEnabled) {
    return reply.code(403).send({ error: 'Feature not enabled' })
  }
  
  // Use typed DAL functions
  const data = await someTypedFunction(supabaseClient, { entityId })
  return reply.send({ success: true, data })
})
```

### Web API Routes (Next.js)
- Use App Router API routes
- Implement proper authentication
- Use typed DAL functions instead of raw Supabase calls
- Handle errors consistently

## Feature Flag Pattern

For new features, implement feature flags in the `operating_modes` table:

```typescript
// Check feature flag
export async function checkFeatureEnabled(
  client: SupabaseClient<Database>,
  entityId: number
): Promise<boolean> {
  const { data } = await client
    .from('operating_modes')
    .select('config')
    .eq('entity_id', entityId)
    .single()
  
  return data?.config?.FeatureEnabled === true
}
```

## Quality Roadmap & PR Guidance

Please read `docs/quality-roadmap.md` before opening PRs. Align changes to its phases and day‑to‑day guidance:

- New SQL: Place under `packages/db/migrations`; lint is strict for new files. Do not churn legacy migrations/seeds just for formatting.
- Python workers: Keep Black + Ruff clean; add type hints; prefer Pydantic v2 `@field_validator`/`@field_serializer`; avoid `Any`.
- Celery tasks: Use a canonical `TaskResult` shape; ensure `update_state` is called with correct signature and payloads.
- FastAPI tests: Use `httpx.ASGITransport` for async tests; avoid real infra; mock DB/Celery cleanly.
- CI/tooling: Use Corepack (`corepack prepare pnpm@9 --activate`); Python dev deps via `uv sync --extra dev`.
- Security: Keep CORS broad in dev only; include security headers; no secrets in repo—use `.env.local`.

When touching legacy areas, isolate relaxations via scoped configs and keep new/changed code strict.

## Code Review Guidelines

### Gate Requirements for Merge
All PRs must meet these requirements:

1. **Zero TypeScript Errors**: `pnpm typecheck` passes
2. **Zero Lint Errors**: `pnpm lint` passes  
3. **All Tests Pass**: `pnpm test` passes
4. **No `any` Types**: Code review checks for type safety
5. **Documentation Updated**: CONTRIBUTING.md and relevant docs updated

### Review Checklist
- [ ] Type safety maintained (no `any` types in source)
- [ ] Tests written and passing
- [ ] Error handling implemented
- [ ] RLS policies tested for security
- [ ] Feature flags implemented correctly
- [ ] Documentation updated

## Common Patterns

### Error Handling
```typescript
// Custom error types
interface CustomError extends Error {
  code?: string
  details?: unknown
}

// Error handling in routes
catch (error) {
  if (error instanceof Error && 'code' in error) {
    const customError = error as CustomError
    return reply.code(400).send({ error: customError.message })
  }
  return reply.code(500).send({ error: 'Internal server error' })
}
```

### Date Validation
```typescript
const DATE_REGEX = /^\d{4}-\d{2}-\d{2}$/
export function isValidDateFormat(date: string): boolean {
  return DATE_REGEX.test(date)
}
```

## Getting Help

- Review existing patterns in the codebase
- Check test files for usage examples
- Refer to package documentation in respective README files
- Ask questions in team channels

## Release Process

1. Ensure all gate requirements pass
2. Update version numbers if needed
3. Create pull request with comprehensive description
4. Get code review approval
5. Merge to develop branch
6. Deploy to staging for integration testing
7. Merge to main for production deployment
