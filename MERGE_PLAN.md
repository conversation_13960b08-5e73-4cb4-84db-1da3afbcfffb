# 🚀 Track B & D Merge Plan: feat/track-b-clean → main

## 📊 **Executive Summary**

**Scope**: Merge massive implementation containing both Track B (inbox E2E) and Track D (bank statement flow)
**Impact**: 154 files changed, 23,148 insertions, 1,018 deletions
**Risk Level**: HIGH - Major database schema changes, new infrastructure, comprehensive auth system
**Estimated Timeline**: 3-5 days for complete validation and deployment

## 🎯 **What's Being Merged**

### **Track B: Inbox E2E Document Processing**
- Complete document upload, OCR, AI extraction pipeline
- Python workers with Celery task queue
- New `inbox_documents` table with RLS policies
- File hash deduplication (SHA256)
- Entity operating mode detection (ledger vs assist)
- Comprehensive Zod/Pydantic type contracts

### **Track D: Bank Statement Flow Enhancements**
- Enhanced `bank_transactions` table (batch_id, dedupe_hash, currency, etc.)
- New `bank_tx_links` table for reconciliation
- 5-state transaction workflow (unmatched→proposed→matched→reconciled→failed)
- Idempotent imports with audit trails

### **Infrastructure & Security**
- Rate limiting, CSRF protection, session management
- 11 new test suites with 634+ test cases
- Python workers infrastructure
- Comprehensive authentication system

## ⚠️ **Critical Risk Assessment**

### **HIGH RISK AREAS**
1. **Database Migrations**: 2 new migrations (0006_inbox.sql, 0007_bank_track_d.sql)
2. **Python Workers**: New Celery infrastructure dependency
3. **Authentication Changes**: Session security, CSRF protection
4. **Type System**: Major changes to contracts.ts and supabase.ts

### **BREAKING CHANGE POTENTIAL**
- ✅ **Low Risk**: All changes appear additive
- ✅ **Backward Compatible**: No existing API endpoints modified
- ⚠️ **Migration Risk**: New tables and columns only
- ⚠️ **Dependency Risk**: New Python packages and Celery workers

## 📋 **Phase 1: Pre-merge Validation**

### **1.1 Code Quality Validation**
```bash
# Run all linting and type checks
pnpm lint
pnpm type-check
pnpm test

# Validate Python workers
cd apps/workers-py
uv run pytest
```

### **1.2 Migration Validation**
```bash
# Test migrations on clean database
supabase db reset
supabase db push

# Verify migration order and dependencies
psql -f packages/db/migrations/all.sql
```

### **1.3 Dependency Analysis**
```bash
# Check for new dependencies
git diff main..feat/track-b-clean -- package.json pnpm-lock.yaml
git diff main..feat/track-b-clean -- apps/workers-py/pyproject.toml
```

## 📋 **Phase 2: Staging Environment Preparation**

### **2.1 Staging Database Backup**
```bash
# Create backup before migration
bin/backup-staging-db
```

### **2.2 Apply Migrations to Staging**
```bash
# Apply migrations with validation
bin/apply-staging-migrations

# Generate updated types
bin/generate-staging-types

# Verify migration success
bin/test-staging
```

### **2.3 Deploy to Staging**
```bash
# Deploy complete application
bin/deploy-staging

# Verify all services are running
bin/health-check-staging
```

## 📋 **Phase 3: Comprehensive Feature Testing**

### **3.1 Track B (Inbox E2E) Testing**
- [ ] Document upload functionality
- [ ] OCR processing pipeline
- [ ] AI extraction accuracy
- [ ] File deduplication (SHA256)
- [ ] Entity mode detection
- [ ] Status workflow (uploaded→extracted→suggested→confirmed→posted/exported)
- [ ] RLS policy enforcement

### **3.2 Track D (Bank Transactions) Testing**
- [ ] Enhanced bank transaction import
- [ ] Deduplication logic
- [ ] Status workflow (unmatched→proposed→matched→reconciled→failed)
- [ ] Bank transaction linking
- [ ] Reconciliation features
- [ ] Audit trail functionality

### **3.3 Security & Infrastructure Testing**
- [ ] Rate limiting functionality
- [ ] CSRF protection
- [ ] Session security management
- [ ] Python workers processing
- [ ] Celery task queue
- [ ] Authentication flows

## 📋 **Phase 4: Production Merge Strategy**

### **4.1 Pre-merge Checklist**
- [ ] All staging tests pass
- [ ] Performance benchmarks acceptable
- [ ] Security audit complete
- [ ] Rollback procedures documented
- [ ] Team notification sent

### **4.2 Merge Execution**
```bash
# Create pull request
git checkout main
git pull origin main
git checkout feat/track-b-clean
git rebase main  # Resolve any conflicts

# Create PR with comprehensive description
gh pr create --title "feat: Complete Track B (inbox E2E) and Track D (bank statement flow)" \
  --body-file PR_TEMPLATE.md
```

### **4.3 Feature Flag Strategy**
```env
# Gradual rollout flags
FEATURE_TRACK_B_INBOX=true
FEATURE_TRACK_D_BANK=true
FEATURE_PYTHON_WORKERS=true
```

## 📋 **Phase 5: Post-merge Monitoring**

### **5.1 Production Deployment**
```bash
# Apply production migrations
bin/apply-prod-migrations

# Deploy to production
bin/deploy-production

# Monitor deployment
bin/monitor-production
```

### **5.2 Validation & Monitoring**
- [ ] Database migration success
- [ ] All services healthy
- [ ] No error rate spikes
- [ ] Performance metrics stable
- [ ] User acceptance testing

## 🔄 **Rollback Procedures**

### **Emergency Rollback**
```bash
# Revert to previous deployment
bin/rollback-production

# Restore database if needed
bin/restore-production-backup
```

### **Partial Rollback**
```env
# Disable features via flags
FEATURE_TRACK_B_INBOX=false
FEATURE_TRACK_D_BANK=false
FEATURE_PYTHON_WORKERS=false
```

## 📞 **Communication Plan**

### **Stakeholder Notifications**
1. **Pre-merge**: Development team, QA team
2. **During merge**: Product team, DevOps team
3. **Post-merge**: All stakeholders, customer success

### **Documentation Updates**
- [ ] API documentation
- [ ] User guides
- [ ] Deployment procedures
- [ ] Troubleshooting guides

## ✅ **Success Criteria**

1. **All tests pass** in staging and production
2. **Zero downtime** during deployment
3. **No performance degradation** post-merge
4. **All features functional** as specified
5. **Security audit** passes
6. **User acceptance** confirmed

## 🎯 **Next Steps**

1. **Execute Phase 1**: Pre-merge validation
2. **Schedule staging deployment**: Coordinate with team
3. **Plan production window**: Low-traffic period
4. **Prepare rollback procedures**: Document and test
5. **Monitor and validate**: Comprehensive post-merge testing

---

**Merge Complexity**: HIGH
**Confidence Level**: MEDIUM (pending validation)
**Recommended Approach**: Phased deployment with comprehensive testing
