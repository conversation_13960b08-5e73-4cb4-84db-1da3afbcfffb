import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    // Global test configuration for memory optimization
    // Use process-based pool to avoid Node worker OOMs in CI
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true,
      },
    },
    testTimeout: 30000,
    hookTimeout: 15000,
    logHeapUsage: true,
    // Optimize memory usage
    maxConcurrency: 3,
    forceRerunTriggers: ['**/package.json/**', '**/vitest.config.*/**'],
  },
})
